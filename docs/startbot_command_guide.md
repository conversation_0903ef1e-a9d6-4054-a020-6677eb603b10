# 📋 Hướng dẫn sử dụng command `/startbot`

## 🎯 Tổng quan

Command `/startbot` đã được cập nhật với logic mới, sử dụng chung codebase với `/createbot` để tránh duplicate code và đảm bảo t<PERSON>h nhất quán.

## 📝 Cú pháp

```
/startbot <symbol> <amount> [dca_amount] [profile] [test]
```

### 📋 Tham số

| Tham số | Bắt buộc | Mô tả | Ví dụ |
|---------|----------|-------|-------|
| `symbol` | ✅ | Symbol để trade | `BTC`, `ETH`, `SOL`, `HYPER` |
| `amount` | ✅ | Số tiền trade (USD) | `50`, `100.5`, `250` |
| `dca_amount` | ❌ | Số tiền DCA (USD) | `25`, `50.5` (mặc định: 0) |
| `profile` | ❌ | Tên profile credentials | `main`, `test` (mặc định: profile đầu tiên) |
| `test` | ❌ | Chế độ test | `test`, `testmode` |

## 🚀 Ví dụ sử dụng

### 1. Cơ bản - Chỉ symbol và amount
```
/startbot BTC 50
```
- Symbol: BTC/USDT:USDT
- Amount: $50
- DCA: $0 (không DCA)
- Profile: Tự động chọn profile đầu tiên

### 2. Với DCA amount
```
/startbot ETH 100 30
```
- Symbol: ETH/USDT:USDT  
- Amount: $100
- DCA: $30
- Profile: Tự động chọn profile đầu tiên

### 3. Chỉ định profile cụ thể
```
/startbot SOL 75 25 main
```
- Symbol: SOL/USDT:USDT
- Amount: $75
- DCA: $25
- Profile: main

### 4. Chế độ test
```
/startbot HYPER 50 20 main test
```
- Symbol: HYPER/USDT:USDT
- Amount: $50
- DCA: $20
- Profile: main
- Test mode: Bật

## ⚙️ Logic xử lý

### 1. **Validation**
- ✅ Symbol phải hợp lệ (chỉ chứa ký tự an toàn)
- ✅ Amount phải là số dương
- ✅ DCA amount phải không âm (có thể 0)
- ✅ Profile phải tồn tại trong hệ thống

### 2. **Profile Resolution**
- Nếu không chỉ định profile → Tự động chọn profile đầu tiên
- Nếu chỉ định profile → Kiểm tra tồn tại và sử dụng
- Nếu không có profile nào → Báo lỗi

### 3. **Container Creation**
- Kiểm tra container đã tồn tại chưa
- Tạo config file từ template
- Tạo Docker container với đầy đủ environment variables
- Khởi động bot với cấu hình đã tạo

## 🔧 Tính năng mới

### ✅ **Shared Logic**
- Sử dụng `BotCreationService` chung với `/createbot`
- Không còn duplicate code
- Logic nhất quán giữa các commands

### ✅ **Better Error Handling**
- Validation chi tiết từng tham số
- Thông báo lỗi rõ ràng và hữu ích
- Rollback tự động khi có lỗi

### ✅ **Improved UX**
- Hiển thị processing message trong khi tạo bot
- Thông báo chi tiết kết quả
- Format đẹp với Markdown

### ✅ **Security**
- Validate tất cả inputs
- Sanitize parameters
- Audit logging cho Docker commands

## 🆚 So sánh với `/createbot`

| Tính năng | `/startbot` | `/createbot` |
|-----------|-------------|--------------|
| **Interface** | Command line | Interactive wizard |
| **Speed** | ⚡ Nhanh (1 command) | 🐌 Chậm (nhiều bước) |
| **Flexibility** | 🔧 Cơ bản | 🎛️ Đầy đủ options |
| **Use case** | Quick start | Detailed setup |
| **Logic** | ✅ Shared service | ✅ Shared service |

## 🎯 Khi nào sử dụng

### 👍 Sử dụng `/startbot` khi:
- Muốn tạo bot nhanh chóng
- Đã biết rõ parameters cần thiết
- Sử dụng cấu hình mặc định
- Automation/scripting

### 👍 Sử dụng `/createbot` khi:
- Lần đầu sử dụng
- Cần tùy chỉnh chi tiết
- Muốn UI thân thiện
- Không chắc chắn về parameters

## 🚨 Lưu ý quan trọng

### ⚠️ **Container Conflicts**
- Nếu container đã tồn tại → Sử dụng `/stop <symbol>` trước
- Mỗi symbol chỉ có thể có 1 container đang chạy

### ⚠️ **Credentials**
- Phải có ít nhất 1 profile credentials
- Sử dụng `/addcreds` để thêm credentials
- Profile phải có đầy đủ API key và secret

### ⚠️ **Resources**
- Đảm bảo đủ RAM và CPU cho container
- Kiểm tra Docker daemon đang chạy
- Có quyền truy cập Docker socket

## 🔍 Troubleshooting

### ❌ "No valid credentials profile found"
**Giải pháp:** Thêm credentials với `/addcreds`

### ❌ "Container already exists"
**Giải pháp:** Stop container cũ với `/stop <symbol>`

### ❌ "Invalid symbol"
**Giải pháp:** Sử dụng symbol hợp lệ (BTC, ETH, SOL, etc.)

### ❌ "Amount must be positive"
**Giải pháp:** Nhập số dương cho amount

## 📞 Hỗ trợ

- 📋 `/help` - Xem tất cả commands
- 📊 `/status <symbol>` - Kiểm tra trạng thái bot
- 📜 `/list` - Xem danh sách bots
- 🛑 `/stop <symbol>` - Dừng bot
