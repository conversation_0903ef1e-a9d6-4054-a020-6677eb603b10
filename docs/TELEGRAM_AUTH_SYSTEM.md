# 🔐 Telegram Bot Authorization System

## Overview

Hệ thống authorization cho Telegram bot với các tính năng bả<PERSON> mật cao, quản lý user linh hoạt và auto-promotion cho creator.

## ✨ Key Features

### 🛡️ Security Features
- **Role-based Access Control (RBAC)** với 4 levels: SUPER_ADMIN, ADMIN, USER, BLOCKED
- **Creator-only User Management** - Chỉ creator có thể CRUD users
- **Auto-promotion** - Tự động promote creator thành SUPER_ADMIN
- **Thread-safe Operations** - Đả<PERSON> bảo an toàn trong môi trường multi-threaded
- **Audit Logging** - Log tất cả authorization attempts
- **Default Deny Policy** - Từ chối tất cả users không được authorize

### 🎯 User Management
- **Flexible Input** - Hỗ trợ username hoặc user ID
- **Interactive Setup Wizard** - Setup dễ dàng qua command line
- **Config Management** - View và manage config qua CLI
- **Persistent Storage** - Lưu trữ trong `~/.autotrader/telegram_auth.json`

## 🏗️ Architecture

### File Structure
```
src/infrastructure/telegram/auth/
├── __init__.py                    # Package exports
├── telegram_auth_service.py       # Main authorization service
└── decorators.py                  # Authorization decorators
```

### Config Location
```
~/.autotrader/telegram_auth.json   # Authorization config file
```

## 🚀 Quick Start

### 1. Start Bot (Auto-Setup)
```bash
# Start Telegram bot - sẽ tự động check và setup auth nếu cần
./bot.sh telegram

# Nếu chưa setup auth, bot sẽ hỏi:
# "Bạn có muốn setup authorization ngay bây giờ? (y/N): y"
# Sau đó follow setup wizard
```

### 2. Manual Setup Authorization
```bash
# Interactive setup wizard (optional - có thể skip nếu dùng auto-setup)
./bot.sh setup-auth

# Nhập username hoặc user ID của super admin
# Ví dụ: hoangtrungdev hoặc 1631630468
```

### 3. View Configuration
```bash
# Xem current config
./bot.sh config-auth
```

## 🔒 Authorization Check System

### Auto-Check on Bot Start
- **Mandatory Check**: Bot sẽ tự động check authorization setup khi khởi động
- **Interactive Setup**: Nếu chưa setup, bot sẽ offer setup ngay lập tức
- **Config Validation**: Validate JSON structure và required fields
- **Error Recovery**: Tự động detect và offer fix cho corrupt config files

### Check Flow
```
./bot.sh telegram
    ↓
🔐 Check auth setup
    ↓
❌ Not setup? → Offer interactive setup → ✅ Setup complete → Continue
✅ Setup OK? → Validate config → ❌ Invalid? → Offer reset → Continue
✅ Valid config → Continue to start bot
```

## 📋 User Roles & Permissions

### 👑 SUPER_ADMIN (Level 3)
- **Who**: Chỉ creator (@hoangtrungdev)
- **Permissions**: 
  - Tất cả USER và ADMIN permissions
  - `/adduser` - Thêm users
  - `/removeuser` - Xóa users
  - `/listusers` - Xem danh sách users
- **Auto-promotion**: Tự động được promote khi lần đầu sử dụng bot

### 👤 ADMIN (Level 2)
- **Who**: Users được SUPER_ADMIN assign role ADMIN
- **Permissions**:
  - Tất cả USER permissions
  - Các admin commands (không bao gồm user management)

### 🙋 USER (Level 1)
- **Who**: Users được authorize với role USER
- **Permissions**:
  - `/start`, `/help`, `/myinfo`
  - Trading bot commands: `/list`, `/startbot`, `/createbot`
  - Bot management: `/stop`, `/stopall`, `/logs`, `/restart`
  - Credential management: `/addcreds`, `/listcreds`, `/showcreds`, `/deletecreds`

### 🚫 BLOCKED (Level 0)
- **Who**: Users bị block explicitly
- **Permissions**: Không có quyền gì

## 🔧 Configuration

### Config File Structure
```json
{
  "authorized_users": {
    "1631630468": {
      "username": "hoangtrungdev",
      "role": "SUPER_ADMIN",
      "added_by": "setup_wizard",
      "added_at": "2025-07-22T13:00:00.000Z"
    }
  },
  "settings": {
    "default_rejection_message": "❌ Bạn không có quyền sử dụng bot này. Liên hệ @hoangtrungdev để được cấp quyền.",
    "super_admin_only_commands": ["/adduser", "/removeuser", "/listusers"],
    "creator_username": "hoangtrungdev",
    "audit_log": true,
    "strict_mode": true,
    "auto_promote_creator": true
  }
}
```

### Settings Explanation
- **creator_username**: Username của creator (auto-promotion)
- **auto_promote_creator**: Enable/disable auto-promotion
- **strict_mode**: Strict authorization checking
- **audit_log**: Enable audit logging
- **super_admin_only_commands**: Commands chỉ SUPER_ADMIN có thể dùng

## 🎮 Bot Commands

### 👑 SUPER_ADMIN Only Commands
```bash
/adduser <user_id> <role> [username]   # Thêm user
/removeuser <user_id>                  # Xóa user  
/listusers                             # Danh sách users
```

### 👤 All Authorized Users
```bash
/start                                 # Welcome message
/help                                  # Help menu
/myinfo                                # User info
/list                                  # List trading bots
/startbot <symbol>                     # Start trading bot
/createbot                             # Create bot wizard
/stop <bot_id>                         # Stop specific bot
/stopall                               # Stop all bots
/logs <bot_id>                         # View bot logs
/restart <bot_id>                      # Restart bot
/addcreds <exchange>                   # Add credentials
/listcreds                             # List credentials
/showcreds <exchange>                  # Show credentials
/deletecreds <exchange>                # Delete credentials
```

## 🛠️ CLI Commands

### Setup & Configuration
```bash
./bot.sh setup-auth                    # Interactive setup wizard
./bot.sh config-auth                   # View/manage config
./bot.sh telegram                      # Start bot
./bot.sh help                          # Show all commands
```

## 🔍 Usage Examples

### Setup New System (Recommended - Auto Setup)
```bash
# 1. Start bot (sẽ tự động setup auth nếu cần)
./bot.sh telegram
# Response: "❌ Telegram bot authorization chưa được setup!"
# Response: "Bạn có muốn setup authorization ngay bây giờ? (y/N): y"
# Enter: hoangtrungdev
# Enter: 1631630468
# Bot sẽ tự động start sau khi setup xong

# 2. Test in Telegram
# Send: /start
# Response: Welcome message với SUPER_ADMIN permissions
```

### Setup New System (Manual)
```bash
# 1. Setup authorization manually
./bot.sh setup-auth
# Enter: hoangtrungdev
# Enter: 1631630468

# 2. Start bot
./bot.sh telegram

# 3. Test in Telegram
# Send: /start
# Response: Welcome message với SUPER_ADMIN permissions
```

### Add New User (via Telegram)
```bash
# As SUPER_ADMIN in Telegram:
/adduser 123456789 USER john_doe
# Response: ✅ User john_doe added successfully

/listusers
# Response: List of all authorized users
```

### View Config (via CLI)
```bash
./bot.sh config-auth
# Shows current configuration in readable format
```

## 🔒 Security Considerations

### Thread Safety
- Sử dụng `threading.Lock()` cho tất cả config operations
- Tránh race conditions khi multiple requests đồng thời

### Input Validation
- Validate user IDs (numeric)
- Sanitize usernames
- Check role validity

### Error Handling
- Graceful degradation khi config file corrupt
- Clear error messages cho users
- Audit logging cho security events

### Access Control
- Default deny policy
- Role hierarchy enforcement
- Command-level permissions

## 🐛 Troubleshooting

### Bot Không Khởi Động - Auth Issues
```bash
# Nếu bot báo lỗi auth setup:
./bot.sh telegram
# Follow interactive setup prompts

# Hoặc setup manual:
./bot.sh setup-auth

# Check auth config:
./bot.sh config-auth
```

### Bot Không Phản Hồi - Runtime Issues
```bash
# Check logs
docker logs telegram-bot

# Restart bot
./restart_bot.sh telegram

# Check authorization service
docker exec telegram-bot python -c "
from src.infrastructure.telegram.auth import TelegramAuthService
auth = TelegramAuthService()
print('Auth service OK')
"
```

### Config File Issues
```bash
# Reset config
./bot.sh setup-auth

# Check config location
ls -la ~/.autotrader/telegram_auth.json

# Validate JSON
python3 -m json.tool ~/.autotrader/telegram_auth.json
```

### Permission Denied
```bash
# Check user role
/myinfo

# Check if user is in authorized list
./bot.sh config-auth

# Add user (as SUPER_ADMIN)
/adduser <user_id> USER <username>
```

## 📊 Monitoring & Logging

### Log Locations
- **Container logs**: `docker logs telegram-bot`
- **Authorization logs**: Included in container logs
- **Config changes**: Logged with timestamps

### Key Log Messages
```
INFO - Authorization granted: user_id=123, role=USER, command=/start
WARNING - Authorization denied: user_id=999, command=/adduser
INFO - User added: user_id=123, role=USER, added_by=1631630468
INFO - Auto-promoted creator: user_id=1631630468, username=hoangtrungdev
```

## 🔄 Migration & Backup

### Backup Config
```bash
# Backup current config
cp ~/.autotrader/telegram_auth.json ~/.autotrader/telegram_auth.json.backup

# Restore from backup
cp ~/.autotrader/telegram_auth.json.backup ~/.autotrader/telegram_auth.json
```

### Migration Between Systems
```bash
# Export config from old system
scp user@old-server:~/.autotrader/telegram_auth.json ./

# Import to new system
cp telegram_auth.json ~/.autotrader/
./restart_bot.sh telegram
```

## 🎯 Best Practices

1. **Regular Backups**: Backup config file thường xuyên
2. **Monitor Logs**: Theo dõi authorization attempts
3. **Least Privilege**: Chỉ assign minimum permissions cần thiết
4. **Regular Review**: Review user list định kỳ
5. **Secure Storage**: Protect config file permissions (600)

## 🚀 Future Enhancements

- [ ] Web-based admin panel
- [ ] Role-based command restrictions
- [ ] Temporary access tokens
- [ ] Integration với external auth systems
- [ ] Advanced audit reporting
- [ ] Rate limiting per user
- [ ] Session management
