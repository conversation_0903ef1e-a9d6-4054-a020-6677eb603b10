# Telegram Bot Self-Testing System

## 🤖 Tổng quan

Telegram bot đã được tích hợp sẵn hệ thống tự test hoàn chỉnh, cho phép bot tự gửi và xử lý các commands để kiểm tra chức năng mà không cần user thật tương tác.

## 🧪 Các Commands Tự Test

### 1. `/selftest` - Tự Test Toàn Bộ
```
/selftest
```

**Chức năng:**
- Tự động test 5 commands chính: `/help`, `/createbot`, `/addcreds`, `/list`, `/status`
- Sử dụng fake user ID: `999999999`
- Có delay 2 giây giữa các test
- Gửi thông báo tiến trình và kết quả

**Quy trình:**
1. Bot gửi thông báo bắt đầu self-test
2. Lần lư<PERSON>t test từng command với thông báo tiến trình
3. Mỗi command được simulate và xử lý như message thật
4. <PERSON><PERSON><PERSON> thông báo hoàn thành với hướng dẫn kiểm tra logs

### 2. `/simulate <message>` - Simulate Message Cụ Thể
```
/simulate /help
/simulate /createbot
/simulate Hello World
```

**Chức năng:**
- Simulate bất kỳ message nào (command hoặc text)
- Sử dụng fake user ID: `999999999`
- Xử lý qua tất cả handlers như message thật
- Log chi tiết quá trình simulation

**Ví dụ:**
```
/simulate /help          # Test help command
/simulate /createbot     # Test createbot wizard
/simulate /list          # Test list bots
```

### 3. `/testcmd <command>` - Test Command Cụ Thể
```
/testcmd /help
/testcmd /createbot
/testcmd /list
```

**Chức năng:**
- Tương tự `/simulate` nhưng chuyên cho commands
- Sử dụng fake user ID: `888888888`
- Có hướng dẫn usage rõ ràng
- Interface thân thiện hơn cho testing commands

## 🔧 Cách Hoạt Động

### Core Method: `simulate_user_message()`

```python
async def simulate_user_message(self, chat_id: int, message_text: str, user_id: int = None):
    # Tạo fake Telegram objects
    fake_user = User(id=user_id, is_bot=False, first_name="SimulatedUser")
    fake_chat = Chat(id=chat_id, type=ChatType.PRIVATE)
    fake_message = Message(message_id=timestamp, date=timestamp, chat=fake_chat, from_user=fake_user, text=message_text)
    fake_update = Update(update_id=timestamp, message=fake_message)
    
    # Xử lý qua application handlers
    await self.application.process_update(fake_update)
```

### Quy Trình Xử Lý:
1. **Tạo Fake Objects**: User, Chat, Message, Update
2. **Process Update**: Gửi qua `application.process_update()`
3. **Handler Processing**: Tất cả handlers nhận và xử lý như message thật
4. **Logging**: Log chi tiết quá trình và kết quả
5. **Response**: Bot phản hồi như với user thật

## 📊 Monitoring & Debugging

### Log Messages
```
🔄 SIMULATED: Creating fake message from user 999999999
   Message: '/help'
   Chat ID: 123456789
🔄 SIMULATED: Processing fake update through handlers...
✅ SIMULATED: Message processing complete
```

### Kiểm Tra Kết Quả
- **Telegram Chat**: Xem responses của bot
- **Console Logs**: Kiểm tra simulation logs
- **Application Logs**: Debug handler processing

## 🎯 Use Cases

### 1. Development Testing
```bash
# Trong quá trình phát triển
/testcmd /newfeature
```

### 2. Deployment Verification
```bash
# Sau khi deploy
/selftest
```

### 3. Bug Reproduction
```bash
# Reproduce lỗi cụ thể
/simulate /problematic_command
```

### 4. Performance Testing
```bash
# Test nhiều commands liên tiếp
/simulate /command1
/simulate /command2
/simulate /command3
```

## ⚡ Best Practices

### 1. **Sử dụng Fake User IDs**
- `/selftest`: `999999999`
- `/simulate`: `999999999`  
- `/testcmd`: `888888888`

### 2. **Kiểm Tra Logs**
- Console logs cho simulation details
- Application logs cho handler processing
- Telegram chat cho bot responses

### 3. **Test Systematically**
- Sử dụng `/selftest` cho comprehensive testing
- Sử dụng `/testcmd` cho specific command testing
- Sử dụng `/simulate` cho edge cases

### 4. **Delay Between Tests**
- Built-in delays để tránh rate limiting
- Manual delays cho extensive testing

## 🔍 Troubleshooting

### Common Issues:
1. **No Response**: Kiểm tra bot token và permissions
2. **Handler Errors**: Xem application logs
3. **Simulation Fails**: Kiểm tra fake object creation

### Debug Commands:
```
/test           # Simple connectivity test
/selftest       # Full system test
/testcmd /help  # Specific command test
```

## 📝 Notes

- Hệ thống hoàn toàn tự động, không cần user interaction
- Tất cả commands được xử lý như messages thật
- Fake user IDs để phân biệt với real users
- Comprehensive logging cho debugging
- Built-in delays để tránh spam/rate limiting
