version: "3.8"

services:
  # Telegram Bot Manager Service - Central Control
  telegram-manager:
    build:
      context: .
      dockerfile: Dockerfile.telegram
    container_name: autotrader-telegram
    restart: unless-stopped
    volumes:
      # Shared data volumes between telegram manager and trading bots
      - ./configs:/app/configs
      - ./credentials:/app/credentials
      - ./data:/app/data
      - ./logs:/app/logs
      - ./docker_volumes:/app/docker_volumes
      # Docker socket for container management
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      # Telegram Bot Configuration (required)
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID}
      
      # Bot Manager Settings
      - BOT_MODE=production
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - TELEGRAM_TIMEOUT=30
      - MAX_RETRIES=5
      - HEARTBEAT_INTERVAL=300
      
      # Docker Integration
      - DOCKER_HOST=unix:///var/run/docker.sock
      
    ports:
      - "8081:8080"  # Telegram bot health monitoring
    networks:
      - autotrader-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    healthcheck:
      test: ["CMD", "curl", "-f", "https://api.telegram.org/bot${TELEGRAM_BOT_TOKEN}/getMe", "||", "exit", "1"]
      interval: 60s
      timeout: 30s
      start_period: 10s
      retries: 3

  # Trading Bot Service Template (managed by Telegram Bot)
  # Individual trading bots will be created dynamically via Telegram commands
  # This service is kept as a template and will not run by default
  trading-bot-template:
    build:
      context: .
      dockerfile: Dockerfile.trader
    # No container_name - will be set dynamically
    restart: "no"  # Controlled by Telegram manager
    volumes:
      # Shared configuration and data
      - ./configs:/app/configs:ro
      - ./credentials:/app/credentials:ro
      - ./data:/app/data
      - ./logs:/app/logs
    environment:
      # Default trading configuration (will be overridden)
      - EXCHANGE=bybit
      - TRADE_MODE=test
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - HEARTBEAT_INTERVAL=60
      
      # Trading parameters (set by Telegram commands)
      - TRADE_SYMBOL=${TRADE_SYMBOL:-HYPER/USDT:USDT}
      - TRADE_AMOUNT=${TRADE_AMOUNT:-10}
      - TRADE_DIRECTION=${TRADE_DIRECTION:-both}
      - STOP_LOSS=${STOP_LOSS:-}
      - TAKE_PROFIT=${TAKE_PROFIT:-}
      
      # API credentials (loaded by credential system)
      - BYBIT_API_KEY=${BYBIT_API_KEY:-}
      - BYBIT_API_SECRET=${BYBIT_API_SECRET:-}
      
    ports:
      - "8080"  # Dynamic port assignment for individual bots
    networks:
      - autotrader-network
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    healthcheck:
      test: ["CMD", "python", "-c", "import sys; sys.exit(0)"]
      interval: 60s
      timeout: 30s
      start_period: 30s
      retries: 3
    profiles:
      - manual  # Only start manually or via Telegram

  # Development/Testing Services (optional)
  telegram-dev:
    build:
      context: .
      dockerfile: Dockerfile.telegram
    container_name: autotrader-telegram-dev
    restart: "no"
    volumes:
      - ./configs:/app/configs
      - ./credentials:/app/credentials
      - ./data:/app/data
      - ./logs:/app/logs
      - ./docker_volumes:/app/docker_volumes
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN_DEV:-}
      - TELEGRAM_CHAT_ID=${TELEGRAM_CHAT_ID_DEV:-}
      - BOT_MODE=development
      - LOG_LEVEL=DEBUG
    ports:
      - "8082:8080"
    networks:
      - autotrader-network
    profiles:
      - dev

networks:
  autotrader-network:
    driver: bridge
    name: autotrader-network

volumes:
  # Named volumes for persistent data
  autotrader-configs:
    driver: local
  autotrader-credentials:
    driver: local
  autotrader-data:
    driver: local
  autotrader-logs:
    driver: local
