# Git and version control
.git
.gitignore
.gitattributes

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Virtual environments
.venv
.conda

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~
.cursor/
.claude/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Documentation
*.md
docs/
MIGRATION.md
DCA_OPTIMIZATION.md
BYBIT_SETUP.md
CLAUDE.md
TIMEFRAME_SUPPORT.md

# Test files
test_*.py
*_test.py
tests/
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# Logs and data (will be mounted as volumes)
logs/
data/
*.log

# Configuration files (will be mounted)
configs/*.json
!configs/config_template.json

# Temporary files
tmp/
temp/
.tmp
*.tmp

# Build artifacts
build/
dist/
*.egg-info/

# Docker related
Dockerfile*
docker-compose*.yml
.dockerignore

# Scripts that shouldn't be in container
run.sh
clear_logs.sh
demo_bybit.py
bot_dca.py

# Environment files
.env
.env.local
.env.*.local

# Jupyter notebooks
*.ipynb
.ipynb_checkpoints

# Package manager files
poetry.lock
Pipfile
Pipfile.lock

# Node.js (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log* 