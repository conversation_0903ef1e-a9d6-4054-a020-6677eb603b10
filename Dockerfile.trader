# Optimized Trading Bot Dockerfile - Minimal production build
FROM python:3.11-slim AS base

# Set environment variables for Python optimization
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH=/app

# Install system dependencies including build tools for compilation
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    build-essential \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Create trader user for security
RUN groupadd -r trader && useradd -r -g trader trader

# Dependencies stage
FROM base AS dependencies

WORKDIR /app

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies with build tools available
RUN pip install --no-cache-dir --upgrade pip setuptools wheel \
    && pip install --no-cache-dir -r requirements.txt

# Production stage - minimal image
FROM python:3.11-slim AS production

# Install only runtime dependencies (no build tools)
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    ca-certificates \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Copy Python packages from dependencies stage
COPY --from=dependencies /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=dependencies /usr/local/bin /usr/local/bin

# Create trader user
RUN groupadd -r trader && useradd -r -g trader trader

# Create minimal directory structure
RUN mkdir -p /app/src /app/configs /app/data /app/logs \
    && chown -R trader:trader /app

WORKDIR /app

# Copy only essential application files
COPY --chown=trader:trader src/ ./src/
COPY --chown=trader:trader main.py ./
COPY --chown=trader:trader configs/ ./configs/

# Set permissions - minimal and secure
RUN chmod +x main.py \
    && chmod 755 src

# Switch to non-root user
USER trader

# Minimal volumes for persistent data
VOLUME ["/app/data", "/app/logs"]

# Environment optimized for trading
ENV EXCHANGE=bybit \
    TRADE_MODE=live \
    LOG_LEVEL=INFO \
    PYTHONPATH=/app

# Lightweight health check
HEALTHCHECK --interval=180s --timeout=15s --start-period=60s --retries=2 \
    CMD python3 -c "import sys; sys.exit(0)" || exit 1

# Default command - direct execution
CMD ["python3", "main.py"] 