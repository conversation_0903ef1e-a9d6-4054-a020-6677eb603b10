# Telegram Bot Configuration
# Copy this file to .env and fill in your actual values

# Telegram Bot Token (get from @BotFather)
TELEGRAM_BOT_TOKEN=your_bot_token_here

# Telegram Chat ID (your user ID or group chat ID)
TELEGRAM_CHAT_ID=your_chat_id_here

# Trading API Configuration
BYBIT_API_KEY=your_bybit_api_key_here
BYBIT_API_SECRET=your_bybit_api_secret_here

# Optional: Test mode credentials
BYBIT_TESTNET_API_KEY=your_testnet_api_key_here
BYBIT_TESTNET_API_SECRET=your_testnet_api_secret_here

# Application Configuration
LOG_LEVEL=INFO
DEBUG=false

# Docker Configuration
TRADER_IMAGE=autotrader-trader:latest
TELEGRAM_IMAGE=autotrader-telegram:latest
