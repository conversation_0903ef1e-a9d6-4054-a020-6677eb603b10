# Core Trading Bot Dependencies (Required)
ccxt>=4.0.0,<5.0.0
pandas>=1.5.0,<3.0.0
numpy>=1.24.0,<2.0.0
python-dotenv>=1.0.0,<2.0.0
ta>=0.10.2,<1.0.0
requests>=2.28.0,<3.0.0
aiohttp>=3.8.0,<4.0.0
websockets>=11.0.0,<12.0.0
pyyaml>=6.0.0,<7.0.0
pytest>=8.0.0,<9.0.0
pytest-asyncio>=0.24.0,<1.0.0
colorama>=0.4.6,<1.0.0
rich>=13.7.0,<14.0.0
click>=8.1.0,<9.0.0
aiohttp-cors>=0.7.0,<1.0.0

# Infrastructure Dependencies  
docker>=6.0.0,<8.0.0

# Telegram Dependencies
python-telegram-bot>=20.0,<21.0

# Optional Dependencies (install separately if needed)
# For Telegram Management:
#
# For Advanced Charts:
# matplotlib>=3.6.0
# plotly>=5.17.0 