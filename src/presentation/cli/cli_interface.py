"""CLI Interface for Trading Bot - Fixed Version"""
import asyncio
import os
import sys
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
import argparse
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.live import Live
from rich.layout import Layout
from rich.text import Text
from rich.progress import Progress, SpinnerColumn, TextColumn
import click

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), '../../../'))

from src.application.engine.trading_engine import TradingEngine
from src.application.events import event_bus, EventType
from src.infrastructure.data.statistics_tracker import StatisticsTracker
from src.infrastructure.data.state_manager import StateManager


class TradingBotCLI:
    """Professional CLI interface for trading bot"""
    
    def __init__(self, debug: bool = False):
        self.console = Console()
        self.debug = debug
        self.engine: Optional[TradingEngine] = None
        self.stats_tracker: Optional[StatisticsTracker] = None
        self.state_manager: Optional[StateManager] = None
        self.is_monitoring = False
        
    def display_header(self):
        """Display bot header"""
        header = """
╔═══════════════════════════════════════════════════════════════╗
║                    🚀 CRYPTO TRADING BOT 🚀                   ║
╚═══════════════════════════════════════════════════════════════╝
        """
        self.console.print(header, style='bold cyan')
    
    def display_menu(self):
        """Display main menu"""
        menu_table = Table(title='📋 Main Menu', show_header=False, box=None)
        menu_table.add_column('Option', style='bold yellow', width=10)
        menu_table.add_column('Description', style='white')
        
        menu_items = [
            ('1', '🟢 Start Trading Bot'),
            ('2', '🛑 Stop Trading Bot'),
            ('3', '📊 View Live Dashboard'),
            ('4', '📈 Performance Statistics'),
            ('5', '💼 Position Status'),
            ('6', '🔧 Bot Configuration'),
            ('7', '📝 Recent Logs'),
            ('8', '🎯 Signal History'),
            ('9', '⚠️  Risk Status'),
            ('0', '🚪 Exit')
        ]
        
        for option, description in menu_items:
            menu_table.add_row(option, description)
        
        self.console.print(menu_table)
    
    async def start_bot(self, config_file: str = 'config.json'):
        """Start trading bot"""
        try:
            self.console.print("\n🚀 Starting Trading Bot...", style='bold green')
            
            with Progress(
                SpinnerColumn(),
                TextColumn('[progress.description]{task.description}'),
                console=self.console
            ) as progress:
                task = progress.add_task('Initializing components...', total=None)
                
                # Initialize engine
                self.engine = TradingEngine(config_file, debug=self.debug)
                self.stats_tracker = self.engine.stats_tracker
                self.state_manager = self.engine.state_manager
                
                progress.update(task, description='Connecting to exchange...')
                await asyncio.sleep(1)
                
                progress.update(task, description='Loading historical data...')
                await asyncio.sleep(1)
                
                progress.update(task, description='Starting trading engine...')
                
                # Start bot in background
                asyncio.create_task(self.engine.start())
                
                progress.update(task, description='Bot started successfully!')
                await asyncio.sleep(1)
            
            self.console.print("✅ Trading Bot started successfully!", style='bold green')
            self.console.print(f"📊 Trading: {self.engine.config.symbol}")
            self.console.print(f"🏢 Exchange: {self.engine.config.exchange.upper()}")
            self.console.print(f"📈 Direction: {self.engine.config.direction}")
            self.console.print(f"💰 Amount: ${self.engine.config.amount}")
            
        except Exception as e:
            self.console.print(f"❌ Failed to start bot: {e}", style='bold red')
    
    async def stop_bot(self):
        """Stop trading bot"""
        if not self.engine:
            self.console.print("❌ Bot is not running!", style='bold red')
            return
        
        self.console.print("\n🛑 Stopping Trading Bot...", style='bold yellow')
        
        try:
            await self.engine.stop()
            self.engine = None
            self.console.print("✅ Bot stopped successfully!", style='bold green')
        except Exception as e:
            self.console.print(f"❌ Error stopping bot: {e}", style='bold red')
    
    async def display_dashboard(self):
        """Display live dashboard"""
        if not self.engine or not self.stats_tracker:
            self.console.print("❌ Bot is not running!", style='bold red')
            return
        
        self.is_monitoring = True
        
        async def update_dashboard():
            while self.is_monitoring:
                os.system('clear' if os.name == 'posix' else 'cls')
                self.display_header()
                
                # Bot Status
                bot_status = self._create_bot_status_panel()
                self.console.print(bot_status)
                
                # Performance
                performance = self._create_performance_panel()
                self.console.print(performance)
                
                self.console.print("\n[dim]Press Ctrl+C to stop dashboard[/dim]")
                
                await asyncio.sleep(2)
        
        self.console.print("📊 Starting Live Dashboard... (Press Ctrl+C to stop)")
        try:
            await update_dashboard()
        except KeyboardInterrupt:
            self.is_monitoring = False
            self.console.print("\n📊 Dashboard stopped")
    
    def _create_bot_status_panel(self) -> Panel:
        """Create bot status panel"""
        if not self.engine:
            return Panel('Bot not running', style='red')
        
        status_table = Table(show_header=False, box=None)
        status_table.add_column('Metric', style='bold yellow')
        status_table.add_column('Value', style='white')
        
        # Bot status
        status_table.add_row('🤖 Status', '🟢 Running' if self.engine.is_running else '🔴 Stopped')
        status_table.add_row('📊 Symbol', self.engine.config.symbol)
        status_table.add_row('📈 Direction', self.engine.config.direction)
        status_table.add_row('💰 Amount', f'${self.engine.config.amount}')
        
        # Current position
        try:
            position = self.state_manager.get_position(self.engine.config.symbol) if self.state_manager else None
            if position:
                status_table.add_row('💼 Position', f'{position.side} - {position.contracts:.4f}')
                status_table.add_row('💵 Entry Price', f'${position.entry_price:.4f}')
                status_table.add_row('📊 Current PnL', f'${position.pnl:.2f}')
            else:
                status_table.add_row('💼 Position', 'None')
        except Exception as e:
            status_table.add_row('💼 Position', f'Error: {str(e)[:30]}')
        
        return Panel(status_table, title='🤖 Bot Status', border_style='green')
    
    def _create_performance_panel(self) -> Panel:
        """Create performance panel"""
        if not self.stats_tracker:
            return Panel('No stats available', style='red')
        
        try:
            stats = self.stats_tracker.get_period_stats('all_time')
            
            perf_table = Table(show_header=False, box=None)
            perf_table.add_column('Metric', style='bold yellow')
            perf_table.add_column('Value', style='white')
            
            perf_table.add_row('💰 Total PnL', f"${stats.total_pnl:.2f}")
            perf_table.add_row('📈 Win Rate', f"{stats.win_rate:.1f}%")
            perf_table.add_row('🎯 Total Trades', str(stats.total_trades))
            perf_table.add_row('🏆 Profit Factor', f"{stats.profit_factor:.2f}")
            perf_table.add_row('📉 Max Drawdown', f"{stats.max_drawdown:.2f}%")
            perf_table.add_row('⚡ Sharpe Ratio', f"{stats.sharpe_ratio:.2f}")
        except Exception as e:
            perf_table = Table(show_header=False, box=None)
            perf_table.add_row('Error', str(e)[:50])
        
        return Panel(perf_table, title='📈 Performance', border_style='blue')
    
    async def show_statistics(self):
        """Show detailed statistics"""
        if not self.stats_tracker:
            self.console.print("❌ No statistics available!", style='bold red')
            return
        
        self.console.print("\n📈 Performance Statistics", style='bold cyan')
        
        try:
            # Generate performance report
            report = self.stats_tracker.generate_performance_report()
            
            # Summary table
            summary_table = Table(title='📊 Performance Summary')
            summary_table.add_column('Period', style='bold yellow')
            summary_table.add_column('Trades', style='white')
            summary_table.add_column('PnL', style='green')
            summary_table.add_column('Win Rate', style='blue')
            
            for period in ['daily', 'weekly', 'monthly', 'all_time']:
                period_data = report.get(period, {})
                summary_table.add_row(
                    period.title().replace('_', ' '),
                    str(period_data.get('total_trades', 0)),
                    f"${period_data.get('total_pnl', 0):.2f}",
                    f"{period_data.get('win_rate', 0):.1f}%"
                )
            
            self.console.print(summary_table)
            
        except Exception as e:
            self.console.print(f"❌ Error loading statistics: {e}", style='bold red')
    
    async def show_position_status(self):
        """Show current position status"""
        if not self.state_manager:
            self.console.print("❌ No state manager available!", style='bold red')
            return
        
        symbol = self.engine.config.symbol if self.engine else 'BTCUSDT'
        position = self.state_manager.get_position(symbol)
        
        if not position:
            self.console.print("💼 No active position", style='bold yellow')
            return
        
        pos_table = Table(title='💼 Current Position')
        pos_table.add_column('Property', style='bold yellow')
        pos_table.add_column('Value', style='white')
        
        pos_table.add_row('Symbol', position.symbol)
        pos_table.add_row('Side', position.side)
        pos_table.add_row('Contracts', f'{position.contracts:.6f}')
        pos_table.add_row('Entry Price', f'${position.entry_price:.4f}')
        pos_table.add_row('Current Price', f'${position.current_price:.4f}')
        pos_table.add_row('PnL', f'${position.pnl:.2f}')
        pos_table.add_row('PnL %', f'{position.pnl_percentage:.2f}%')
        pos_table.add_row('State', position.state.value)
        
        if position.dca_orders:
            pos_table.add_row('DCA Orders', str(len(position.dca_orders)))
        
        self.console.print(pos_table)
    
    async def show_recent_logs(self):
        """Show recent logs from log files"""
        self.console.print("\n📝 Recent Logs", style='bold cyan')
        
        log_files = [
            ('logs/tradingengine.log', '🤖 Trading Engine'),
            ('logs/errors.log', '❌ Error Logs'),
            ('logs/trades.jsonl', '💰 Trade Logs')
        ]
        
        for log_file, title in log_files:
            if os.path.exists(log_file):
                try:
                    self.console.print(f"\n{title}:", style='bold yellow')
                    
                    # Read last 10 lines
                    with open(log_file, 'r') as f:
                        lines = f.readlines()
                        recent_lines = lines[-10:] if len(lines) > 10 else lines
                    
                    if recent_lines:
                        log_table = Table(show_header=False, box=None, padding=(0, 1))
                        log_table.add_column('Time', style='dim', width=20)
                        log_table.add_column('Message', style='white')
                        
                        for line in recent_lines:
                            line = line.strip()
                            if line:
                                # Parse log line
                                if '|' in line:
                                    parts = line.split('|', 2)
                                    if len(parts) >= 3:
                                        timestamp = parts[0].strip()
                                        message = parts[2].strip()
                                        log_table.add_row(timestamp, message[:80] + '...' if len(message) > 80 else message)
                                else:
                                    log_table.add_row('', line[:100] + '...' if len(line) > 100 else line)
                        
                        self.console.print(log_table)
                    else:
                        self.console.print("  No recent logs", style='dim')
                        
                except Exception as e:
                    self.console.print(f"  Error reading {log_file}: {e}", style='red')
            else:
                self.console.print(f"\n{title}: File not found", style='dim red')
    
    async def show_bot_configuration(self):
        """Show current bot configuration"""
        if not self.engine:
            self.console.print("❌ Bot is not running!", style='bold red')
            return
        
        self.console.print("\n🔧 Bot Configuration", style='bold cyan')
        
        config = self.engine.config
        
        # Basic settings
        basic_table = Table(title='⚙️ Basic Settings')
        basic_table.add_column('Setting', style='bold yellow')
        basic_table.add_column('Value', style='white')
        
        basic_table.add_row('Symbol', config.symbol)
        basic_table.add_row('Exchange', config.exchange.upper())
        basic_table.add_row('Direction', config.direction)
        basic_table.add_row('Amount', f'${config.amount}')
        basic_table.add_row('Order Type', config.order_type)
        basic_table.add_row('Test Mode', '🟢 Enabled' if config.use_test_mode else '🔴 Disabled')
        
        self.console.print(basic_table)
        
        # DCA settings
        if hasattr(config, 'dca') and config.dca:
            try:
                dca_table = Table(title='📊 DCA Settings')
                dca_table.add_column('Setting', style='bold yellow')
                dca_table.add_column('Value', style='white')
                
                dca_table.add_row('Enabled', '🟢 Yes' if config.dca.enabled else '🔴 No')
                dca_table.add_row('Max Orders', str(config.dca.max_orders))
                dca_table.add_row('Price Deviation', f'{config.dca.price_deviation}%')
                dca_table.add_row('Volume Scale', f'{config.dca.volume_scale}x')
                
                self.console.print(dca_table)
            except Exception as e:
                self.console.print(f"DCA config error: {e}")
        
        # Risk settings
        if hasattr(config, 'risk') and config.risk:
            try:
                risk_table = Table(title='⚠️ Risk Management')
                risk_table.add_column('Setting', style='bold yellow')
                risk_table.add_column('Value', style='white')
                
                # Use safe attribute access
                risk_table.add_row('Stop Loss', f'{getattr(config.risk, "stop_loss", 2.0)}%')
                risk_table.add_row('Take Profit', f'{getattr(config.risk, "take_profit", 3.0)}%')
                risk_table.add_row('Risk Per Trade', f'{config.risk.risk_per_trade}%')
                risk_table.add_row('Daily Loss Limit', f'{config.risk.daily_loss_limit}%')
                risk_table.add_row('Max Position Size', f'${config.risk.max_position_size}')
                
                self.console.print(risk_table)
            except Exception as e:
                self.console.print(f"Risk config error: {e}")
    
    async def show_signal_history(self):
        """Show recent trading signals"""
        self.console.print("\n🎯 Signal History", style='bold cyan')
        
        try:
            # Get signal events from event bus
            signal_events = event_bus.get_events_by_type(EventType.SIGNAL_GENERATED, limit=20)
            
            if not signal_events:
                self.console.print("📊 No signals found", style='dim yellow')
                return
            
            signal_table = Table(title='📈 Recent Trading Signals')
            signal_table.add_column('Time', style='bold yellow', width=20)
            signal_table.add_column('Direction', style='green', width=10)
            signal_table.add_column('Price', style='cyan', width=12)
            signal_table.add_column('Confidence', style='blue', width=12)
            signal_table.add_column('Indicators', style='white')
            
            for event in signal_events[-10:]:  # Last 10 signals
                signal_data = event.data
                time_str = event.timestamp.strftime('%H:%M:%S')
                
                direction = signal_data.get('direction', 'N/A')
                price = signal_data.get('entry_price', 0)
                confidence = signal_data.get('confidence', 0)
                indicators = signal_data.get('indicators', {})
                
                # Format indicators
                indicator_str = []
                if 'rsi' in indicators:
                    indicator_str.append(f"RSI: {indicators['rsi']:.1f}")
                if 'ema_trend' in indicators:
                    indicator_str.append(f"EMA: {indicators['ema_trend']}")
                
                signal_table.add_row(
                    time_str,
                    direction,
                    f'${price:.4f}' if price else 'N/A',
                    f'{confidence:.1f}%' if confidence else 'N/A',
                    ', '.join(indicator_str) if indicator_str else 'N/A'
                )
            
            self.console.print(signal_table)
            
        except Exception as e:
            self.console.print(f"❌ Error loading signal history: {e}", style='bold red')
    
    async def show_risk_status(self):
        """Show current risk status"""
        if not self.engine:
            self.console.print("❌ Bot is not running!", style='bold red')
            return
        
        self.console.print("\n⚠️ Risk Status", style='bold cyan')
        
        try:
            risk_manager = self.engine.risk_manager
            risk_summary = risk_manager.get_risk_summary()
            
            # Current risk metrics
            risk_table = Table(title='🛡️ Current Risk Metrics')
            risk_table.add_column('Metric', style='bold yellow')
            risk_table.add_column('Current', style='white')
            risk_table.add_column('Limit', style='red')
            risk_table.add_column('Status', style='green')
            
            # Daily loss
            daily_loss = risk_summary['daily_loss_percentage']
            daily_limit = self.engine.config.risk.daily_loss_limit
            loss_status = '🟢 OK' if daily_loss < daily_limit else '🔴 EXCEEDED'
            
            risk_table.add_row(
                'Daily Loss',
                f'{daily_loss:.2f}%',
                f'{daily_limit}%',
                loss_status
            )
            
            # Position count
            active_positions = risk_summary['active_positions']
            max_positions = getattr(self.engine.config.risk, 'max_concurrent_positions', 3)
            pos_status = '🟢 OK' if active_positions < max_positions else '🔴 MAX'
            
            risk_table.add_row(
                'Active Positions',
                str(active_positions),
                str(max_positions),
                pos_status
            )
            
            # Trade count today
            trades_today = risk_summary['daily_trades']
            max_trades = getattr(self.engine.config.risk, 'max_trades_per_day', 10)
            trades_status = '🟢 OK' if trades_today < max_trades else '🔴 MAX'
            
            risk_table.add_row(
                'Trades Today',
                str(trades_today),
                str(max_trades),
                trades_status
            )
            
            self.console.print(risk_table)
            
            # Risk score calculation
            win_rate = risk_summary['win_rate']
            drawdown = risk_summary['current_drawdown']
            exposure_ratio = risk_summary['total_exposure'] / risk_summary['current_capital'] if risk_summary['current_capital'] > 0 else 0
            
            # Simple risk score: 0-100 (higher is more risky)
            risk_score = (
                (100 - win_rate) * 0.3 +  # Win rate component
                drawdown * 0.4 +           # Drawdown component
                exposure_ratio * 100 * 0.3  # Exposure component
            )
            
            risk_level = 'LOW'
            risk_color = 'green'
            if risk_score > 70:
                risk_level = 'HIGH'
                risk_color = 'red'
            elif risk_score > 40:
                risk_level = 'MEDIUM'
                risk_color = 'yellow'
            
            self.console.print(f"\n🎯 Overall Risk Level: [{risk_color}]{risk_level}[/{risk_color}] ({risk_score:.1f}/100)")
            
        except Exception as e:
            self.console.print(f"❌ Error loading risk status: {e}", style='bold red')
    
    async def interactive_menu(self):
        """Interactive menu loop"""
        while True:
            os.system('clear' if os.name == 'posix' else 'cls')
            self.display_header()
            self.display_menu()
            
            choice = self.console.input('\n🔧 Select option: ').strip()
            
            if choice == '1':
                await self.start_bot()
                self.console.input('\nPress Enter to continue...')
            elif choice == '2':
                await self.stop_bot()
                self.console.input('\nPress Enter to continue...')
            elif choice == '3':
                await self.display_dashboard()
            elif choice == '4':
                await self.show_statistics()
                self.console.input('\nPress Enter to continue...')
            elif choice == '5':
                await self.show_position_status()
                self.console.input('\nPress Enter to continue...')
            elif choice == '6':
                await self.show_bot_configuration()
                self.console.input('\nPress Enter to continue...')
            elif choice == '7':
                await self.show_recent_logs()
                self.console.input('\nPress Enter to continue...')
            elif choice == '8':
                await self.show_signal_history()
                self.console.input('\nPress Enter to continue...')
            elif choice == '9':
                await self.show_risk_status()
                self.console.input('\nPress Enter to continue...')
            elif choice == '0':
                if self.engine:
                    await self.stop_bot()
                self.console.print("👋 Goodbye!", style='bold cyan')
                break
            else:
                self.console.print("❌ Invalid option!", style='bold red')
                self.console.input('\nPress Enter to continue...')


# Click commands for CLI
@click.group()
def cli():
    """Professional Crypto Trading Bot CLI"""
    pass


@cli.command()
@click.option('--config', default='config.json', help='Configuration file path')
@click.option('--debug', is_flag=True, help='Enable debug logging')
# Trading Parameters (same as main.py)
@click.option('--symbol', type=str, help='Trading pair (e.g., HYPER, BTC/USDT:USDT)')
@click.option('--amount', type=float, help='Position size in USDT')
@click.option('--dca-amount', type=float, help='DCA amount for all strategies')
@click.option('--dca-bb-amount', type=float, help='DCA amount for Bollinger Bands strategy')
@click.option('--dca-ema-amount', type=float, help='DCA amount for EMA strategies')
@click.option('--exchange', type=str, help='Exchange name (default: bybit)')
@click.option('--direction', type=click.Choice(['LONG', 'SHORT', 'long', 'short']), help='Trade direction (LONG or SHORT)')
@click.option('--test-mode', is_flag=True, help='Enable test/paper trading mode')
@click.option('--max-position', type=float, help='Maximum allowed position size')
@click.option('--stop-loss', type=float, help='Stop loss percentage (e.g., 2 for 2%)')
@click.option('--take-profit', type=float, help='Take profit percentage (e.g., 3 for 3%)')
@click.option('--timeframe', type=str, help='Primary timeframe (15m, 1h, 4h)')
@click.option('--key', type=str, help='Bybit API key (overrides BYBIT_API_KEY environment variable)')
@click.option('--secret', type=str, help='Bybit API secret (overrides BYBIT_API_SECRET environment variable)')
def start(config, debug, symbol, amount, dca_amount, dca_bb_amount, dca_ema_amount, 
          exchange, direction, test_mode, max_position, stop_loss, take_profit, 
          timeframe, key, secret):
    """Start the trading bot"""
    
    # Set environment variables from CLI arguments
    _set_cli_environment_variables(symbol, amount, dca_amount, dca_bb_amount, dca_ema_amount,
                                   exchange, direction, test_mode, max_position, stop_loss,
                                   take_profit, timeframe, key, secret)
    
    async def _start():
        bot_cli = TradingBotCLI(debug=debug)
        await bot_cli.start_bot(config)
        # Keep running
        try:
            while True:
                await asyncio.sleep(1)
        except KeyboardInterrupt:
            await bot_cli.stop_bot()
    
    asyncio.run(_start())


@cli.command()
@click.option('--debug', is_flag=True, help='Enable debug logging')
# Trading Parameters for dashboard
@click.option('--symbol', type=str, help='Trading pair (e.g., HYPER, BTC/USDT:USDT)')
@click.option('--amount', type=float, help='Position size in USDT')
@click.option('--dca-amount', type=float, help='DCA amount for all strategies')
@click.option('--dca-bb-amount', type=float, help='DCA amount for Bollinger Bands strategy')
@click.option('--dca-ema-amount', type=float, help='DCA amount for EMA strategies')
@click.option('--exchange', type=str, help='Exchange name (default: bybit)')
@click.option('--direction', type=click.Choice(['LONG', 'SHORT', 'long', 'short']), help='Trade direction (LONG or SHORT)')
@click.option('--test-mode', is_flag=True, help='Enable test/paper trading mode')
@click.option('--max-position', type=float, help='Maximum allowed position size')
@click.option('--stop-loss', type=float, help='Stop loss percentage (e.g., 2 for 2%)')
@click.option('--take-profit', type=float, help='Take profit percentage (e.g., 3 for 3%)')
@click.option('--timeframe', type=str, help='Primary timeframe (15m, 1h, 4h)')
@click.option('--key', type=str, help='Bybit API key (overrides BYBIT_API_KEY environment variable)')
@click.option('--secret', type=str, help='Bybit API secret (overrides BYBIT_API_SECRET environment variable)')
def dashboard(debug, symbol, amount, dca_amount, dca_bb_amount, dca_ema_amount,
              exchange, direction, test_mode, max_position, stop_loss, take_profit,
              timeframe, key, secret):
    """Launch interactive dashboard"""
    
    # Set environment variables from CLI arguments
    _set_cli_environment_variables(symbol, amount, dca_amount, dca_bb_amount, dca_ema_amount,
                                   exchange, direction, test_mode, max_position, stop_loss,
                                   take_profit, timeframe, key, secret)
    
    async def _dashboard():
        bot_cli = TradingBotCLI(debug=debug)
        await bot_cli.interactive_menu()
    
    asyncio.run(_dashboard())


@cli.command()
def status():
    """Show bot status"""
    console = Console()
    console.print("📊 Bot Status Check", style='bold cyan')
    # Add status check logic here


def _set_cli_environment_variables(symbol, amount, dca_amount, dca_bb_amount, dca_ema_amount,
                                   exchange, direction, test_mode, max_position, stop_loss,
                                   take_profit, timeframe, key, secret):
    """Set environment variables from CLI arguments (same logic as main.py)"""
    import os
    
    # Process symbol format (same logic as bot.sh and main.py)
    if symbol:
        if '/' not in symbol:
            # Convert simple format to full format and uppercase
            symbol = symbol.upper()
            symbol = f"{symbol}/USDT:USDT"
        else:
            # If already in full format, ensure base symbol is uppercase
            base_part = symbol.split('/')[0]
            rest_part = '/'.join(symbol.split('/')[1:])
            base_part = base_part.upper()
            symbol = f"{base_part}/{rest_part}"
        
        os.environ['TRADE_SYMBOL'] = symbol
        print(f"📊 Symbol: {symbol}")
    
    # Set other trading parameters
    if amount is not None:
        os.environ['TRADE_AMOUNT'] = str(amount)
        print(f"💰 Amount: ${amount}")
    
    if dca_amount is not None:
        os.environ['TRADE_DCA_AMOUNT'] = str(dca_amount)
        print(f"📈 DCA Amount: ${dca_amount}")
    
    if dca_bb_amount is not None:
        os.environ['TRADE_DCA_BB_AMOUNT'] = str(dca_bb_amount)
        print(f"📊 DCA BB Amount: ${dca_bb_amount}")
    
    if dca_ema_amount is not None:
        os.environ['TRADE_DCA_EMA_AMOUNT'] = str(dca_ema_amount)
        print(f"📈 DCA EMA Amount: ${dca_ema_amount}")
    
    if exchange:
        os.environ['TRADE_EXCHANGE'] = exchange
        print(f"🏢 Exchange: {exchange}")
    
    if direction:
        direction = direction.upper()
        os.environ['TRADE_DIRECTION'] = direction
        print(f"📈 Direction: {direction}")
    
    if max_position is not None:
        os.environ['TRADE_MAX_POSITION'] = str(max_position)
        print(f"📊 Max Position: ${max_position}")
    
    if stop_loss is not None:
        os.environ['TRADE_STOP_LOSS'] = str(stop_loss)
        print(f"🛑 Stop Loss: {stop_loss}%")
    
    if take_profit is not None:
        os.environ['TRADE_TAKE_PROFIT'] = str(take_profit)
        print(f"🎯 Take Profit: {take_profit}%")
    
    if timeframe:
        os.environ['TRADE_TIMEFRAME'] = timeframe
        print(f"⏰ Timeframe: {timeframe}")
    
    if key:
        os.environ['BYBIT_API_KEY'] = key
        print(f"🔑 API Key: {key[:10]}...")
    
    if secret:
        os.environ['BYBIT_API_SECRET'] = secret
        print("🔐 API Secret: ***")
    
    # Set test mode environment variable
    if test_mode:
        os.environ['TRADE_TEST_MODE'] = 'true'
        print("🧪 Test Mode: Enabled")


if __name__ == '__main__':
    cli() 