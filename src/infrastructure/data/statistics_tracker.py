"""Statistics tracker for trading performance"""
import csv
import json
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
import pandas as pd
from collections import defaultdict


@dataclass
class TradingStats:
    """Trading statistics data class"""
    # Time info
    timestamp: datetime
    period: str  # 'daily', 'weekly', 'monthly', 'all_time'
    
    # Trade counts
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    
    # Financial metrics
    total_pnl: float = 0.0
    total_winning_pnl: float = 0.0
    total_losing_pnl: float = 0.0
    total_fees: float = 0.0
    net_pnl: float = 0.0
    
    # Performance metrics
    win_rate: float = 0.0
    average_win: float = 0.0
    average_loss: float = 0.0
    profit_factor: float = 0.0
    sharpe_ratio: float = 0.0
    max_drawdown: float = 0.0
    
    # Risk metrics
    largest_win: float = 0.0
    largest_loss: float = 0.0
    average_risk_reward: float = 0.0
    
    # Volume metrics
    total_volume_traded: float = 0.0
    average_position_size: float = 0.0
    
    def calculate_metrics(self) -> None:
        """Calculate derived metrics"""
        if self.total_trades > 0:
            self.win_rate = (self.winning_trades / self.total_trades) * 100
            self.net_pnl = self.total_pnl - self.total_fees
            
        if self.winning_trades > 0:
            self.average_win = self.total_winning_pnl / self.winning_trades

        if self.losing_trades > 0:
            self.average_loss = abs(self.total_losing_pnl) / self.losing_trades

        if self.average_loss > 0:
            self.profit_factor = self.average_win / self.average_loss


class StatisticsTracker:
    """Track and persist trading statistics"""
    
    def __init__(self, data_dir: str = 'data'):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # File paths
        self.trades_file = self.data_dir / 'trades.csv'
        self.positions_file = self.data_dir / 'positions.csv'
        self.daily_stats_file = self.data_dir / 'daily_stats.csv'
        self.performance_file = self.data_dir / 'performance.json'
        
        # In-memory tracking
        self.current_stats = defaultdict(lambda: TradingStats(
            timestamp=datetime.now(),
            period='current'
        ))
        self.trade_history: List[Dict[str, Any]] = []
        self.position_history: List[Dict[str, Any]] = []
        
        # Initialize files
        self._initialize_files()
        
    def _initialize_files(self) -> None:
        """Initialize CSV files with headers"""
        # Trades CSV
        if not self.trades_file.exists():
            with open(self.trades_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'timestamp', 'symbol', 'side', 'type', 'price', 'amount',
                    'fee', 'pnl', 'pnl_percentage', 'order_id', 'position_id'
                ])
        
        # Positions CSV
        if not self.positions_file.exists():
            with open(self.positions_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'timestamp', 'symbol', 'side', 'entry_price', 'exit_price',
                    'contracts', 'pnl', 'pnl_percentage', 'fees_paid',
                    'duration_minutes', 'max_pnl', 'min_pnl', 'position_id'
                ])
        
        # Daily stats CSV
        if not self.daily_stats_file.exists():
            with open(self.daily_stats_file, 'w', newline='') as f:
                writer = csv.writer(f)
                writer.writerow([
                    'date', 'total_trades', 'winning_trades', 'losing_trades',
                    'total_pnl', 'total_fees', 'net_pnl', 'win_rate',
                    'average_win', 'average_loss', 'profit_factor',
                    'total_volume', 'max_drawdown'
                ])
    
    def record_trade(self, trade_data: Dict[str, Any]) -> None:
        """Record a trade execution"""
        # Add timestamp if not present
        if 'timestamp' not in trade_data:
            trade_data['timestamp'] = datetime.now().isoformat()
        
        # Append to memory
        self.trade_history.append(trade_data)
        
        # Write to CSV
        with open(self.trades_file, 'a', newline='') as f:
            writer = csv.writer(f)
            writer.writerow([
                trade_data.get('timestamp'),
                trade_data.get('symbol'),
                trade_data.get('side'),
                trade_data.get('type'),
                trade_data.get('price'),
                trade_data.get('amount'),
                trade_data.get('fee', 0),
                trade_data.get('pnl', 0),
                trade_data.get('pnl_percentage', 0),
                trade_data.get('order_id'),
                trade_data.get('position_id')
            ])
        
        # Update statistics
        self._update_trade_stats(trade_data)
    
    def record_position_closed(self, position_data: Dict[str, Any]) -> None:
        """Record a closed position"""
        # Calculate duration
        if 'opened_at' in position_data and 'closed_at' in position_data:
            opened = datetime.fromisoformat(position_data['opened_at'])
            closed = datetime.fromisoformat(position_data['closed_at'])
            duration = (closed - opened).total_seconds() / 60
        else:
            duration = 0
        
        # Write to CSV
        with open(self.positions_file, 'a', newline='') as f:
            writer = csv.writer(f)
            writer.writerow([
                position_data.get('closed_at', datetime.now().isoformat()),
                position_data.get('symbol'),
                position_data.get('side'),
                position_data.get('entry_price'),
                position_data.get('exit_price', position_data.get('current_price')),
                position_data.get('contracts'),
                position_data.get('pnl'),
                position_data.get('pnl_percentage'),
                position_data.get('fees_paid', 0),
                duration,
                position_data.get('max_pnl', 0),
                position_data.get('min_pnl', 0),
                position_data.get('position_id')
            ])
        
        # Update position history
        self.position_history.append(position_data)
    
    def _update_trade_stats(self, trade_data: Dict[str, Any]) -> None:
        """Update in-memory statistics"""
        stats = self.current_stats['all_time']
        
        stats.total_trades += 1
        stats.total_volume_traded += trade_data.get('amount', 0) * trade_data.get('price', 0)
        stats.total_fees += trade_data.get('fee', 0)
        
        pnl = trade_data.get('pnl', 0)
        if pnl > 0:
            stats.winning_trades += 1
            stats.largest_win = max(stats.largest_win, pnl)
            stats.total_winning_pnl += pnl
        elif pnl < 0:
            stats.losing_trades += 1
            stats.largest_loss = min(stats.largest_loss, pnl)
            stats.total_losing_pnl += pnl

        stats.total_pnl += pnl
        stats.calculate_metrics()
    
    def save_daily_stats(self) -> None:
        """Save daily statistics snapshot"""
        today = datetime.now().date()
        stats = self.get_period_stats('daily')
        
        with open(self.daily_stats_file, 'a', newline='') as f:
            writer = csv.writer(f)
            writer.writerow([
                today.isoformat(),
                stats.total_trades,
                stats.winning_trades,
                stats.losing_trades,
                stats.total_pnl,
                stats.total_fees,
                stats.net_pnl,
                stats.win_rate,
                stats.average_win,
                stats.average_loss,
                stats.profit_factor,
                stats.total_volume_traded,
                stats.max_drawdown
            ])
    
    def get_period_stats(self, period: str = 'all_time') -> TradingStats:
        """Get statistics for a specific period"""
        if period == 'all_time':
            return self.current_stats['all_time']
        
        # Calculate from trade history
        now = datetime.now()
        if period == 'daily':
            start_time = now - timedelta(days=1)
        elif period == 'weekly':
            start_time = now - timedelta(weeks=1)
        elif period == 'monthly':
            start_time = now - timedelta(days=30)
        else:
            return self.current_stats['all_time']
        
        # Filter trades by period
        period_trades = [
            t for t in self.trade_history
            if datetime.fromisoformat(t['timestamp']) >= start_time
        ]
        
        # Calculate stats for period
        stats = TradingStats(timestamp=now, period=period)
        for trade in period_trades:
            stats.total_trades += 1
            pnl = trade.get('pnl', 0)
            if pnl > 0:
                stats.winning_trades += 1
            elif pnl < 0:
                stats.losing_trades += 1
            stats.total_pnl += pnl
            stats.total_fees += trade.get('fee', 0)
            stats.total_volume_traded += trade.get('amount', 0) * trade.get('price', 0)
        
        stats.calculate_metrics()
        return stats
    
    def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance report"""
        report = {
            'generated_at': datetime.now().isoformat(),
            'all_time': asdict(self.get_period_stats('all_time')),
            'monthly': asdict(self.get_period_stats('monthly')),
            'weekly': asdict(self.get_period_stats('weekly')),
            'daily': asdict(self.get_period_stats('daily')),
            'top_winning_trades': self._get_top_trades(5, 'winning'),
            'top_losing_trades': self._get_top_trades(5, 'losing'),
            'symbol_performance': self._calculate_symbol_performance()
        }
        
        # Save to JSON
        with open(self.performance_file, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        return report
    
    def _get_top_trades(self, count: int, trade_type: str) -> List[Dict[str, Any]]:
        """Get top winning or losing trades"""
        if trade_type == 'winning':
            filtered = [t for t in self.trade_history if t.get('pnl', 0) > 0]
            sorted_trades = sorted(filtered, key=lambda x: x.get('pnl', 0), reverse=True)
        else:
            filtered = [t for t in self.trade_history if t.get('pnl', 0) < 0]
            sorted_trades = sorted(filtered, key=lambda x: x.get('pnl', 0))
        
        return sorted_trades[:count]
    
    def _calculate_symbol_performance(self) -> Dict[str, Dict[str, float]]:
        """Calculate performance by symbol"""
        symbol_stats = defaultdict(lambda: {
            'total_trades': 0,
            'winning_trades': 0,
            'total_pnl': 0.0,
            'total_volume': 0.0,
            'win_rate': 0.0
        })
        
        for trade in self.trade_history:
            symbol = trade.get('symbol', 'UNKNOWN')
            stats = symbol_stats[symbol]
            
            stats['total_trades'] += 1
            stats['total_pnl'] += trade.get('pnl', 0)
            stats['total_volume'] += trade.get('amount', 0) * trade.get('price', 0)
            
            if trade.get('pnl', 0) > 0:
                stats['winning_trades'] += 1
        
        # Calculate win rates
        for symbol, stats in symbol_stats.items():
            if stats['total_trades'] > 0:
                stats['win_rate'] = float((stats['winning_trades'] / stats['total_trades']) * 100)
        
        return dict(symbol_stats)
    
    def load_historical_data(self) -> None:
        """Load historical data from CSV files"""
        # Load trades
        if self.trades_file.exists():
            df = pd.read_csv(self.trades_file)
            self.trade_history = df.to_dict('records')
        
        # Load positions
        if self.positions_file.exists():
            df = pd.read_csv(self.positions_file)
            self.position_history = df.to_dict('records')
        
        # Recalculate statistics
        for trade in self.trade_history:
            self._update_trade_stats(trade) 