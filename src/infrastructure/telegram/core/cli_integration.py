#!/usr/bin/env python3
"""
Direct CLI Integration for Telegram Bot
Eliminates subprocess overhead by importing CLI modules directly
"""

import sys
import os
import asyncio
import logging
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from io import StringIO
from contextlib import redirect_stdout, redirect_stderr

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

# Direct imports of CLI modules
from cli.autotrader_cli import AutoTraderCLI
from cli.credentials_cli import CredentialsCLI
from cli.bot_creator import BotCreator
from utils.container_helper import container_helper
from core.credential_utils import list_profiles, load_credentials, store_credentials


class TelegramCLIIntegration:
    """
    Direct Python CLI integration for Telegram bot
    Eliminates subprocess calls by importing CLI modules directly
    """
    
    def __init__(self):
        self.logger = logging.getLogger('TelegramCLIIntegration')
        self.autotrader_cli = AutoTraderCLI()
        self.credentials_cli = CredentialsCLI()
        self.bot_creator = BotCreator()
        self.container_helper = container_helper
    
    async def execute_autotrader_command(self, command: str, args: List[str] = None) -> Tuple[int, str, str]:
        """
        Execute AutoTrader CLI command directly without subprocess
        
        Args:
            command: CLI command (list, status, stop, restart, remove, logs)
            args: Command arguments
            
        Returns:
            Tuple of (return_code, stdout, stderr)
        """
        if args is None:
            args = []
            
        try:
            # Capture stdout and stderr
            stdout_capture = StringIO()
            stderr_capture = StringIO()
            
            # Execute command in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            
            def run_cli_command():
                try:
                    with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                        if command == 'list':
                            format_type = args[0] if args and args[0] in ['table', 'json'] else 'table'
                            return self.autotrader_cli.list_containers(format_type)
                        elif command == 'status':
                            if not args:
                                raise ValueError("Symbol required for status command")
                            detailed = '--detailed' in args
                            symbol = args[0] if args[0] != '--detailed' else args[1]
                            return self.autotrader_cli.get_container_status(symbol, detailed)
                        elif command == 'logs':
                            if not args:
                                raise ValueError("Symbol required for logs command")
                            symbol = args[0]
                            lines = int(args[1]) if len(args) > 1 and args[1].isdigit() else 50
                            return self.autotrader_cli.get_container_logs(symbol, lines)
                        elif command == 'stop':
                            if not args:
                                raise ValueError("Symbol required for stop command")
                            symbol = args[0]
                            force = '--force' in args
                            return self.autotrader_cli.stop_container(symbol, force)
                        elif command == 'restart':
                            if not args:
                                raise ValueError("Symbol required for restart command")
                            symbol = args[0]
                            force = '--force' in args
                            return self.autotrader_cli.restart_container(symbol, force)
                        elif command == 'remove':
                            if not args:
                                raise ValueError("Symbol required for remove command")
                            symbol = args[0]
                            force = '--force' in args
                            return self.autotrader_cli.remove_container(symbol, force)
                        else:
                            raise ValueError(f"Unknown command: {command}")
                except Exception as e:
                    stderr_capture.write(str(e))
                    return 1
            
            # Run in thread pool to avoid blocking event loop
            return_code = await loop.run_in_executor(None, run_cli_command)
            
            stdout_content = stdout_capture.getvalue()
            stderr_content = stderr_capture.getvalue()
            
            return return_code, stdout_content, stderr_content
            
        except Exception as e:
            self.logger.error(f"Error executing autotrader command {command}: {e}")
            return 1, "", str(e)
    
    async def execute_credentials_command(self, command: str, args: List[str] = None) -> Tuple[int, str, str]:
        """
        Execute Credentials CLI command directly without subprocess
        
        Args:
            command: CLI command (list, store, load, show, delete)
            args: Command arguments
            
        Returns:
            Tuple of (return_code, stdout, stderr)
        """
        if args is None:
            args = []
            
        try:
            stdout_capture = StringIO()
            stderr_capture = StringIO()
            
            loop = asyncio.get_event_loop()
            
            def run_credentials_command():
                try:
                    with redirect_stdout(stdout_capture), redirect_stderr(stderr_capture):
                        if command == 'list':
                            show_details = '--details' in args
                            return self.credentials_cli.list_credentials(show_details)
                        elif command == 'store':
                            if len(args) < 3:
                                raise ValueError("Profile, API key, and API secret required")
                            profile, api_key, api_secret = args[0], args[1], args[2]
                            display_name = args[3] if len(args) > 3 else profile
                            return self.credentials_cli.store_credentials(
                                profile, api_key, api_secret, display_name, interactive=False
                            )
                        elif command == 'load':
                            if not args:
                                raise ValueError("Profile required for load command")
                            profile = args[0]
                            export_env = '--export' in args
                            return self.credentials_cli.load_credentials(profile, export_env)
                        elif command == 'show':
                            if not args:
                                raise ValueError("Profile required for show command")
                            profile = args[0]
                            show_secret = '--show-secret' in args
                            return self.credentials_cli.show_credentials(profile, show_secret)
                        elif command == 'delete':
                            if not args:
                                raise ValueError("Profile required for delete command")
                            profile = args[0]
                            force = '--force' in args
                            return self.credentials_cli.delete_credentials(profile, force)
                        else:
                            raise ValueError(f"Unknown credentials command: {command}")
                except Exception as e:
                    stderr_capture.write(str(e))
                    return 1
            
            return_code = await loop.run_in_executor(None, run_credentials_command)
            
            stdout_content = stdout_capture.getvalue()
            stderr_content = stderr_capture.getvalue()
            
            return return_code, stdout_content, stderr_content
            
        except Exception as e:
            self.logger.error(f"Error executing credentials command {command}: {e}")
            return 1, "", str(e)
    
    async def get_trading_containers(self) -> List[Dict[str, Any]]:
        """
        Get list of trading containers directly using container helper
        
        Returns:
            List of container information dictionaries
        """
        try:
            loop = asyncio.get_event_loop()
            containers = await loop.run_in_executor(
                None, 
                self.container_helper.list_trading_containers
            )
            return containers
        except Exception as e:
            self.logger.error(f"Error getting trading containers: {e}")
            return []
    
    async def get_container_status_direct(self, symbol: str) -> Optional[Dict[str, Any]]:
        """
        Get container status directly using container helper
        
        Args:
            symbol: Trading symbol or container name
            
        Returns:
            Container status dictionary or None if not found
        """
        try:
            loop = asyncio.get_event_loop()
            status = await loop.run_in_executor(
                None,
                self.container_helper.get_container_status,
                symbol
            )
            return status
        except Exception as e:
            self.logger.error(f"Error getting container status for {symbol}: {e}")
            return None


# Global instance for use across handlers
telegram_cli = TelegramCLIIntegration()
