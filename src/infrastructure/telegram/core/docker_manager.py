#!/usr/bin/env python3
"""
Optimized Docker Manager for Telegram Bot
Uses Docker Python API instead of subprocess calls
"""

import asyncio
import logging
from typing import Dict, List, Optional, Tuple, Any
import docker
from docker.errors import DockerException, NotFound, APIError


class TelegramDockerManager:
    """
    Docker operations for Telegram bot
    Uses Docker Python API directly instead of subprocess calls
    """
    
    def __init__(self):
        self.logger = logging.getLogger('TelegramDockerManager')
        self._client = None
    
    @property
    def client(self):
        """Lazy initialization of Docker client"""
        if self._client is None:
            try:
                # Use Docker socket directly (available in container)
                self._client = docker.DockerClient(base_url='unix://var/run/docker.sock')
                # Test connection
                self._client.ping()
            except Exception as e:
                self.logger.error(f"Failed to connect to Docker: {e}")
                raise
        return self._client
    
    async def list_containers(self, filters: Dict[str, Any] = None) -> List[Dict[str, Any]]:
        """
        List Docker containers asynchronously
        
        Args:
            filters: Docker filters (e.g., {'status': 'running'})
            
        Returns:
            List of container information dictionaries
        """
        try:
            loop = asyncio.get_event_loop()
            
            def _list_containers():
                containers = self.client.containers.list(all=True, filters=filters)
                return [
                    {
                        'id': container.id,
                        'name': container.name,
                        'status': container.status,
                        'image': container.image.tags[0] if container.image.tags else 'unknown',
                        'created': container.attrs['Created'],
                        'ports': container.ports,
                        'labels': container.labels
                    }
                    for container in containers
                ]
            
            return await loop.run_in_executor(None, _list_containers)
            
        except Exception as e:
            self.logger.error(f"Error listing containers: {e}")
            return []
    
    async def get_container_info(self, name_or_id: str) -> Optional[Dict[str, Any]]:
        """
        Get detailed container information
        
        Args:
            name_or_id: Container name or ID
            
        Returns:
            Container information dictionary or None if not found
        """
        try:
            loop = asyncio.get_event_loop()
            
            def _get_container():
                try:
                    container = self.client.containers.get(name_or_id)
                    return {
                        'id': container.id,
                        'name': container.name,
                        'status': container.status,
                        'image': container.image.tags[0] if container.image.tags else 'unknown',
                        'created': container.attrs['Created'],
                        'started_at': container.attrs['State'].get('StartedAt'),
                        'finished_at': container.attrs['State'].get('FinishedAt'),
                        'exit_code': container.attrs['State'].get('ExitCode'),
                        'ports': container.ports,
                        'labels': container.labels,
                        'env': container.attrs['Config'].get('Env', []),
                        'mounts': container.attrs.get('Mounts', [])
                    }
                except NotFound:
                    return None
            
            return await loop.run_in_executor(None, _get_container)
            
        except Exception as e:
            self.logger.error(f"Error getting container info for {name_or_id}: {e}")
            return None
    
    async def stop_container(self, name_or_id: str, timeout: int = 10) -> Tuple[bool, str]:
        """
        Stop a container
        
        Args:
            name_or_id: Container name or ID
            timeout: Stop timeout in seconds
            
        Returns:
            Tuple of (success, message)
        """
        try:
            loop = asyncio.get_event_loop()
            
            def _stop_container():
                try:
                    container = self.client.containers.get(name_or_id)
                    container.stop(timeout=timeout)
                    return True, f"Container {name_or_id} stopped successfully"
                except NotFound:
                    return False, f"Container {name_or_id} not found"
                except APIError as e:
                    return False, f"Failed to stop container: {e}"
            
            return await loop.run_in_executor(None, _stop_container)
            
        except Exception as e:
            self.logger.error(f"Error stopping container {name_or_id}: {e}")
            return False, str(e)
    
    async def restart_container(self, name_or_id: str, timeout: int = 10) -> Tuple[bool, str]:
        """
        Restart a container
        
        Args:
            name_or_id: Container name or ID
            timeout: Restart timeout in seconds
            
        Returns:
            Tuple of (success, message)
        """
        try:
            loop = asyncio.get_event_loop()
            
            def _restart_container():
                try:
                    container = self.client.containers.get(name_or_id)
                    container.restart(timeout=timeout)
                    return True, f"Container {name_or_id} restarted successfully"
                except NotFound:
                    return False, f"Container {name_or_id} not found"
                except APIError as e:
                    return False, f"Failed to restart container: {e}"
            
            return await loop.run_in_executor(None, _restart_container)
            
        except Exception as e:
            self.logger.error(f"Error restarting container {name_or_id}: {e}")
            return False, str(e)
    
    async def remove_container(self, name_or_id: str, force: bool = False) -> Tuple[bool, str]:
        """
        Remove a container
        
        Args:
            name_or_id: Container name or ID
            force: Force removal even if running
            
        Returns:
            Tuple of (success, message)
        """
        try:
            loop = asyncio.get_event_loop()
            
            def _remove_container():
                try:
                    container = self.client.containers.get(name_or_id)
                    container.remove(force=force)
                    return True, f"Container {name_or_id} removed successfully"
                except NotFound:
                    return False, f"Container {name_or_id} not found"
                except APIError as e:
                    return False, f"Failed to remove container: {e}"
            
            return await loop.run_in_executor(None, _remove_container)
            
        except Exception as e:
            self.logger.error(f"Error removing container {name_or_id}: {e}")
            return False, str(e)
    
    async def get_container_logs(self, name_or_id: str, lines: int = 50) -> Tuple[bool, str]:
        """
        Get container logs
        
        Args:
            name_or_id: Container name or ID
            lines: Number of lines to retrieve
            
        Returns:
            Tuple of (success, logs)
        """
        try:
            loop = asyncio.get_event_loop()
            
            def _get_logs():
                try:
                    container = self.client.containers.get(name_or_id)
                    logs = container.logs(tail=lines, timestamps=True).decode('utf-8', errors='ignore')
                    return True, logs
                except NotFound:
                    return False, f"Container {name_or_id} not found"
                except APIError as e:
                    return False, f"Failed to get logs: {e}"
            
            return await loop.run_in_executor(None, _get_logs)
            
        except Exception as e:
            self.logger.error(f"Error getting logs for {name_or_id}: {e}")
            return False, str(e)
    
    async def create_container(self, image: str, name: str, **kwargs) -> Tuple[bool, str]:
        """
        Create and start a new container
        
        Args:
            image: Docker image name
            name: Container name
            **kwargs: Additional container options
            
        Returns:
            Tuple of (success, container_id_or_error)
        """
        try:
            loop = asyncio.get_event_loop()
            
            def _create_container():
                try:
                    container = self.client.containers.run(
                        image=image,
                        name=name,
                        detach=True,
                        **kwargs
                    )
                    return True, container.id
                except APIError as e:
                    return False, f"Failed to create container: {e}"
            
            return await loop.run_in_executor(None, _create_container)
            
        except Exception as e:
            self.logger.error(f"Error creating container {name}: {e}")
            return False, str(e)
    
    def close(self):
        """Close Docker client connection"""
        if self._client:
            self._client.close()
            self._client = None


# Global instance for use across handlers
docker_manager = TelegramDockerManager()
