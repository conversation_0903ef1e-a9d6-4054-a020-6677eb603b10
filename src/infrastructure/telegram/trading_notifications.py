#!/usr/bin/env python3
"""
Trading Notifications System for Telegram Bot
Handles real-time notifications for trades, positions, and bot status
"""

import asyncio
import logging
import json
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path

try:
    from telegram import Bo<PERSON>
    from telegram.constants import ParseMode
    TELEGRAM_AVAILABLE = True
except ImportError:
    TELEGRAM_AVAILABLE = False


class TradingNotificationManager:
    """Manages trading notifications and alerts"""
    
    def __init__(self, bot_token: str):
        self.logger = logging.getLogger('TradingNotificationManager')
        self.bot_token = bot_token
        self.bot = None
        
        # Notification settings
        self.notifications_enabled = True
        self.notification_types = {
            'trade_opened': True,
            'trade_closed': True,
            'position_update': True,
            'bot_status': True,
            'error_alerts': True,
            'profit_alerts': True
        }
        
        # Alert thresholds
        self.alert_thresholds = {
            'profit_threshold': 5.0,  # %
            'loss_threshold': -3.0,   # %
            'position_size_threshold': 1000.0  # USDT
        }
        
        if TELEGRAM_AVAILABLE:
            self.bot = Bot(token=bot_token)
    
    async def initialize(self):
        """Initialize the notification manager"""
        if not TELEGRAM_AVAILABLE:
            self.logger.warning("Telegram not available, notifications disabled")
            return False
        
        try:
            # Test bot connection
            bot_info = await self.bot.get_me()
            self.logger.info(f"Notification bot initialized: @{bot_info.username}")
            return True
        except Exception as e:
            self.logger.error(f"Failed to initialize notification bot: {e}")
            return False
    
    async def send_trade_notification(self, trade_data: Dict):
        """Send notification for trade execution"""
        if not self.notifications_enabled or not self.notification_types.get('trade_opened'):
            return
        
        try:
            message = self._format_trade_message(trade_data)
            await self._send_message(message)
        except Exception as e:
            self.logger.error(f"Failed to send trade notification: {e}")
    
    async def send_position_update(self, position_data: Dict):
        """Send notification for position updates"""
        if not self.notifications_enabled or not self.notification_types.get('position_update'):
            return
        
        try:
            message = self._format_position_message(position_data)
            await self._send_message(message)
        except Exception as e:
            self.logger.error(f"Failed to send position update: {e}")
    
    async def send_bot_status_alert(self, status_data: Dict):
        """Send notification for bot status changes"""
        if not self.notifications_enabled or not self.notification_types.get('bot_status'):
            return
        
        try:
            message = self._format_status_message(status_data)
            await self._send_message(message)
        except Exception as e:
            self.logger.error(f"Failed to send status alert: {e}")
    
    async def send_error_alert(self, error_data: Dict):
        """Send notification for errors"""
        if not self.notifications_enabled or not self.notification_types.get('error_alerts'):
            return
        
        try:
            message = self._format_error_message(error_data)
            await self._send_message(message)
        except Exception as e:
            self.logger.error(f"Failed to send error alert: {e}")
    
    async def send_profit_alert(self, profit_data: Dict):
        """Send notification for profit milestones"""
        if not self.notifications_enabled or not self.notification_types.get('profit_alerts'):
            return
        
        profit_pct = profit_data.get('profit_percentage', 0)
        if profit_pct >= self.alert_thresholds['profit_threshold']:
            try:
                message = self._format_profit_message(profit_data)
                await self._send_message(message)
            except Exception as e:
                self.logger.error(f"Failed to send profit alert: {e}")
    
    def _format_trade_message(self, trade_data: Dict) -> str:
        """Format trade execution message"""
        symbol = trade_data.get('symbol', 'Unknown')
        side = trade_data.get('side', 'Unknown')
        amount = trade_data.get('amount', 0)
        price = trade_data.get('price', 0)
        timestamp = trade_data.get('timestamp', datetime.now().strftime('%H:%M:%S'))
        
        emoji = "🟢" if side.upper() == "BUY" else "🔴"
        
        message = f"{emoji} <b>Trade Executed</b>\n\n"
        message += f"📊 <b>Symbol:</b> `{symbol}`\n"
        message += f"📈 <b>Side:</b> {side.upper()}\n"
        message += f"💰 <b>Amount:</b> ${amount:.2f}\n"
        message += f"💲 <b>Price:</b> ${price:.6f}\n"
        message += f"⏰ <b>Time:</b> {timestamp}"
        
        return message
    
    def _format_position_message(self, position_data: Dict) -> str:
        """Format position update message"""
        symbol = position_data.get('symbol', 'Unknown')
        side = position_data.get('side', 'Unknown')
        size = position_data.get('size', 0)
        unrealized_pnl = position_data.get('unrealized_pnl', 0)
        pnl_percentage = position_data.get('pnl_percentage', 0)
        
        emoji = "📈" if unrealized_pnl >= 0 else "📉"
        pnl_emoji = "🟢" if unrealized_pnl >= 0 else "🔴"
        
        message = f"{emoji} <b>Position Update</b>\n\n"
        message += f"📊 <b>Symbol:</b> `{symbol}`\n"
        message += f"📍 <b>Side:</b> {side.upper()}\n"
        message += f"📏 <b>Size:</b> ${size:.2f}\n"
        message += f"{pnl_emoji} <b>PnL:</b> ${unrealized_pnl:.2f} ({pnl_percentage:+.2f}%)"
        
        return message
    
    def _format_status_message(self, status_data: Dict) -> str:
        """Format bot status message"""
        bot_name = status_data.get('bot_name', 'Trading Bot')
        status = status_data.get('status', 'Unknown')
        message_text = status_data.get('message', '')
        
        emoji = "✅" if status == "running" else "❌" if status == "error" else "⚠️"
        
        message = f"{emoji} <b>Bot Status Update</b>\n\n"
        message += f"🤖 <b>Bot:</b> {bot_name}\n"
        message += f"📊 <b>Status:</b> {status.upper()}\n"
        
        if message_text:
            message += f"💬 <b>Message:</b> {message_text}"
        
        return message
    
    def _format_error_message(self, error_data: Dict) -> str:
        """Format error alert message"""
        error_type = error_data.get('type', 'Unknown Error')
        error_message = error_data.get('message', 'No details available')
        bot_name = error_data.get('bot_name', 'Trading Bot')
        timestamp = error_data.get('timestamp', datetime.now().strftime('%H:%M:%S'))
        
        message = f"🚨 <b>Error Alert</b>\n\n"
        message += f"🤖 <b>Bot:</b> {bot_name}\n"
        message += f"❌ <b>Error:</b> {error_type}\n"
        message += f"💬 <b>Details:</b> {error_message}\n"
        message += f"⏰ <b>Time:</b> {timestamp}"
        
        return message
    
    def _format_profit_message(self, profit_data: Dict) -> str:
        """Format profit milestone message"""
        profit_amount = profit_data.get('profit_amount', 0)
        profit_percentage = profit_data.get('profit_percentage', 0)
        total_trades = profit_data.get('total_trades', 0)
        win_rate = profit_data.get('win_rate', 0)
        
        message = f"🎉 <b>Profit Milestone!</b>\n\n"
        message += f"💰 <b>Profit:</b> ${profit_amount:.2f} ({profit_percentage:+.2f}%)\n"
        message += f"📊 <b>Trades:</b> {total_trades}\n"
        message += f"🎯 <b>Win Rate:</b> {win_rate:.1f}%\n"
        message += f"🚀 <b>Keep it up!</b>"
        
        return message
    
    async def _send_message(self, message: str, chat_id: int = None):
        """Send message to Telegram"""
        if not self.bot or not chat_id:
            return

        try:
            await self.bot.send_message(
                chat_id=chat_id,
                text=message,
                parse_mode=ParseMode.MARKDOWN
            )
        except Exception as e:
            self.logger.error(f"Failed to send Telegram message: {e}")
    
    def enable_notifications(self, notification_type: str = None):
        """Enable notifications"""
        if notification_type:
            self.notification_types[notification_type] = True
        else:
            self.notifications_enabled = True
    
    def disable_notifications(self, notification_type: str = None):
        """Disable notifications"""
        if notification_type:
            self.notification_types[notification_type] = False
        else:
            self.notifications_enabled = False
    
    def set_alert_threshold(self, threshold_type: str, value: float):
        """Set alert threshold"""
        if threshold_type in self.alert_thresholds:
            self.alert_thresholds[threshold_type] = value


# Example usage and testing
async def test_notifications():
    """Test notification system"""
    import os
    
    token = os.environ.get('TELEGRAM_BOT_TOKEN')
    chat_id = os.environ.get('TELEGRAM_CHAT_ID')
    
    if not token or not chat_id:
        print("❌ Missing TELEGRAM_BOT_TOKEN or TELEGRAM_CHAT_ID")
        return
    
    manager = TradingNotificationManager(token, int(chat_id))
    
    if await manager.initialize():
        print("✅ Notification manager initialized")
        
        # Test trade notification
        await manager.send_trade_notification({
            'symbol': 'BTC/USDT',
            'side': 'BUY',
            'amount': 100.0,
            'price': 45000.0,
            'timestamp': datetime.now().strftime('%H:%M:%S')
        })
        
        print("✅ Test notification sent")
    else:
        print("❌ Failed to initialize notification manager")


if __name__ == "__main__":
    asyncio.run(test_notifications())
