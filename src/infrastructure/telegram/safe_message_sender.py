"""
Safe message sending utilities for Telegram bot with automatic fallback handling.
Prevents entity parsing errors from crashing the bot.
"""

import re
import logging
from typing import Callable, Any, Optional
from telegram.constants import ParseMode


class SafeMessageSender:
    """Utility class for sending Telegram messages with automatic error handling and fallback."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
    
    async def send_safe_message(self, send_func: Callable, text: str, 
                               parse_mode: Optional[str] = None, **kwargs) -> Any:
        """
        Send message with automatic fallback for parsing errors.
        
        Args:
            send_func: The Telegram send function (e.g., update.message.reply_text)
            text: Message text to send
            parse_mode: Parse mode (MARKDOWN, HTML, or None)
            **kwargs: Additional arguments for the send function
            
        Returns:
            Result from the send function
            
        Raises:
            Exception: Re-raises non-parsing related errors
        """
        try:
            # Log outgoing message
            self.logger.info(f"📤 SENDING MESSAGE: {text[:100]}{'...' if len(text) > 100 else ''}")

            # First attempt with specified parse_mode
            result = await send_func(text=text, parse_mode=parse_mode, **kwargs)

            # Log successful send
            self.logger.info(f"✅ MESSAGE SENT SUCCESSFULLY")
            return result
            
        except Exception as e:
            error_str = str(e).lower()
            
            # Check if it's a parsing error
            if self._is_parsing_error(error_str):
                self.logger.warning(f"Message parsing failed, retrying with plain text: {e}")
                
                # Strip formatting and retry with plain text
                plain_text = self._strip_formatting(text)
                try:
                    return await send_func(text=plain_text, parse_mode=None, **kwargs)
                except Exception as e2:
                    self.logger.error(f"Plain text fallback also failed: {e2}")
                    
                    # Last resort: send simple error message
                    try:
                        return await send_func(
                            text="❌ Có lỗi xảy ra khi gửi tin nhắn", 
                            parse_mode=None, 
                            **kwargs
                        )
                    except Exception as e3:
                        self.logger.error(f"Even simple message failed: {e3}")
                        pass  # Give up completely
            else:
                # Re-raise non-parsing errors
                self.logger.error(f"Non-parsing error in message sending: {e}")
                raise
    
    def _is_parsing_error(self, error_str: str) -> bool:
        """Check if the error is related to message parsing."""
        parsing_error_phrases = [
            "can't parse entities",
            "parse error",
            "bad request",
            "entity",
            "markdown",
            "html",
            "can't find end of the entity",
            "byte offset",
            "invalid entity"
        ]
        
        return any(phrase in error_str for phrase in parsing_error_phrases)
    
    def _strip_formatting(self, text: str) -> str:
        """
        Strip Markdown and HTML formatting from text.
        
        Args:
            text: Text with potential formatting
            
        Returns:
            Plain text without formatting
        """
        # Remove Markdown formatting
        text = re.sub(r'\*\*(.*?)\*\*', r'\1', text)  # **bold**
        text = re.sub(r'\*(.*?)\*', r'\1', text)      # *italic*
        text = re.sub(r'`(.*?)`', r'\1', text)        # `code`
        text = re.sub(r'```(.*?)```', r'\1', text, flags=re.DOTALL)  # ```code block```
        text = re.sub(r'__(.*?)__', r'\1', text)      # __underline__
        text = re.sub(r'~~(.*?)~~', r'\1', text)      # ~~strikethrough~~
        text = re.sub(r'\[(.*?)\]\(.*?\)', r'\1', text)  # [text](link)
        
        # Remove HTML formatting
        text = re.sub(r'<b>(.*?)</b>', r'\1', text)
        text = re.sub(r'<i>(.*?)</i>', r'\1', text)
        text = re.sub(r'<code>(.*?)</code>', r'\1', text)
        text = re.sub(r'<pre>(.*?)</pre>', r'\1', text, flags=re.DOTALL)
        text = re.sub(r'<[^>]+>', '', text)  # Remove any other HTML tags

        # Escape remaining HTML characters for safety
        text = text.replace('&', '&amp;').replace('<', '&lt;').replace('>', '&gt;')

        # Clean up extra whitespace
        text = re.sub(r'\n\s*\n', '\n\n', text)  # Multiple newlines to double newline
        text = text.strip()

        return text
    
    async def send_safe_reply(self, message, text: str, 
                             parse_mode: Optional[str] = ParseMode.MARKDOWN, **kwargs) -> Any:
        """Convenience method for replying to messages safely."""
        return await self.send_safe_message(message.reply_text, text, parse_mode, **kwargs)
    
    async def edit_safe_message(self, query, text: str, 
                               parse_mode: Optional[str] = ParseMode.MARKDOWN, **kwargs) -> Any:
        """Convenience method for editing messages safely."""
        return await self.send_safe_message(query.edit_message_text, text, parse_mode, **kwargs)


# Global instance for easy import
safe_sender = SafeMessageSender()


# Convenience functions for backward compatibility
async def send_safe_message(send_func: Callable, text: str, 
                           parse_mode: Optional[str] = None, **kwargs) -> Any:
    """Global function for safe message sending."""
    return await safe_sender.send_safe_message(send_func, text, parse_mode, **kwargs)


async def send_safe_reply(message, text: str, 
                         parse_mode: Optional[str] = ParseMode.MARKDOWN, **kwargs) -> Any:
    """Global function for safe message replies."""
    return await safe_sender.send_safe_reply(message, text, parse_mode, **kwargs)


async def edit_safe_message(query, text: str, 
                           parse_mode: Optional[str] = ParseMode.MARKDOWN, **kwargs) -> Any:
    """Global function for safe message editing."""
    return await safe_sender.edit_safe_message(query, text, parse_mode, **kwargs)
