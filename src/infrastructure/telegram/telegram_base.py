"""Base module for Telegram infrastructure with shared utilities"""
import asyncio
import logging
import subprocess
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from abc import ABC, abstractmethod

from telegram import InlineKeyboardButton, InlineKeyboardMarkup
from telegram.constants import ParseMode


class TelegramBaseHandler(ABC):
    """Base class for all Telegram handlers"""
    
    def __init__(self, logger_name: str):
        self.logger = logging.getLogger(logger_name)
    

    
    def parse_telegram_args(self, args: List[str]) -> List[str]:
        """Convert Telegram command args to bot.sh format"""
        botsh_args = []
        
        i = 0
        while i < len(args):
            arg = args[i]
            
            # Handle key=value format from Telegram
            if '=' in arg:
                key, value = arg.split('=', 1)
                botsh_args.extend([f"--{key}", value])
            else:
                # Assume it's a flag or positional argument
                if arg.startswith('--'):
                    botsh_args.append(arg)
                else:
                    # Convert common patterns
                    if arg in ['test', 'test_mode', 'testmode']:
                        botsh_args.append('--test-mode')
                    elif arg in ['debug']:
                        botsh_args.append('--debug')
                    else:
                        # Assume it's a value or symbol
                        botsh_args.append(arg)
            
            i += 1
        
        return botsh_args
    
    async def send_message(self, bot, chat_id: int, text: str, 
                          parse_mode=ParseMode.MARKDOWN, reply_markup=None) -> bool:
        """Send message with error handling"""
        try:
            await bot.send_message(
                chat_id=chat_id,
                text=text,
                parse_mode=parse_mode,
                reply_markup=reply_markup
            )
            return True
        except Exception as e:
            self.logger.error(f"Error sending message: {e}")
            return False
    
    async def edit_message(self, query, text: str, 
                          parse_mode=ParseMode.MARKDOWN, reply_markup=None) -> bool:
        """Edit message with error handling"""
        try:
            await query.edit_message_text(
                text=text,
                parse_mode=parse_mode,
                reply_markup=reply_markup
            )
            return True
        except Exception as e:
            self.logger.error(f"Error editing message: {e}")
            return False
    
    def create_keyboard(self, buttons: List[List[Tuple[str, str]]]) -> InlineKeyboardMarkup:
        """Create inline keyboard from button definitions"""
        keyboard = []
        for row in buttons:
            keyboard_row = []
            for text, callback_data in row:
                keyboard_row.append(InlineKeyboardButton(text, callback_data=callback_data))
            keyboard.append(keyboard_row)
        return InlineKeyboardMarkup(keyboard)
    
    def format_error_message(self, error: str, command: str = "") -> str:
        """Format error message consistently"""
        msg = f"❌ <b>Error</b>\n\n"
        if command:
            msg += f"Command: <code>{command}</code>\n"
        msg += f"Error: {error}"
        return msg

    def format_success_message(self, message: str, details: str = "") -> str:
        """Format success message consistently"""
        msg = f"✅ <b>Success</b>\n\n{message}"
        if details:
            msg += f"\n\n<pre>{details}</pre>"
        return msg
    
    @abstractmethod
    async def handle_command(self, update, context) -> None:
        """Handle command - to be implemented by subclasses"""
        pass


class UserSessionManager:
    """Manages user sessions for wizards and multi-step operations"""
    
    def __init__(self):
        self.sessions: Dict[int, Dict] = {}
        self.logger = logging.getLogger('UserSessionManager')
    
    def get_session(self, user_id: int) -> Dict:
        """Get or create user session"""
        if user_id not in self.sessions:
            self.sessions[user_id] = {
                'wizard_state': None,
                'wizard_data': {},
                'last_activity': datetime.now(),
                'current_command': None
            }
        
        # Update last activity
        self.sessions[user_id]['last_activity'] = datetime.now()
        return self.sessions[user_id]
    
    def clear_session(self, user_id: int):
        """Clear user session"""
        if user_id in self.sessions:
            del self.sessions[user_id]
    
    def is_wizard_active(self, user_id: int) -> bool:
        """Check if user has active wizard"""
        session = self.get_session(user_id)
        return session.get('wizard_state') is not None
    
    def start_wizard(self, user_id: int, wizard_type: str, initial_data: Optional[Dict] = None):
        """Start a wizard for user"""
        session = self.get_session(user_id)
        session['wizard_state'] = wizard_type
        session['wizard_data'] = initial_data if initial_data is not None else {}
        session['current_command'] = wizard_type
    
    def update_wizard_data(self, user_id: int, key: str, value: Any):
        """Update wizard data"""
        session = self.get_session(user_id)
        session['wizard_data'][key] = value
    
    def get_wizard_data(self, user_id: int, key: Optional[str] = None):
        """Get wizard data"""
        session = self.get_session(user_id)
        if key:
            return session['wizard_data'].get(key)
        return session['wizard_data']
    
    def finish_wizard(self, user_id: int):
        """Finish wizard and clear wizard state"""
        session = self.get_session(user_id)
        session['wizard_state'] = None
        session['wizard_data'] = {}
        session['current_command'] = None

    def clear_wizard(self, user_id: int):
        """Clear wizard state (alias for finish_wizard)"""
        self.finish_wizard(user_id)

    # Additional methods for compatibility with base_handler
    def is_in_wizard(self, user_id: int) -> bool:
        """Check if user is in a wizard (alias for is_wizard_active)"""
        return self.is_wizard_active(user_id)

    def get_wizard_step(self, user_id: int) -> Optional[str]:
        """Get current wizard step"""
        session = self.get_session(user_id)
        return session.get('wizard_state')

    def set_wizard_step(self, user_id: int, step: str):
        """Set wizard step"""
        session = self.get_session(user_id)
        session['wizard_state'] = step
        session['current_command'] = step

    def get_session_data(self, user_id: int, key: str, default=None):
        """Get data from user session"""
        session = self.get_session(user_id)
        return session['wizard_data'].get(key, default)

    def set_session_data(self, user_id: int, key: str, value):
        """Set data in user session"""
        session = self.get_session(user_id)
        session['wizard_data'][key] = value

    def cleanup_old_sessions(self, max_age_hours: int = 24):
        """Clean up old inactive sessions"""
        cutoff_time = datetime.now().timestamp() - (max_age_hours * 3600)
        
        to_remove = []
        for user_id, session in self.sessions.items():
            if session['last_activity'].timestamp() < cutoff_time:
                to_remove.append(user_id)
        
        for user_id in to_remove:
            del self.sessions[user_id]
            self.logger.info(f"Cleaned up old session for user {user_id}")


class MessageFormatter:
    """Utility class for formatting Telegram messages"""

    @staticmethod
    def code_block(text: str) -> str:
        """Format text as code block"""
        return f"<pre>{text}</pre>"

    @staticmethod
    def inline_code(text: str) -> str:
        """Format text as inline code"""
        return f"<code>{text}</code>"

    @staticmethod
    def bold(text: str) -> str:
        """Format text as bold"""
        return f"<b>{text}</b>"

    @staticmethod
    def italic(text: str) -> str:
        """Format text as italic"""
        return f"<i>{text}</i>"
    
    @staticmethod
    def create_list(items: List[str], numbered: bool = False) -> str:
        """Create formatted list"""
        if numbered:
            return "\n".join(f"{i+1}. {item}" for i, item in enumerate(items))
        else:
            return "\n".join(f"• {item}" for item in items)
    
    @staticmethod
    def create_table(headers: List[str], rows: List[List[str]]) -> str:
        """Create simple table format"""
        table = " | ".join(headers) + "\n"
        table += "-" * len(table) + "\n"
        for row in rows:
            table += " | ".join(row) + "\n"
        return table
    
    @staticmethod
    def status_emoji(status: str) -> str:
        """Get emoji for status"""
        status_map = {
            'running': '🟢',
            'stopped': '🔴',
            'starting': '🟡',
            'error': '❌',
            'success': '✅',
            'warning': '⚠️',
            'info': 'ℹ️'
        }
        return status_map.get(status.lower(), '❓')


class ValidationUtils:
    """Utility class for input validation with security hardening"""

    # Security: Define allowed characters to prevent injection
    ALLOWED_SYMBOL_CHARS = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-')
    ALLOWED_PROFILE_CHARS = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789_-')
    SHELL_METACHARACTERS = set(';&|`$(){}[]<>*?~!#^"\'\\')

    @staticmethod
    def sanitize_input(input_str: str) -> str:
        """Sanitize input by removing dangerous characters"""
        if not input_str:
            return ""

        # Remove shell metacharacters
        sanitized = ''.join(c for c in input_str if c not in ValidationUtils.SHELL_METACHARACTERS)

        # Limit length to prevent buffer overflow
        return sanitized[:100].strip()

    @staticmethod
    def validate_api_key(api_key: str) -> bool:
        """Validate API key format with security checks"""
        if not api_key or len(api_key) < 10 or len(api_key) > 100:
            return False

        # Security: Check for shell metacharacters
        if any(c in ValidationUtils.SHELL_METACHARACTERS for c in api_key):
            return False

        # Allow alphanumeric and some safe special characters
        allowed_chars = set('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789-_')
        return all(c in allowed_chars for c in api_key)

    @staticmethod
    def validate_symbol(symbol: str) -> bool:
        """Validate trading symbol format with security checks"""
        if not symbol or len(symbol) < 2 or len(symbol) > 20:
            return False

        # Security: Check for shell metacharacters
        if any(c in ValidationUtils.SHELL_METACHARACTERS for c in symbol):
            return False

        # Accept both simple format (hyper, btc) and full format (HYPER/USDT:USDT)
        if "/" in symbol:
            # Full format validation - allow : for futures symbols
            allowed_chars = ValidationUtils.ALLOWED_SYMBOL_CHARS | {'/', ':'}
            return all(c in allowed_chars for c in symbol) and len(symbol) >= 3
        else:
            # Simple format validation
            return all(c in ValidationUtils.ALLOWED_SYMBOL_CHARS for c in symbol)

    @staticmethod
    def validate_profile_name(profile: str) -> bool:
        """Validate profile name with security checks"""
        if not profile or len(profile) < 1 or len(profile) > 50:
            return False

        # Security: Check for shell metacharacters and path traversal
        if any(c in ValidationUtils.SHELL_METACHARACTERS for c in profile):
            return False

        # Prevent path traversal
        if '..' in profile or '/' in profile or '\\' in profile:
            return False

        # Only allow safe characters
        return all(c in ValidationUtils.ALLOWED_PROFILE_CHARS for c in profile)

    @staticmethod
    def validate_amount(amount: str) -> bool:
        """Validate trading amount with security checks"""
        if not amount or len(amount) > 20:
            return False

        # Security: Check for shell metacharacters
        if any(c in ValidationUtils.SHELL_METACHARACTERS for c in amount):
            return False

        try:
            value = float(amount)
            return 0 < value <= 1000000  # Reasonable limits
        except ValueError:
            return False

    @staticmethod
    def validate_container_name(name: str) -> bool:
        """Validate Docker container name with security checks"""
        if not name or len(name) < 1 or len(name) > 63:
            return False

        # Docker container name rules + security
        # Must start with alphanumeric, can contain alphanumeric, hyphens, underscores
        allowed_chars = set('abcdefghijklmnopqrstuvwxyz0123456789-_')

        # Must start with alphanumeric
        if not name[0].isalnum():
            return False

        return all(c in allowed_chars for c in name.lower())
    
    @staticmethod
    def validate_percentage(percentage: str) -> bool:
        """Validate percentage value"""
        try:
            value = float(percentage)
            return 0 <= value <= 100
        except ValueError:
            return False
