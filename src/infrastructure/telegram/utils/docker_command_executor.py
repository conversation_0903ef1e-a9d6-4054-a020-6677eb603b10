#!/usr/bin/env python3
"""
Centralized Docker Command Executor for Telegram Bot
Eliminates duplicate security validation methods
"""

import asyncio
import logging
from typing import List, Tuple
from ..telegram_base import ValidationUtils


class DockerCommandExecutor:
    """
    Centralized Docker command executor with security validation
    Prevents duplicate security check methods across handlers
    """
    
    def __init__(self, logger_name: str = 'DockerCommandExecutor'):
        self.logger = logging.getLogger(logger_name)
    
    async def execute_docker_cmd(self, cmd: List[str]) -> Tuple[int, str, str]:
        """
        Execute docker command with comprehensive security validation
        
        Args:
            cmd: Docker command as list of strings
            
        Returns:
            Tuple of (return_code, stdout, stderr)
        """
        try:
            # Security: Validate command structure
            if not cmd or len(cmd) == 0:
                raise ValueError("Empty command not allowed")

            # Security: Only allow docker commands
            if cmd[0] != "docker":
                raise ValueError(f"Only docker commands allowed, got: {cmd[0]}")

            # Security: Validate all command arguments
            for arg in cmd:
                if not isinstance(arg, str):
                    raise ValueError(f"Invalid argument type: {type(arg)}")

                # Check for shell metacharacters in arguments
                if any(c in ValidationUtils.SHELL_METACHARACTERS for c in arg):
                    raise ValueError(f"Dangerous characters in argument: {arg}")

                # Limit argument length
                if len(arg) > 200:
                    raise ValueError(f"Argument too long: {len(arg)} chars")

            # Security: Log the command for audit
            safe_cmd = ' '.join(cmd)
            self.logger.info(f"Executing docker command: {safe_cmd}")

            # Run docker command asynchronously
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd="/app"
            )

            stdout, stderr = await process.communicate()

            return (
                process.returncode,
                stdout.decode('utf-8', errors='ignore').strip(),
                stderr.decode('utf-8', errors='ignore').strip()
            )

        except Exception as e:
            self.logger.error(f"Error executing docker command {' '.join(cmd) if cmd else 'None'}: {e}")
            return 1, "", str(e)
    
    def validate_container_name(self, name: str) -> bool:
        """
        Validate container name for security
        
        Args:
            name: Container name to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not name or len(name) == 0:
            return False
            
        # Container names must be alphanumeric with hyphens/underscores
        import re
        if not re.match(r'^[a-zA-Z0-9][a-zA-Z0-9_.-]*$', name):
            return False
            
        # Reasonable length limit
        if len(name) > 63:  # Docker limit
            return False
            
        return True
    
    def validate_image_name(self, image: str) -> bool:
        """
        Validate Docker image name for security
        
        Args:
            image: Image name to validate
            
        Returns:
            True if valid, False otherwise
        """
        if not image or len(image) == 0:
            return False
            
        # Basic image name validation
        import re
        # Allow registry/namespace/image:tag format
        if not re.match(r'^[a-zA-Z0-9][a-zA-Z0-9._/-]*[a-zA-Z0-9](?::[a-zA-Z0-9._-]+)?$', image):
            return False
            
        # Reasonable length limit
        if len(image) > 255:
            return False
            
        return True


# Global instance for use across handlers
docker_executor = DockerCommandExecutor()
