#!/usr/bin/env python3
"""Credential management handler for Telegram bot."""

import asyncio
import logging
from typing import Dict, List, Optional, Any, Tuple

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, ForceReply
from telegram.constants import ParseMode

from .base_handler import BaseTelegramHandler
from ....core.credential_utils import list_profiles, load_credentials, store_credentials
from ..auth import require_auth


class CredentialHandler(BaseTelegramHandler):
    """Handler for credential management operations."""

    async def handle_command(self, update, context) -> None:
        """Handle command - required by abstract base class"""
        pass
    
    @require_auth("USER")
    async def handle_listcreds(self, update: Update, context) -> None:
        """Handle /listcreds command using centralized utilities"""
        self.logger.info(f"Received /listcreds command from user {update.effective_user.id}")

        try:
            # Use centralized credential utilities
            profiles = list_profiles()
            
            if not profiles:
                await update.message.reply_text(
                    "📋 Không có tài khoản nào\n\n"
                    "Sử dụng /addcreds để thêm tài khoản mới.",
                    parse_mode=None
                )
                return

            # Format profiles list without markdown
            message = "📋 Danh sách tài khoản:\n\n"
            for profile in profiles:
                display_name = profile.get('display_name', profile['profile'])
                message += f"🔑 {profile['profile']}\n"
                message += f"   Tên hiển thị: {display_name}\n"
                message += f"   Định dạng: {profile.get('format', 'json')}\n\n"

            message += f"📊 Tổng cộng: {len(profiles)} tài khoản"

            await update.message.reply_text(message, parse_mode=None)

        except Exception as e:
            self.logger.error(f"Error in handle_listcreds: {e}")
            await self._send_error_message(update, str(e))
    
    @require_auth("USER")
    async def handle_addcreds_wizard(self, update: Update, context) -> None:
        """Handle /addcreds command - start credential wizard"""
        user_id = update.effective_user.id
        
        # Clear any existing wizard state
        self._clear_wizard(user_id)
        
        # Start credential wizard
        self._set_wizard_step(user_id, 'addcreds_profile')
        
        await update.message.reply_text(
            "🔑 **Thêm tài khoản mới**\n\n"
            "Nhập tên profile (ví dụ: main, test, backup):",
            reply_markup=ForceReply(selective=True),
            parse_mode=ParseMode.MARKDOWN
        )
    
    @require_auth("USER")
    async def handle_addcreds_wizard_step(self, update: Update, context) -> None:
        """Handle steps in add credentials wizard"""
        user_id = update.effective_user.id
        step = self._get_wizard_step(user_id)
        text = update.message.text.strip()
        
        if step == 'addcreds_profile':
            # Security: Validate profile name thoroughly
            if not text or not self._validate_profile_name(text):
                await self._send_error_message(update, "Profile name không hợp lệ hoặc không an toàn")
                return

            # Security: Sanitize profile name
            sanitized_profile = self._sanitize_input(text)

            # Check if profile already exists
            profiles = list_profiles()
            if any(p['profile'] == sanitized_profile for p in profiles):
                await self._send_error_message(update, f"Profile '{sanitized_profile}' đã tồn tại")
                return

            # Save profile name and move to next step
            self._set_session_data(user_id, 'addcreds_profile', sanitized_profile)
            self._set_wizard_step(user_id, 'addcreds_api_key')
            
            await update.message.reply_text(
                f"✅ Profile: `{text}`\n\n"
                "Nhập API Key:",
                reply_markup=ForceReply(selective=True),
                parse_mode=ParseMode.MARKDOWN
            )
        
        elif step == 'addcreds_api_key':
            # Security: Validate API key thoroughly
            if not self._validate_api_key(text):
                await self._send_error_message(update, "API Key không hợp lệ hoặc chứa ký tự không an toàn")
                return

            # Security: Sanitize API key
            sanitized_api_key = self._sanitize_input(text)

            # Save API key and move to next step
            self._set_session_data(user_id, 'addcreds_api_key', sanitized_api_key)
            self._set_wizard_step(user_id, 'addcreds_api_secret')
            
            await update.message.reply_text(
                "✅ API Key đã lưu\n\n"
                "Nhập API Secret:",
                reply_markup=ForceReply(selective=True),
                parse_mode=ParseMode.MARKDOWN
            )
        
        elif step == 'addcreds_api_secret':
            # Security: Validate API secret thoroughly
            if not self._validate_api_key(text):  # Use same validation as API key
                await self._send_error_message(update, "API Secret không hợp lệ hoặc chứa ký tự không an toàn")
                return

            # Security: Sanitize API secret
            sanitized_api_secret = self._sanitize_input(text)
            
            # Save API secret and move to next step
            self._set_session_data(user_id, 'addcreds_api_secret', sanitized_api_secret)
            self._set_wizard_step(user_id, 'addcreds_display_name')
            
            await update.message.reply_text(
                "✅ API Secret đã lưu\n\n"
                "Nhập tên hiển thị (hoặc gửi 'skip' để bỏ qua):",
                reply_markup=ForceReply(selective=True),
                parse_mode=ParseMode.MARKDOWN
            )
        
        elif step == 'addcreds_display_name':
            # Handle display name (optional)
            display_name = None if text.lower() == 'skip' else text
            
            # Get all saved data
            profile = self._get_session_data(user_id, 'addcreds_profile')
            api_key = self._get_session_data(user_id, 'addcreds_api_key')
            api_secret = self._get_session_data(user_id, 'addcreds_api_secret')
            
            try:
                # Store credentials using centralized utility
                store_credentials(profile, api_key, api_secret, display_name)
                
                # Clear wizard state
                self._clear_wizard(user_id)
                
                await self._send_success_message(
                    update, 
                    f"Tài khoản '{profile}' đã được thêm thành công!"
                )
                
            except Exception as e:
                self.logger.error(f"Error storing credentials: {e}")
                await self._send_error_message(update, f"Không thể lưu tài khoản: {str(e)}")
                self._clear_wizard(user_id)
    
    @require_auth("USER")
    async def handle_deletecreds(self, update: Update, context) -> None:
        """Handle /deletecreds command - show list of credentials to delete"""
        try:
            # Get all available profiles
            profiles = list_profiles()

            if not profiles:
                await self._send_error_message(update, "Không có tài khoản nào để xóa")
                return

            # Create keyboard with profile options
            keyboard = []
            for profile in profiles:
                display_name = profile.get('display_name', profile['profile'])
                profile_name = profile['profile']
                keyboard.append([
                    InlineKeyboardButton(
                        f"🗑️ {display_name} ({profile_name})",
                        callback_data=f"delete_creds_select_{profile_name}"
                    )
                ])

            # Add cancel button
            keyboard.append([
                InlineKeyboardButton("❌ Hủy", callback_data="delete_creds_cancel")
            ])

            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                "🗑️ **Xóa tài khoản**\n\n"
                "Chọn tài khoản bạn muốn xóa:\n"
                "⚠️ *Hành động này không thể hoàn tác!*",
                reply_markup=reply_markup,
                parse_mode=ParseMode.MARKDOWN
            )

        except Exception as e:
            self.logger.error(f"Error in handle_deletecreds: {e}")
            await self._send_error_message(update, str(e))
    
    async def handle_delete_creds_callback(self, query, data: str) -> None:
        """Handle credential deletion confirmation"""
        try:
            if data.startswith("delete_creds_select_"):
                # User selected a profile to delete - show confirmation
                profile = data.replace("delete_creds_select_", "")

                # Get profile info for display
                profiles = list_profiles()
                profile_info = next((p for p in profiles if p['profile'] == profile), None)

                if not profile_info:
                    await query.edit_message_text(
                        f"❌ Profile '{profile}' không tồn tại",
                        parse_mode=None
                    )
                    return

                display_name = profile_info.get('display_name', profile)

                # Create confirmation keyboard
                keyboard = [
                    [
                        InlineKeyboardButton("✅ Xác nhận xóa", callback_data=f"delete_creds_confirm_{profile}"),
                        InlineKeyboardButton("❌ Hủy", callback_data="delete_creds_cancel")
                    ]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)

                await query.edit_message_text(
                    f"⚠️ **Xác nhận xóa tài khoản**\n\n"
                    f"**Tên hiển thị:** {display_name}\n"
                    f"**Profile:** {profile}\n\n"
                    f"Bạn có chắc muốn xóa tài khoản này?\n"
                    f"*Hành động này không thể hoàn tác!*",
                    reply_markup=reply_markup,
                    parse_mode=ParseMode.MARKDOWN
                )

            elif data.startswith("delete_creds_confirm_"):
                # User confirmed deletion
                profile = data.replace("delete_creds_confirm_", "")

                # Import delete function
                from ....core.credential_utils import delete_profile

                # Delete credential using utility function
                success = delete_profile(profile)

                if success:
                    await query.edit_message_text(
                        f"✅ **Đã xóa tài khoản thành công**\n\n"
                        f"Profile '{profile}' đã được xóa khỏi hệ thống.",
                        parse_mode=ParseMode.MARKDOWN
                    )
                else:
                    await query.edit_message_text(
                        f"❌ **Lỗi xóa tài khoản**\n\n"
                        f"Không thể xóa profile '{profile}'. Vui lòng thử lại.",
                        parse_mode=ParseMode.MARKDOWN
                    )

            elif data == "delete_creds_cancel":
                await query.edit_message_text(
                    "❌ **Đã hủy xóa tài khoản**",
                    parse_mode=ParseMode.MARKDOWN
                )
            else:
                await query.edit_message_text("❌ Callback không hợp lệ")

        except Exception as e:
            self.logger.error(f"Error deleting credentials: {e}")
            await query.edit_message_text(f"❌ Lỗi: {str(e)}")
    
    @require_auth("USER")
    async def handle_showcreds(self, update: Update, context) -> None:
        """Handle /showcreds command with masked API secret"""
        if not context.args:
            await self._send_error_message(update, "Sử dụng: /showcreds <profile_name>")
            return

        profile = context.args[0]

        try:
            # Use centralized credential utilities to get masked data
            from src.core.credential_utils import load_credentials

            cred_data = load_credentials(profile)
            if not cred_data:
                await self._send_error_message(update, f"Profile '{profile}' không tồn tại")
                return

            # Format credentials with masked API secret
            api_key = cred_data['api_key']
            api_secret = cred_data['api_secret']

            # Mask API secret for security
            masked_key = f"{api_key[:8]}...{api_key[-4:] if len(api_key) > 12 else '***'}"
            masked_secret = f"{api_secret[:4]}{'*' * 16}{api_secret[-4:] if len(api_secret) > 8 else '***'}"

            message = f"🔐 Credential Details: {profile}\n\n"
            message += f"📝 Display Name: {cred_data.get('display_name', profile)}\n"
            message += f"🔑 API Key: {masked_key}\n"
            message += f"🔒 API Secret: {masked_secret}\n"
            message += f"📅 Created: {cred_data.get('created', 'Unknown')}\n"
            message += f"📊 Version: {cred_data.get('version', '1.0')}"

            await update.message.reply_text(message, parse_mode=None)

        except Exception as e:
            self.logger.error(f"Error in handle_showcreds: {e}")
            await self._send_error_message(update, str(e))

    @require_auth("USER")
    async def handle_setkey(self, update: Update, context) -> None:
        """Handle /setkey command for quick credential setup"""
        if len(context.args) < 2:
            await self._send_error_message(update,
                "Sử dụng: /setkey <api_key> <api_secret> [profile_name]\n"
                "Ví dụ: /setkey your_api_key your_secret main"
            )
            return

        try:
            api_key = context.args[0]
            api_secret = context.args[1]
            profile_name = context.args[2] if len(context.args) > 2 else "main"

            # Security: Validate all inputs thoroughly
            if not self._validate_api_key(api_key):
                await self._send_error_message(update, "API key không hợp lệ hoặc chứa ký tự không an toàn")
                return

            if not self._validate_api_key(api_secret):
                await self._send_error_message(update, "API secret không hợp lệ hoặc chứa ký tự không an toàn")
                return

            if not self._validate_profile_name(profile_name):
                await self._send_error_message(update, "Profile name không hợp lệ hoặc không an toàn")
                return

            # Security: Sanitize all inputs
            sanitized_api_key = self._sanitize_input(api_key)
            sanitized_api_secret = self._sanitize_input(api_secret)
            sanitized_profile = self._sanitize_input(profile_name)

            # Store credentials using direct CLI integration
            result = await self.execute_cli_command("store-credentials", [
                sanitized_profile, sanitized_api_key, sanitized_api_secret, f"Quick setup via /setkey"
            ])

            if result[0] == 0:
                await self._send_success_message(update,
                    f"Đã lưu tài khoản '{sanitized_profile}' thành công!\n"
                    f"Sử dụng /listcreds để xem tất cả tài khoản."
                )
            else:
                await self._send_error_message(update, result[2] or result[1])

        except Exception as e:
            self.logger.error(f"Error in handle_setkey: {e}")
            await self._send_error_message(update, str(e))

    async def _send_unauthorized_message(self, update: Update) -> None:
        """Send unauthorized access message to user"""
        try:
            rejection_msg = self.auth_service.get_rejection_message()

            # Add user info for admin reference
            user = update.effective_user
            user_info = f"\n\n🔍 **User Info:**\n" \
                       f"• ID: `{user.id}`\n" \
                       f"• Username: @{user.username or 'N/A'}\n" \
                       f"• Name: {user.first_name or ''} {user.last_name or ''}".strip()

            full_message = rejection_msg + user_info

            if update.message:
                await update.message.reply_text(full_message, parse_mode=ParseMode.MARKDOWN)
            elif update.callback_query:
                await update.callback_query.answer(rejection_msg, show_alert=True)

        except Exception as e:
            self.logger.error(f"Error sending unauthorized message: {e}")
