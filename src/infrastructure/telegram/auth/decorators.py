"""
Authorization decorators for Telegram bot commands
"""

import functools
import logging
from typing import Callable, Any
from telegram import Update
from telegram.ext import ContextTypes


def require_auth(required_role: str = "USER"):
    """
    Decorator to require authentication for Telegram bot commands
    
    Args:
        required_role: Minimum required role (USER, ADMIN)
        
    Usage:
        @require_auth("USER")
        async def handle_command(self, update: Update, context):
            # Command logic here
            
        @require_auth("ADMIN") 
        async def handle_admin_command(self, update: Update, context):
            # Admin-only logic here
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(self, update: Update, context: ContextTypes.DEFAULT_TYPE, *args, **kwargs) -> Any:
            # Get user info from update
            user = update.effective_user
            user_id = user.id if user else None
            username = user.username if user else None

            if user_id is None:
                logging.warning("No user ID found in update")
                return

            # Check authorization
            if not hasattr(self, 'auth_service'):
                logging.error("Handler missing auth_service attribute")
                await self._send_error_message(update, "❌ Lỗi hệ thống: <PERSON>hông thể xác thực người dùng")
                return

            if not self.auth_service.is_authorized(user_id, required_role, username):
                await self._send_unauthorized_message(update)
                return

            # User is authorized, execute the command
            return await func(self, update, context, *args, **kwargs)
        
        return wrapper
    return decorator


def require_auth_callback(required_role: str = "USER"):
    """
    Decorator to require authentication for callback query handlers
    
    Args:
        required_role: Minimum required role (USER, ADMIN)
        
    Usage:
        @require_auth_callback("USER")
        async def handle_callback(self, query):
            # Callback logic here
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(self, query, *args, **kwargs) -> Any:
            # Get user info from callback query
            user = query.from_user
            user_id = user.id if user else None
            username = user.username if user else None

            if user_id is None:
                logging.warning("No user ID found in callback query")
                await query.answer("❌ Lỗi xác thực", show_alert=True)
                return

            # Check authorization
            if not hasattr(self, 'auth_service'):
                logging.error("Handler missing auth_service attribute")
                await query.answer("❌ Lỗi hệ thống", show_alert=True)
                return

            if not self.auth_service.is_authorized(user_id, required_role, username):
                rejection_msg = self.auth_service.get_rejection_message()
                await query.answer(rejection_msg, show_alert=True)
                return

            # User is authorized, execute the callback
            return await func(self, query, *args, **kwargs)
        
        return wrapper
    return decorator
