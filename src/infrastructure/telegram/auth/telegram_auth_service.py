"""
Telegram Bot Authorization Service
Handles user authentication and authorization for Telegram bot commands.
"""

import json
import os
import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from enum import Enum
import threading


class UserRole(Enum):
    """User permission roles"""
    SUPER_ADMIN = "SUPER_ADMIN"  # Only creator can CRUD users
    ADMIN = "ADMIN"              # Can use all commands but not manage users
    USER = "USER"                # Basic trading commands
    BLOCKED = "BLOCKED"          # Explicitly blocked users


class TelegramAuthService:
    """
    Service for managing Telegram bot user authorization.
    Provides secure access control with role-based permissions.
    """
    
    def __init__(self, config_path: str = None):
        # Default to ~/.autotrader/telegram_auth.json if no path provided
        if config_path is None:
            home_dir = os.path.expanduser("~")
            config_path = os.path.join(home_dir, ".autotrader", "telegram_auth.json")

        self.config_path = config_path
        self.logger = logging.getLogger(__name__)
        self._lock = threading.Lock()
        self._config = None
        self._super_admin_id = None  # Will be set when first SUPER_ADMIN user is detected
        self._load_config()
    
    def _load_config(self) -> None:
        """Load authorization config from file"""
        try:
            with self._lock:
                if os.path.exists(self.config_path):
                    with open(self.config_path, 'r', encoding='utf-8') as f:
                        self._config = json.load(f)
                    self.logger.info(f"Loaded authorization config from {self.config_path}")
                else:
                    self._create_default_config()
                    self.logger.warning(f"Created default authorization config at {self.config_path}")
        except Exception as e:
            self.logger.error(f"Error loading auth config: {e}")
            self._create_default_config()
    
    def _create_default_config(self) -> None:
        """Create default authorization config - empty initially"""
        self._config = {
            "authorized_users": {
                # No default users - will be added when first user interacts
            },
            "settings": {
                "default_rejection_message": "❌ Bạn không có quyền sử dụng bot này. Liên hệ @hoangtrungdev để được cấp quyền.",
                "super_admin_only_commands": ["/adduser", "/removeuser", "/listusers"],
                "creator_username": "hoangtrungdev",  # Only this user can CRUD
                "audit_log": True,
                "strict_mode": True,
                "auto_promote_creator": True  # Auto-promote creator to SUPER_ADMIN
            }
        }
        self._save_config()
    
    def _save_config(self) -> bool:
        """Save authorization config to file"""
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.config_path), exist_ok=True)

            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self._config, f, indent=2, ensure_ascii=False)

            self.logger.info("Authorization config saved successfully")
            return True
        except Exception as e:
            self.logger.error(f"Error saving auth config: {e}")
            return False

    def _save_config_with_lock(self) -> bool:
        """Save authorization config to file with lock"""
        try:
            with self._lock:
                return self._save_config()
        except Exception as e:
            self.logger.error(f"Error saving auth config with lock: {e}")
            return False
    
    def is_authorized(self, user_id: int, required_role: str = "USER", user_username: str = None) -> bool:
        """
        Check if user is authorized with required role

        Args:
            user_id: Telegram user ID
            required_role: Minimum required role (USER, ADMIN, SUPER_ADMIN)
            user_username: Username for auto-promotion check

        Returns:
            bool: True if authorized, False otherwise
        """
        try:
            user_id_str = str(user_id)

            # Auto-promote creator to SUPER_ADMIN if enabled
            if self._should_auto_promote_creator(user_id, user_username):
                self._auto_promote_creator(user_id, user_username)

            # Check if user exists in authorized list
            if user_id_str not in self._config.get("authorized_users", {}):
                self._log_auth_attempt(user_id, "DENIED", "User not in authorized list")
                return False

            user_info = self._config["authorized_users"][user_id_str]
            user_role = user_info.get("role", "USER")

            # Check if user is blocked
            if user_role == UserRole.BLOCKED.value:
                self._log_auth_attempt(user_id, "BLOCKED", "User is explicitly blocked")
                return False

            # Check role hierarchy: SUPER_ADMIN > ADMIN > USER
            role_hierarchy = {
                UserRole.USER.value: 1,
                UserRole.ADMIN.value: 2,
                UserRole.SUPER_ADMIN.value: 3
            }

            user_level = role_hierarchy.get(user_role, 0)
            required_level = role_hierarchy.get(required_role, 1)

            authorized = user_level >= required_level

            if authorized:
                self._log_auth_attempt(user_id, "GRANTED", f"Role: {user_role}, Required: {required_role}")
            else:
                self._log_auth_attempt(user_id, "DENIED", f"Insufficient role: {user_role}, Required: {required_role}")

            return authorized

        except Exception as e:
            self.logger.error(f"Error checking authorization for user {user_id}: {e}")
            return False
    
    def get_user_info(self, user_id: int) -> Optional[Dict]:
        """Get user information"""
        try:
            user_id_str = str(user_id)
            return self._config.get("authorized_users", {}).get(user_id_str)
        except Exception as e:
            self.logger.error(f"Error getting user info for {user_id}: {e}")
            return None
    
    def add_user(self, user_id: int, username: str, role: str, added_by: int) -> Tuple[bool, str]:
        """
        Add user to authorized list (SUPER_ADMIN only)

        Args:
            user_id: Telegram user ID to add
            username: Username for reference
            role: User role (USER, ADMIN) - SUPER_ADMIN cannot be assigned
            added_by: User ID of SUPER_ADMIN adding this user

        Returns:
            Tuple[bool, str]: (success, message)
        """
        try:
            # Only SUPER_ADMIN can add users
            if not self._is_super_admin(added_by):
                return False, "❌ Chỉ creator (@hoangtrungdev) mới có thể thêm users."

            # Validate role - SUPER_ADMIN cannot be assigned manually
            if role not in [UserRole.USER.value, UserRole.ADMIN.value]:
                return False, f"❌ Invalid role: {role}. Must be USER or ADMIN."

            user_id_str = str(user_id)

            # Check if user already exists
            if user_id_str in self._config.get("authorized_users", {}):
                existing_role = self._config["authorized_users"][user_id_str].get("role")
                return False, f"❌ User {username} ({user_id}) already exists with role {existing_role}"

            # Add user
            with self._lock:
                if "authorized_users" not in self._config:
                    self._config["authorized_users"] = {}

                self._config["authorized_users"][user_id_str] = {
                    "username": username,
                    "role": role,
                    "added_by": str(added_by),
                    "added_at": datetime.now().isoformat()
                }

                if self._save_config():
                    self.logger.info(f"User {username} ({user_id}) added with role {role} by {added_by}")
                    return True, f"✅ User {username} added successfully with role {role}"
                else:
                    return False, "❌ Failed to save configuration"

        except Exception as e:
            self.logger.error(f"Error adding user {user_id}: {e}")
            return False, f"❌ Error adding user: {str(e)}"
    
    def remove_user(self, user_id: int, removed_by: int) -> Tuple[bool, str]:
        """
        Remove user from authorized list (SUPER_ADMIN only)

        Args:
            user_id: User ID to remove
            removed_by: User ID of SUPER_ADMIN removing this user

        Returns:
            Tuple[bool, str]: (success, message)
        """
        try:
            # Only SUPER_ADMIN can remove users
            if not self._is_super_admin(removed_by):
                return False, "❌ Chỉ creator (@hoangtrungdev) mới có thể xóa users."

            user_id_str = str(user_id)

            # Check if user exists
            if user_id_str not in self._config.get("authorized_users", {}):
                return False, f"❌ User {user_id} not found in authorized list"

            # Prevent removing yourself (SUPER_ADMIN)
            if user_id == removed_by:
                return False, "❌ Cannot remove yourself from authorized list"

            # Prevent removing other SUPER_ADMINs
            user_info = self._config["authorized_users"][user_id_str]
            if user_info.get("role") == UserRole.SUPER_ADMIN.value:
                return False, "❌ Cannot remove SUPER_ADMIN users"

            username = user_info.get("username", "unknown")

            # Remove user
            with self._lock:
                del self._config["authorized_users"][user_id_str]

                if self._save_config():
                    self.logger.info(f"User {username} ({user_id}) removed by {removed_by}")
                    return True, f"✅ User {username} removed successfully"
                else:
                    return False, "❌ Failed to save configuration"

        except Exception as e:
            self.logger.error(f"Error removing user {user_id}: {e}")
            return False, f"❌ Error removing user: {str(e)}"
    
    def list_users(self) -> List[Dict]:
        """List all authorized users"""
        try:
            users = []
            for user_id, user_info in self._config.get("authorized_users", {}).items():
                users.append({
                    "user_id": int(user_id),
                    "username": user_info.get("username", "unknown"),
                    "role": user_info.get("role", "USER"),
                    "added_by": user_info.get("added_by", "unknown"),
                    "added_at": user_info.get("added_at", "unknown")
                })
            return users
        except Exception as e:
            self.logger.error(f"Error listing users: {e}")
            return []
    
    def get_rejection_message(self) -> str:
        """Get default rejection message for unauthorized users"""
        return self._config.get("settings", {}).get(
            "default_rejection_message", 
            "❌ Bạn không có quyền sử dụng bot này."
        )
    
    def _log_auth_attempt(self, user_id: int, result: str, details: str) -> None:
        """Log authorization attempts for audit"""
        if self._config.get("settings", {}).get("audit_log", True):
            self.logger.info(f"AUTH {result}: User {user_id} - {details}")

    def _should_auto_promote_creator(self, user_id: int, username: str) -> bool:
        """Check if user should be auto-promoted to SUPER_ADMIN"""
        if not username:
            return False

        settings = self._config.get("settings", {})
        creator_username = settings.get("creator_username", "hoangtrungdev")
        auto_promote = settings.get("auto_promote_creator", True)

        # Check if this is the creator and auto-promotion is enabled
        if auto_promote and username.lower() == creator_username.lower():
            user_id_str = str(user_id)
            # Only promote if user doesn't exist or isn't already SUPER_ADMIN
            if user_id_str not in self._config.get("authorized_users", {}):
                return True

            existing_role = self._config["authorized_users"][user_id_str].get("role")
            return existing_role != UserRole.SUPER_ADMIN.value

        return False

    def _auto_promote_creator(self, user_id: int, username: str) -> bool:
        """Auto-promote creator to SUPER_ADMIN"""
        try:
            user_id_str = str(user_id)

            with self._lock:
                if "authorized_users" not in self._config:
                    self._config["authorized_users"] = {}

                self._config["authorized_users"][user_id_str] = {
                    "username": username,
                    "role": UserRole.SUPER_ADMIN.value,
                    "added_by": "auto_promotion",
                    "added_at": datetime.now().isoformat()
                }

                self._super_admin_id = user_id

                if self._save_config():
                    self.logger.info(f"Auto-promoted creator {username} ({user_id}) to SUPER_ADMIN")
                    return True
                else:
                    return False

        except Exception as e:
            self.logger.error(f"Error auto-promoting creator {user_id}: {e}")
            return False

    def _is_super_admin(self, user_id: int) -> bool:
        """Check if user is SUPER_ADMIN"""
        user_id_str = str(user_id)
        user_info = self._config.get("authorized_users", {}).get(user_id_str)
        return user_info and user_info.get("role") == UserRole.SUPER_ADMIN.value
