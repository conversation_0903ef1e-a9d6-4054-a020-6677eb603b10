"""Advanced logging system for trading bot"""
import logging
import sys
from pathlib import Path
from datetime import datetime
from typing import Optional, Any, Dict
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler
import json


class TradeLogger:
    """Custom logger for trading operations"""
    
    def __init__(
        self, 
        name: str = 'TradingBot',
        log_dir: str = 'logs',
        log_level: str = 'INFO',
        max_bytes: int = 10 * 1024 * 1024,  # 10MB
        backup_count: int = 5,
        console_enabled: bool = True
    ):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # Create logger
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        self.logger.handlers.clear()
        
        # Setup handlers
        if console_enabled:
            self._setup_console_handler()
        self._setup_file_handler(max_bytes, backup_count)
        self._setup_trade_handler()
        self._setup_error_handler()
        
    def _setup_console_handler(self) -> None:
        """Setup console output with colors"""
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # Color formatter
        formatter = ColoredFormatter(
            '[%(asctime)s] | %(levelname)-8s | %(name)s | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(formatter)
        self.logger.addHandler(console_handler)
    
    def _setup_file_handler(self, max_bytes: int, backup_count: int) -> None:
        """Setup rotating file handler for general logs"""
        log_file = self.log_dir / f'{self.name.lower()}.log'
        
        file_handler = RotatingFileHandler(
            log_file,
            maxBytes=max_bytes,
            backupCount=backup_count
        )
        file_handler.setLevel(logging.DEBUG)
        
        formatter = logging.Formatter(
            '[%(asctime)s] | %(levelname)-8s | %(name)s | %(funcName)s:%(lineno)d | %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        self.logger.addHandler(file_handler)
    
    def _setup_trade_handler(self) -> None:
        """Setup separate handler for trade logs"""
        trade_log_file = self.log_dir / 'trades.jsonl'
        
        self.trade_handler = logging.FileHandler(trade_log_file)
        self.trade_handler.setLevel(logging.INFO)
        self.trade_handler.addFilter(TradeFilter())
        
        # JSON formatter for trades
        formatter = logging.Formatter('%(message)s')
        self.trade_handler.setFormatter(formatter)
        self.logger.addHandler(self.trade_handler)
    
    def _setup_error_handler(self) -> None:
        """Setup separate handler for errors"""
        error_log_file = self.log_dir / 'errors.log'
        
        error_handler = TimedRotatingFileHandler(
            error_log_file,
            when='midnight',
            interval=1,
            backupCount=30
        )
        error_handler.setLevel(logging.ERROR)
        
        formatter = logging.Formatter(
            '[%(asctime)s] | %(levelname)s | %(name)s | %(funcName)s:%(lineno)d\n'
            'Message: %(message)s\n'
            'Exception: %(exc_info)s\n'
            '-' * 80,
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        error_handler.setFormatter(formatter)
        self.logger.addHandler(error_handler)
    
    def log_trade(self, trade_data: Dict[str, Any]) -> None:
        """Log trade data in structured format"""
        trade_data['timestamp'] = datetime.now().isoformat()
        trade_data['log_type'] = 'trade'
        
        # Log as JSON for analysis
        self.logger.info(json.dumps(trade_data), extra={'is_trade': True})
    
    def log_signal(self, signal_data: Dict[str, Any]) -> None:
        """Log trading signal"""
        signal_data['timestamp'] = datetime.now().isoformat()
        signal_data['log_type'] = 'signal'
        
        self.logger.info(f"Signal generated: {signal_data['symbol']} - {signal_data['direction']}")
        self.logger.debug(json.dumps(signal_data))
    
    def log_order(self, order_data: Dict[str, Any]) -> None:
        """Log order execution"""
        order_data['timestamp'] = datetime.now().isoformat()
        order_data['log_type'] = 'order'
        
        self.logger.info(
            f"Order {order_data.get('action', 'executed')}: "
            f"{order_data['symbol']} {order_data['side']} "
            f"{order_data['amount']} @ {order_data['price']}"
        )
        self.logger.debug(json.dumps(order_data))
    
    def log_position(self, position_data: Dict[str, Any]) -> None:
        """Log position update"""
        self.logger.info(
            f"Position update: {position_data['symbol']} "
            f"PnL: {position_data['pnl']:.2f} ({position_data['pnl_percentage']:.2f}%)"
        )
    
    def log_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> None:
        """Log error with context"""
        error_data = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'context': context or {},
            'timestamp': datetime.now().isoformat()
        }
        
        self.logger.error(
            f"Error occurred: {error}",
            exc_info=True,
            extra={'error_data': error_data}
        )
    
    def get_logger(self) -> logging.Logger:
        """Get the underlying logger"""
        return self.logger


class ColoredFormatter(logging.Formatter):
    """Formatter with colors for console output"""
    
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
    }
    RESET = '\033[0m'
    
    def format(self, record: logging.LogRecord) -> str:
        """Apply color to log level"""
        if record.levelname in self.COLORS:
            record.levelname = f"{self.COLORS[record.levelname]}{record.levelname}{self.RESET}"
        return super().format(record)


class TradeFilter(logging.Filter):
    """Filter to only allow trade logs"""
    
    def filter(self, record: logging.LogRecord) -> bool:
        """Only allow records with is_trade flag"""
        return getattr(record, 'is_trade', False) 