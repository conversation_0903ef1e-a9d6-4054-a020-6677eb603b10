"""Configuration manager for loading from environment and files"""
import os
import json
import logging
from typing import Any, Dict, Optional
from pathlib import Path
from src.core.models import TradeConfig


class ConfigManager:
    """Manages configuration from multiple sources"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        # Check environment variable first, then parameter, then default
        self.config_file = config_file or os.environ.get('BOT_CONFIG_FILE') or 'configs/config.json'
        self.logger.info(f"Using config file: {self.config_file}")
        self._config_cache: Optional[TradeConfig] = None
        self._raw_config: Dict[str, Any] = {}
        
    def load_config(self, force_reload: bool = False) -> TradeConfig:
        """Load configuration with caching"""
        if self._config_cache and not force_reload:
            return self._config_cache
            
        # Load from file first
        self._load_from_file()
        
        # Override with environment variables
        self._load_from_environment()
        
        # Validate configuration
        self._validate_config()
        
        # Create TradeConfig object
        self._config_cache = TradeConfig.from_dict(self._raw_config)
        
        self.logger.info(f"Configuration loaded successfully for {self._config_cache.symbol}")
        return self._config_cache
    
    def _load_from_file(self) -> None:
        """Load configuration from JSON file"""
        config_path = Path(self.config_file)
        
        if not config_path.exists():
            self.logger.warning(f"Config file {self.config_file} not found, using defaults")
            return
            
        try:
            with open(config_path, 'r') as f:
                self._raw_config = json.load(f)
            self.logger.info(f"Loaded configuration from {self.config_file}")
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in config file: {e}")
        except Exception as e:
            raise RuntimeError(f"Error loading config file: {e}")
    
    def _load_from_environment(self) -> None:
        """Load and override configuration from environment variables"""
        # Get exchange name from config (default to 'bybit' if not set)
        exchange_config = self._raw_config.get('exchange', 'bybit')
        
        # Handle both string and dict formats for exchange
        if isinstance(exchange_config, dict):
            exchange_name = exchange_config.get('name', 'bybit').upper()
        elif isinstance(exchange_config, str):
            exchange_name = exchange_config.upper()
        else:
            exchange_name = 'BYBIT'
        
        # API credentials (required from environment) - dynamic based on exchange
        api_key_var = f'{exchange_name}_API_KEY'
        api_secret_var = f'{exchange_name}_API_SECRET'
        
        api_key = os.environ.get(api_key_var)
        api_secret = os.environ.get(api_secret_var)
        
        if not api_key or not api_secret:
            if not self._raw_config.get('use_test_mode', True):
                raise ValueError(f"API credentials must be set in environment variables: {api_key_var}, {api_secret_var}")
        
        # Store credentials separately (not in config)
        self._api_credentials = {
            'api_key': api_key,
            'api_secret': api_secret,
            'exchange': exchange_name,
            'api_key_var': api_key_var,
            'api_secret_var': api_secret_var
        }
        
        # Override config values from environment
        env_mappings = {
            'TRADE_SYMBOL': 'symbol',
            'TRADE_EXCHANGE': 'exchange',
            'TRADE_DIRECTION': 'direction',
            'TRADE_AMOUNT': ('amount', float),
            'TRADE_TEST_MODE': ('use_test_mode', lambda x: x.lower() == 'true'),
            'TEST_MODE': ('use_test_mode', lambda x: x.lower() == 'true'),
            'SANDBOX_MODE': ('use_sandbox', lambda x: x.lower() == 'true'),
            'LOG_LEVEL': 'log_level',
            'TRADE_TIMEFRAME': 'indicators.primary_timeframe',
            'TRADE_MAX_POSITION': ('risk.max_position_size', float),
            'TRADE_STOP_LOSS': ('risk.default_stop_loss', float),
            'TRADE_TAKE_PROFIT': ('risk.default_take_profit', float),
            'MIN_POSITION_SIZE': ('risk.min_position_size', float),
            'MAX_POSITION_SIZE': ('risk.max_position_size', float),
            'MAX_POSITION_PERCENTAGE': ('risk.max_position_percentage', float),
            'DAILY_LOSS_LIMIT': ('risk.daily_loss_limit', float),
            'RISK_PER_TRADE': ('risk.risk_per_trade', float),
        }
        
        for env_var, config_key in env_mappings.items():
            value = os.environ.get(env_var)
            if value is not None:
                if isinstance(config_key, tuple):
                    key, converter = config_key
                    value = converter(value)
                    self._set_nested_config(key, value)
                else:
                    self._raw_config[config_key] = value
                    
                self.logger.debug(f"Overrode {config_key} from environment")
        
        # Handle DCA-specific environment variables
        self._handle_dca_environment_variables()
    
    def _handle_dca_environment_variables(self) -> None:
        """Handle DCA-specific environment variables"""
        # General DCA amount (applies to all strategies)
        dca_amount = os.environ.get('TRADE_DCA_AMOUNT')
        if dca_amount:
            dca_amount_float = float(dca_amount)
            # Ensure dca section exists
            if 'dca' not in self._raw_config:
                self._raw_config['dca'] = {}
            if 'strategies' not in self._raw_config['dca']:
                self._raw_config['dca']['strategies'] = {}
            
            # Apply to all existing strategies
            for strategy_name in self._raw_config['dca']['strategies']:
                self._raw_config['dca']['strategies'][strategy_name]['amount'] = dca_amount_float
            
            self.logger.debug(f"Set DCA amount to {dca_amount_float} for all strategies")
        
        # BB-specific DCA amount
        bb_dca_amount = os.environ.get('TRADE_DCA_BB_AMOUNT')
        if bb_dca_amount:
            bb_amount_float = float(bb_dca_amount)
            # Ensure path exists
            if 'dca' not in self._raw_config:
                self._raw_config['dca'] = {}
            if 'strategies' not in self._raw_config['dca']:
                self._raw_config['dca']['strategies'] = {}
            if 'BB_LOWER' not in self._raw_config['dca']['strategies']:
                self._raw_config['dca']['strategies']['BB_LOWER'] = {}
            
            self._raw_config['dca']['strategies']['BB_LOWER']['amount'] = bb_amount_float
            self.logger.debug(f"Set BB_LOWER DCA amount to {bb_amount_float}")
        
        # EMA-specific DCA amount (applies to all EMA strategies)
        ema_dca_amount = os.environ.get('TRADE_DCA_EMA_AMOUNT')
        if ema_dca_amount:
            ema_amount_float = float(ema_dca_amount)
            # Ensure path exists
            if 'dca' not in self._raw_config:
                self._raw_config['dca'] = {}
            if 'strategies' not in self._raw_config['dca']:
                self._raw_config['dca']['strategies'] = {}
            
            # Apply to all EMA strategies
            ema_strategies = ['EMA_34', 'EMA_89', 'EMA_120']
            for strategy_name in ema_strategies:
                if strategy_name in self._raw_config['dca']['strategies']:
                    self._raw_config['dca']['strategies'][strategy_name]['amount'] = ema_amount_float
            
            self.logger.debug(f"Set EMA DCA amount to {ema_amount_float} for EMA strategies")
    
    def _set_nested_config(self, key_path: str, value: Any) -> None:
        """Set nested configuration value"""
        keys = key_path.split('.')
        config = self._raw_config
        
        # Navigate to the parent of the target key
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # Set the value
        config[keys[-1]] = value
    
    def _validate_config(self) -> None:
        """Validate configuration"""
        required_fields = ['symbol']
        
        for field in required_fields:
            if field not in self._raw_config:
                raise ValueError(f"Required configuration field '{field}' is missing")
        
        # Validate symbol format
        symbol = self._raw_config['symbol']
        if '/' not in symbol:
            raise ValueError(f"Invalid symbol format: {symbol}. Expected format: BASE/QUOTE")
        
        # Validate numeric ranges
        validations = [
            ('amount', 0, float('inf')),
            ('risk.min_position_size', 0.1, float('inf')),
            ('risk.max_position_size', 1, float('inf')),
            ('risk.max_position_percentage', 1, 100),
            ('risk.daily_loss_limit', 0, 100),
            ('risk.risk_per_trade', 0.1, 50),
        ]
        
        for field, min_val, max_val in validations:
            value = self._get_nested_config(field)
            if value is not None:
                if not min_val <= float(value) <= max_val:
                    raise ValueError(f"{field} must be between {min_val} and {max_val}")
    
    def _get_nested_config(self, key_path: str) -> Any:
        """Get nested configuration value"""
        keys = key_path.split('.')
        config = self._raw_config
        
        try:
            for key in keys:
                config = config[key]
            return config
        except (KeyError, TypeError):
            return None
    
    def get_api_credentials(self) -> Dict[str, Optional[str]]:
        """Get API credentials"""
        if not hasattr(self, '_api_credentials'):
            self._load_from_environment()
        return self._api_credentials
    
    def save_config(self, config: TradeConfig, filename: Optional[str] = None) -> None:
        """Save configuration to file"""
        filename = filename or self.config_file
        config_dict = config.to_dict()
        
        with open(filename, 'w') as f:
            json.dump(config_dict, f, indent=2)
            
        self.logger.info(f"Configuration saved to {filename}")
    
    def reload_config(self) -> TradeConfig:
        """Reload configuration from sources"""
        self._config_cache = None
        self._raw_config = {}
        return self.load_config(force_reload=True) 