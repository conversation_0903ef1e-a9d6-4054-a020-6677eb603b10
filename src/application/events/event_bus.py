"""Event Bus for trading bot communication"""
import asyncio
from typing import Dict, List, Callable, Any, Optional, Type
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import logging
import json


class EventType(Enum):
    """Event types for trading bot"""
    # Trading events
    SIGNAL_GENERATED = 'signal_generated'
    ORDER_CREATED = 'order_created'
    ORDER_FILLED = 'order_filled'
    ORDER_CANCELLED = 'order_cancelled'
    POSITION_OPENED = 'position_opened'
    POSITION_CLOSED = 'position_closed'
    
    # Risk management events
    STOP_LOSS_TRIGGERED = 'stop_loss_triggered'
    TAKE_PROFIT_TRIGGERED = 'take_profit_triggered'
    DCA_ORDER_TRIGGERED = 'dca_order_triggered'
    RISK_LIMIT_EXCEEDED = 'risk_limit_exceeded'
    
    # System events
    MARKET_DATA_UPDATE = 'market_data_update'
    CONNECTION_LOST = 'connection_lost'
    CONNECTION_RESTORED = 'connection_restored'
    ERROR_OCCURRED = 'error_occurred'
    
    # Performance events
    STATS_UPDATED = 'stats_updated'
    REPORT_GENERATED = 'report_generated'


@dataclass
class Event:
    """Base event class"""
    type: EventType
    timestamp: datetime = field(default_factory=datetime.now)
    data: Dict[str, Any] = field(default_factory=dict)
    source: Optional[str] = None
    correlation_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary"""
        return {
            'type': self.type.value,
            'timestamp': self.timestamp.isoformat(),
            'data': self.data,
            'source': self.source,
            'correlation_id': self.correlation_id
        }
    
    def to_json(self) -> str:
        """Convert event to JSON string"""
        return json.dumps(self.to_dict(), default=str)


class EventBus:
    """Professional event bus with async support"""
    
    def __init__(self, max_history: int = 1000):
        self.subscribers: Dict[EventType, List[Callable]] = {}
        self.event_history: List[Event] = []
        self.max_history = max_history
        self.logger = logging.getLogger('EventBus')
        
        # Performance tracking
        self.event_count = 0
        self.subscriber_count = 0
        
        # Processing queue
        self.event_queue: asyncio.Queue = asyncio.Queue()
        self.is_processing = False
        
    async def start(self) -> None:
        """Start event processing"""
        self.is_processing = True
        asyncio.create_task(self._process_events())
        self.logger.info("Event bus started")
    
    async def stop(self) -> None:
        """Stop event processing"""
        self.is_processing = False
        self.logger.info("Event bus stopped")
    
    def subscribe(self, event_type: EventType, handler: Callable) -> None:
        """Subscribe to event type"""
        if event_type not in self.subscribers:
            self.subscribers[event_type] = []
        
        self.subscribers[event_type].append(handler)
        self.subscriber_count += 1
        
        self.logger.debug(f"Subscribed to {event_type.value}, total subscribers: {self.subscriber_count}")
    
    def unsubscribe(self, event_type: EventType, handler: Callable) -> None:
        """Unsubscribe from event type"""
        if event_type in self.subscribers and handler in self.subscribers[event_type]:
            self.subscribers[event_type].remove(handler)
            self.subscriber_count -= 1
            
            self.logger.debug(f"Unsubscribed from {event_type.value}")
    
    async def publish(self, event: Event) -> None:
        """Publish event to subscribers"""
        try:
            # Add to queue for processing
            await self.event_queue.put(event)
            self.event_count += 1
            
        except Exception as e:
            self.logger.error(f"Failed to publish event {event.type.value}: {e}")
    
    async def publish_event(self, event_type: EventType, data: Dict[str, Any], 
                           source: Optional[str] = None, correlation_id: Optional[str] = None) -> None:
        """Convenience method to publish event"""
        event = Event(
            type=event_type,
            data=data,
            source=source,
            correlation_id=correlation_id
        )
        await self.publish(event)
    
    async def _process_events(self) -> None:
        """Process events from queue"""
        while self.is_processing:
            try:
                # Wait for event with timeout
                event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)
                
                # Add to history
                self._add_to_history(event)
                
                # Notify subscribers
                await self._notify_subscribers(event)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing event: {e}")
    
    def _add_to_history(self, event: Event) -> None:
        """Add event to history"""
        self.event_history.append(event)
        
        # Maintain history size
        if len(self.event_history) > self.max_history:
            self.event_history = self.event_history[-self.max_history:]
    
    async def _notify_subscribers(self, event: Event) -> None:
        """Notify all subscribers of event"""
        if event.type not in self.subscribers:
            return
        
        tasks = []
        for handler in self.subscribers[event.type]:
            try:
                if asyncio.iscoroutinefunction(handler):
                    tasks.append(asyncio.create_task(handler(event)))
                else:
                    # Run sync handler in thread pool
                    tasks.append(asyncio.get_event_loop().run_in_executor(None, handler, event))
            except Exception as e:
                self.logger.error(f"Error creating task for handler: {e}")
        
        # Wait for all handlers to complete
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    def get_events_by_type(self, event_type: EventType, limit: Optional[int] = None) -> List[Event]:
        """Get events by type from history"""
        events = [e for e in self.event_history if e.type == event_type]
        
        if limit:
            events = events[-limit:]
        
        return events
    
    def get_events_by_source(self, source: str, limit: Optional[int] = None) -> List[Event]:
        """Get events by source from history"""
        events = [e for e in self.event_history if e.source == source]
        
        if limit:
            events = events[-limit:]
        
        return events
    
    def get_recent_events(self, minutes: int = 60) -> List[Event]:
        """Get events from recent time period"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [e for e in self.event_history if e.timestamp >= cutoff_time]
    
    def clear_history(self) -> None:
        """Clear event history"""
        self.event_history.clear()
        self.logger.info("Event history cleared")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get event bus statistics"""
        event_type_counts = {}
        for event in self.event_history:
            event_type = event.type.value
            event_type_counts[event_type] = event_type_counts.get(event_type, 0) + 1
        
        return {
            'total_events': self.event_count,
            'history_size': len(self.event_history),
            'subscriber_count': self.subscriber_count,
            'event_type_counts': event_type_counts,
            'is_processing': self.is_processing
        }


# Global event bus instance
event_bus = EventBus()


# Event handler decorators
def on_event(event_type: EventType):
    """Decorator to register event handler"""
    def decorator(func):
        event_bus.subscribe(event_type, func)
        return func
    return decorator


# Specialized event classes
@dataclass
class SignalEvent(Event):
    """Signal generated event"""
    def __init__(self, signal_data: Dict[str, Any], source: str = 'strategy'):
        super().__init__(
            type=EventType.SIGNAL_GENERATED,
            data=signal_data,
            source=source
        )


@dataclass
class OrderEvent(Event):
    """Order related event"""
    def __init__(self, event_type: EventType, order_data: Dict[str, Any], source: str = 'trading_engine'):
        super().__init__(
            type=event_type,
            data=order_data,
            source=source
        )


@dataclass
class PositionEvent(Event):
    """Position related event"""
    def __init__(self, event_type: EventType, position_data: Dict[str, Any], source: str = 'trading_engine'):
        super().__init__(
            type=event_type,
            data=position_data,
            source=source
        )


@dataclass
class RiskEvent(Event):
    """Risk management event"""
    def __init__(self, event_type: EventType, risk_data: Dict[str, Any], source: str = 'risk_manager'):
        super().__init__(
            type=event_type,
            data=risk_data,
            source=source
        )


@dataclass
class SystemEvent(Event):
    """System event"""
    def __init__(self, event_type: EventType, system_data: Dict[str, Any], source: str = 'system'):
        super().__init__(
            type=event_type,
            data=system_data,
            source=source
        ) 