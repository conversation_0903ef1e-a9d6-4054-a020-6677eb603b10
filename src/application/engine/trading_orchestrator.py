"""Clean and modular trading engine orchestrator"""
import asyncio
import atexit
import logging
from datetime import datetime
from typing import Optional

from src.core.models import Signal, SignalDirection
from src.core.risk.risk_manager import RiskManager
from src.core.strategies.dca_strategy import DCAStrategy
from src.infrastructure.config.config_manager import ConfigManager
from src.infrastructure.logging.logger import TradeLogger
from src.infrastructure.data.statistics_tracker import StatisticsTracker
from src.infrastructure.exchange.exchange_connector import ExchangeConnector

# Import new managers
from src.application.managers.account_manager import AccountManager
from src.application.managers.order_manager import OrderManager
from src.application.managers.exchange_position_manager import ExchangePositionManager
from src.application.managers.market_data_manager import MarketDataManager

# Import new modular components
from src.application.engine.logging_setup import LoggingSetup
from src.application.engine.position_handler import PositionHandler
from src.application.engine.trading_loop_manager import TradingLoopManager

# Import Event Bus only (no Telegram)
from src.application.events.event_bus import EventBus

# from src.infrastructure.sync.exchange_sync_service import ExchangeSyncService  # Not needed with stateless approach


class TradingOrchestrator:
    """Clean and modular trading engine orchestrator"""
    
    def __init__(self, config_file: Optional[str] = None, debug: bool = False):
        # Initialize core components
        self.config_manager = ConfigManager(config_file)
        self.config = self.config_manager.load_config()
        self.debug = debug
        
        # Setup logging using new LoggingSetup module
        LoggingSetup.setup_global_console_logging(debug)
        self.logger = logging.getLogger('TradingOrchestrator')
        
        # Create trade logger without console handler
        self.trade_logger = TradeLogger(
            name='TradingOrchestrator',
            log_level='DEBUG' if debug else 'INFO',
            console_enabled=False
        )
        
        # Initialize infrastructure
        self._initialize_infrastructure()
        
        # Initialize managers
        self._initialize_managers()
        
        # Initialize Event Bus first (needed by handlers)
        self.event_bus = EventBus()
        
        # Initialize specialized handlers
        self._initialize_handlers()
        
        # Control flags
        self.is_running = False
        self.shutdown_event = asyncio.Event()
        
        # Cleanup tracking
        self._cleanup_done = False
        self._register_cleanup()
        
        self.logger.info(f"🚀 Trading Orchestrator initialized for {self.config.symbol}")
    
    def _initialize_infrastructure(self) -> None:
        """Initialize infrastructure components"""
        # Data management
        self.stats_tracker = StatisticsTracker()
        # self.state_manager = StateManager()  # Not needed with stateless approach
        self.risk_manager = RiskManager(self.config)
        
        # Exchange connection
        credentials = self.config_manager.get_api_credentials()
        safe_credentials = {
            'api_key': credentials.get('api_key') or '',
            'api_secret': credentials.get('api_secret') or ''
        }
        self.exchange = ExchangeConnector(self.config, safe_credentials)
        
        # Set exchange connector for risk manager
        self.risk_manager.set_exchange_connector(self.exchange)
        
        # Strategy
        self.strategy = DCAStrategy(self.config)
        
        # Exchange sync service - Not needed with stateless approach
        # self.sync_service = ExchangeSyncService(self.exchange, self.state_manager, self.risk_manager)
    
    def _initialize_managers(self) -> None:
        """Initialize manager components"""
        # Account management
        self.account_manager = AccountManager(self.risk_manager, self.exchange)
        
        # Order management  
        self.order_manager = OrderManager(self.config, self.risk_manager, self.exchange)
        
        # Position management - Using stateless exchange-driven approach
        self.position_manager = ExchangePositionManager(self.exchange, self.order_manager)
        
        # Market data management
        self.market_data_manager = MarketDataManager(self.exchange)
    
    def _initialize_handlers(self) -> None:
        """Initialize specialized handler components"""
        # Position handler - consolidates all position logic
        self.position_handler = PositionHandler(
            self.config, self.strategy, self.risk_manager,
            self.account_manager, self.order_manager, self.position_manager
        )
        
        # Trading loop manager - handles main loop execution
        self.loop_manager = TradingLoopManager(
            self.config, self.position_handler, self.market_data_manager,
            self.position_manager, self.stats_tracker
        )
    

    
    def _register_cleanup(self) -> None:
        """Register cleanup handlers"""
        def cleanup_on_exit():
            if not self._cleanup_done and hasattr(self, 'exchange') and self.exchange:
                try:
                    import warnings
                    with warnings.catch_warnings():
                        warnings.simplefilter("ignore")
                        if (hasattr(self.exchange, 'exchange') and 
                            self.exchange.exchange and 
                            hasattr(self.exchange.exchange, 'close')):
                            try:
                                close_method = getattr(self.exchange.exchange, 'close')
                                if not asyncio.iscoroutinefunction(close_method):
                                    close_method()
                            except:
                                pass
                except:
                    pass
        
        atexit.register(cleanup_on_exit)
    
    async def start(self) -> None:
        """Start the trading orchestrator"""
        try:
            self.is_running = True
            self.logger.info("🚀 Starting Trading Orchestrator...")
            
            # Start Event Bus
            await self.event_bus.start()
            
            self.logger.info("🚀 Starting Connection to Exchange...")
            
            # Connect to exchange
            try:
                await self.exchange.connect()
                self.logger.info("✅ Exchange connected successfully")
            except ConnectionError as e:
                # Exchange connection failed with specific error
                self.logger.error(f"❌ {e}")
                raise Exception(str(e))
            except Exception as e:
                # Other unexpected connection errors
                self.logger.error(f"❌ Unknown connection error: {e}")
                raise Exception("Failed to connect to exchange")
            
            # Initialize capital from exchange
            await self.account_manager.initialize_capital_from_exchange()
            
            # Fetch and log account information
            await self.account_manager.fetch_and_log_account_info()
            
            # Load historical state
            self._load_historical_state()
            
            # Stateless approach - no sync service needed
            self.logger.info("🔄 Stateless approach - no sync service needed")
            self.logger.info("✅ Stateless position management ready")
            
            # Start main trading loop using loop manager
            await self.loop_manager.run_main_loop(
                lambda: self.is_running, 
                self.shutdown_event
            )
            
        except Exception as e:
            self.logger.error(f"❌ Orchestrator start error: {e}")
            raise
        finally:
            await self.stop()
    
    def _load_historical_state(self) -> None:
        """Load historical statistics (stateless approach - no position state needed)"""
        self.logger.info("📚 Loading historical statistics...")
        
        # Only load statistics - no position state needed with stateless approach
        self.stats_tracker.load_historical_data()
        
        self.logger.info("✅ Historical statistics loaded successfully")
    
    async def stop(self) -> None:
        """Stop the trading orchestrator gracefully"""
        if not self.is_running and self._cleanup_done:
            return
            
        self.logger.info("🛑 Stopping Trading Orchestrator...")
        self.is_running = False
        self.shutdown_event.set()
        
        await self._cleanup()
    
    async def _cleanup(self) -> None:
        """Cleanup resources"""
        if self._cleanup_done:
            return
            
        try:
            # Stop Event Bus
            await self.event_bus.stop()
            
            # Stateless approach - no sync service or state saving needed
            self.logger.info("🔄 Stateless cleanup - only saving statistics")
            self.stats_tracker.save_daily_stats()
            
            # Close exchange connection properly
            if hasattr(self, 'exchange') and self.exchange:
                try:
                    import warnings
                    import os
                    
                    # Redirect stderr temporarily
                    stderr_backup = os.dup(2)
                    with open(os.devnull, 'w') as devnull:
                        os.dup2(devnull.fileno(), 2)
                        try:
                            with warnings.catch_warnings():
                                warnings.simplefilter("ignore")
                                await self.exchange.close()
                        finally:
                            os.dup2(stderr_backup, 2)
                            os.close(stderr_backup)
                            
                except Exception:
                    # Complete silence on exchange close
                    pass
                
        except Exception as e:
            self.logger.error(f"❌ Error during shutdown: {e}")
        finally:
            self._cleanup_done = True
            self.logger.info("✅ Trading Orchestrator stopped")


# Backward compatibility alias  
TradingEngine = TradingOrchestrator