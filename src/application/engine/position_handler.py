"""
Consolidates all position handling logic into a single module.
This extracts position-related code from the main orchestrator for better separation of concerns.
"""
import logging
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime

from src.core.strategies.dca_strategy import DCAStrategy
from src.core.risk.risk_manager import RiskManager
from src.application.managers.account_manager import AccountManager
from src.application.managers.order_manager import OrderManager
from src.application.managers.exchange_position_manager import ExchangePositionManager
from src.application.managers.market_data_manager import MarketDataManager
from src.infrastructure.data.statistics_tracker import StatisticsTracker
from src.core.models import Signal, Position, Order

from src.core.strategies.indicators.technical_indicators import TechnicalIndicatorCalculator
import pandas as pd


# DCA Constants
class DCAConstants:
    """Constants for DCA order processing"""
    PRICE_UPDATE_TOLERANCE = 0.001  # 0.1% tolerance for price updates
    MIN_CANDLES_REQUIRED = 50  # Minimum candles needed for DCA processing
    FRESH_CANDLES_COUNT = 300  # Number of candles to fetch when refreshing data
    # Note: DEFAULT_DCA_AMOUNT removed - now handled by unified AmountCalculator


class PositionHandler:
    """
    Handles all position-related logic for the trading orchestrator
    """
    
    def __init__(self, config, strategy: DCAStrategy, 
                 risk_manager: RiskManager, account_manager: AccountManager,
                 order_manager: OrderManager, position_manager: ExchangePositionManager):
        self.config = config  # TradeConfig object
        self.strategy = strategy
        self.risk_manager = risk_manager
        self.account_manager = account_manager
        self.order_manager = order_manager
        self.position_manager = position_manager
        self.logger = logging.getLogger('TradingOrchestrator')
    
    def _safe_float(self, value, default: float = 0.0) -> float:
        """Safely convert value to float - delegates to position manager"""
        return self.position_manager._safe_float(value, default)
    
    async def _cancel_all_entry_orders_no_signal(self, symbol: str) -> bool:
        """
        Cancel all entry orders when no signal is generated
        Returns True if any orders were cancelled, False otherwise
        """
        try:
            # Get open orders to check existing entry orders
            open_orders = await self.position_manager.get_open_orders(symbol)

            entry_orders_found = 0
            orders_cancelled = 0

            # Check each order and cancel entry orders
            for order in open_orders:
                order_info = order.get('info', {})
                stop_order_type = order_info.get('stopOrderType')

                # Skip SL/TP orders - chỉ hủy entry orders
                if stop_order_type in ['StopLoss', 'TakeProfit']:
                    continue

                entry_orders_found += 1
                order_id = order.get('id')
                order_price = self._safe_float(order.get('price'))

                if not order_id:
                    self.logger.warning(f"⚠️ Invalid order ID found: {order_id}")
                    continue

                # Cancel entry order since no signal exists
                try:
                    self.logger.info(
                        f"🗑️ Cancelling entry order (no signal): {order_id[:8]} "
                        f"@ ${order_price:.6f}"
                    )
                    await self.order_manager.cancel_order(order_id, symbol)
                    orders_cancelled += 1
                except Exception as e:
                    self.logger.error(f"❌ Error cancelling order {order_id[:8]}: {e}")

            # Summary logging
            if entry_orders_found > 0:
                self.logger.info(
                    f"📊 No signal cleanup: {entry_orders_found} entry orders found, "
                    f"{orders_cancelled} cancelled"
                )
                return orders_cancelled > 0
            else:
                self.logger.debug("📊 No entry orders found to cancel")
                return False

        except Exception as e:
            self.logger.error(f"❌ Failed to cancel entry orders for {symbol}: {e}", exc_info=True)
            return False
    async def handle_no_position(self, current_price: float, exchange) -> Dict:
        """Handle case when no position exists - unified version with result tracking"""
        result = {
            'signal_generated': False,
            'action_taken': 'monitoring',
            'trade_created': False
        }
        
        try:
            self.logger.debug("🔍 No position found, checking for entry signals...")
            
            # Generate trading signal
            signal = await self.strategy.generate_signal(exchange)

            # Nếu không có signal, hủy các entry order hiện tại nếu có
            if not signal:
                self.logger.debug("❌ No signal generated by strategy")
                
                # Hủy các entry order không còn hợp lệ
                orders_cancelled = await self._cancel_all_entry_orders_no_signal(self.config.symbol)
                if orders_cancelled:
                    self.logger.info("🧹 Cancelled invalid entry orders due to no signal")
                    result['action_taken'] = 'cancelled_orders_no_signal'
                else:
                    result['action_taken'] = 'no_signal_generated'
                
                return result
            
            # Signal was generated
            result['signal_generated'] = True
            result['signal_direction'] = signal.direction.value
            result['signal_price'] = signal.entry_price
            result['action_taken'] = 'signal_analysis'
            
            # Log signal
            self.logger.info(
                f"📶 Signal generated: {signal.direction.value} at ${signal.entry_price:.8f} "
                f"(confidence: {signal.confidence:.1%})"
            )
            

            
            # Check signal direction matches config
            if signal.direction.value.upper() != self.config.direction:
                self.logger.debug(
                    f"🚫 Signal direction {signal.direction.value} doesn't match config {self.config.direction}"
                )
                result['action_taken'] = 'signal_direction_mismatch'
                return result
            
            # Process the valid signal
            trade_result = await self._process_entry_signal(signal, current_price)
            result.update(trade_result)
            
        except Exception as e:
            self.logger.error(f"❌ Handle no position error: {e}", exc_info=True)
            self.logger.warning("⚠️ Position handling failed, continuing to monitor...")
            result['action_taken'] = 'general_error'
            

        
        return result
    
    async def _cancel_existing_entry_orders(self, symbol: str, signal_price: float, price_tolerance: float = 0.001) -> bool:
        """
        Cancel existing entry orders only if price differs from new signal
        
        Args:
            symbol: Trading symbol
            signal_price: New signal entry price to compare with
            price_tolerance: Price difference tolerance (0.1% default)
            
        Returns:
            bool: True if any orders were kept (should skip creating new order), False otherwise
        """
        try:
            # Get open orders to check existing entry orders
            open_orders = await self.position_manager.get_open_orders(symbol)
            
            entry_orders_found = 0
            orders_cancelled = 0
            orders_kept = 0
            
            # Check each order and decide whether to cancel
            for order in open_orders:
                order_info = order.get('info', {})
                stop_order_type = order_info.get('stopOrderType')
                
                # Skip SL/TP orders
                if stop_order_type in ['StopLoss', 'TakeProfit']:
                    continue
                
                entry_orders_found += 1
                order_id = order.get('id')
                order_price = self._safe_float(order.get('price'))
                
                if not order_id or order_price <= 0:
                    self.logger.warning(f"⚠️ Invalid order data: ID={order_id[-8:] if order_id and len(order_id) >= 8 else order_id}, price={order_price}")
                    continue
                
                # Calculate price difference percentage
                price_diff = abs(order_price - signal_price) / signal_price
                
                if price_diff > price_tolerance:
                    # Price differs significantly - cancel order
                    try:
                        self.logger.info(
                            f"🗑️ Cancelling entry order (price change): {order_id[:8]} "
                            f"Old=${order_price:.6f} -> New=${signal_price:.6f} "
                            f"(diff: {price_diff*100:.2f}%)"
                        )
                        await self.order_manager.cancel_order(order_id, symbol)
                        orders_cancelled += 1
                    except Exception as e:
                        self.logger.error(f"❌ Error cancelling order {order_id[:8]}: {e}")
                else:
                    # Price is similar - keep existing order
                    orders_kept += 1
                    self.logger.info(
                        f"✅ Keeping existing entry order: {order_id[:8]} "
                        f"Price=${order_price:.6f} vs Signal=${signal_price:.6f} "
                        f"(diff: {price_diff*100:.3f}% < {price_tolerance*100:.1f}% tolerance)"
                    )
            
            # Summary logging
            if entry_orders_found > 0:
                if orders_kept > 0:
                    self.logger.info(
                        f"📊 Entry orders summary: {entry_orders_found} found, "
                        f"{orders_cancelled} cancelled, {orders_kept} kept - Skipping new order creation"
                    )
                else:
                    self.logger.info(
                        f"📊 Entry orders summary: {entry_orders_found} found, "
                        f"{orders_cancelled} cancelled, {orders_kept} kept - Will create new order"
                    )
            else:
                self.logger.debug("📊 No existing entry orders found - Will create new order")
            
            # Return True if any orders were kept (should skip creating new order)
            return orders_kept > 0
                
        except Exception as e:
            self.logger.error(f"❌ Failed to process entry orders for {symbol}: {e}", exc_info=True)
            # On error, assume no orders kept, allow new order creation
            return False

    async def handle_no_position_planned_pair(self, current_price: float, exchange) -> Dict:
        """
        Handle case when no position exists using planned pair logic - matches bot_dca.py
        Validation is already handled in generate_planned_pair_signal
        """
        result = {
            'signal_generated': False,
            'action_taken': 'planned_pair_monitoring',
            'trade_created': False
        }
        
        try:
            self.logger.debug("🎯 No position found, checking for planned pair entry signals...")
            
            # Generate planned pair signal (validation already handled inside)
            signal = await self.strategy.generate_planned_pair_signal(exchange)
            
            # Nếu không có planned pair signal, hủy các entry order hiện tại nếu có
            if not signal:
                self.logger.debug("❌ No planned pair signal generated")
                
                # Hủy các entry order không còn hợp lệ
                orders_cancelled = await self._cancel_all_entry_orders_no_signal(self.config.symbol)
                if orders_cancelled:
                    self.logger.info("🧹 Cancelled invalid entry orders due to no planned pair signal")
                    result['action_taken'] = 'cancelled_orders_no_planned_pair_signal'
                else:
                    result['action_taken'] = 'no_planned_pair_signal_generated'
                
                return result
            
            # Signal was generated and validated
            result['signal_generated'] = True
            result['signal_direction'] = signal.direction.value
            result['signal_price'] = signal.entry_price
            result['action_taken'] = 'planned_pair_signal_analysis'
            
            # Log signal
            self.logger.info(
                f"🎯 Planned pair signal generated: {signal.direction.value} at ${signal.entry_price:.8f} "
                f"(stink bid strategy - entry at EMA_34)"
            )
            
            # Cancel existing entry orders before creating new one (matches bot_dca.py)
            self.logger.info("🗑️ Checking existing entry orders...")
            orders_kept = await self._cancel_existing_entry_orders(signal.symbol, signal.entry_price)
            
            # Only create new order if no existing orders were kept
            if orders_kept:
                self.logger.info("🔄 Existing order matches signal price - skipping new order creation")
                result['trade_created'] = False
                result['trade_action'] = 'existing_order_kept'
                result['action_taken'] = 'planned_pair_existing_order_kept'
            else:
                # Process the planned pair signal (create new entry order)
                self.logger.info("🎯 Creating new planned pair entry order...")
                trade_result = await self._process_planned_pair_entry_signal(signal, current_price)
                result.update(trade_result)
            
        except Exception as e:
            self.logger.error(f"❌ Handle no position (Planned Pair) error: {e}", exc_info=True)
            self.logger.warning("⚠️ Planned pair position handling failed, continuing to monitor...")
            result['action_taken'] = 'planned_pair_general_error'
        
        return result
    
    async def _process_planned_pair_entry_signal(self, signal: Signal, current_price: float) -> Dict:
        """Process planned pair entry signal with stink bid approach"""
        result = {
            'trade_created': False,
            'action_taken': 'planned_pair_signal_processing'
        }
        
        try:
            # Planned pair risk check (simplified)
            self.logger.info("🎯 Starting planned pair risk management check...")
            risk_metrics = await self.risk_manager.check_trade_allowed(signal)
            self.logger.info(f"✅ Planned pair risk check completed: ${risk_metrics.position_size:.2f} size, can_trade: {risk_metrics.can_trade}")
            
            if not risk_metrics.can_trade:
                self.logger.warning(f"🚫 Planned pair trade not allowed: {risk_metrics.reason}")
                result['action_taken'] = 'planned_pair_risk_check_failed'
                return result
                
        except Exception as risk_error:
            self.logger.error(f"❌ Planned pair risk check failed: {risk_error}", exc_info=True)
            result['action_taken'] = 'planned_pair_risk_check_error'
            return result
        
        # Additional safety check for zero position size
        if risk_metrics.position_size <= 0:
            self.logger.error(f"❌ Invalid position size from risk manager: ${risk_metrics.position_size:.2f}")
            result['action_taken'] = 'planned_pair_invalid_position_size'
            return result

        # Create bot DCA entry order (stink bid at EMA_34)
        try:
            self.logger.info("🎯 Creating planned pair entry order at EMA_34 (stink bid)...")
            self.logger.debug(f"📊 Planned pair order details: Signal price=${signal.entry_price:.6f}, Current price=${current_price:.6f}, Position size=${risk_metrics.position_size:.2f}")
            
            position = await self.order_manager.create_entry_order(signal, risk_metrics, current_price)
            
            if position:
                self.logger.info("✅ Planned pair order created, setting up position...")
                await self.position_manager.create_position(position)
                self.logger.info("✅ Planned pair position created successfully")
                result['trade_created'] = True
                result['trade_action'] = 'planned_pair_position_created'
                result['action_taken'] = 'planned_pair_trade_executed'
                

            else:
                self.logger.error("❌ Failed to create planned pair position - order manager returned None")
                result['action_taken'] = 'planned_pair_order_creation_failed'
                
        except Exception as order_error:
            self.logger.error(f"❌ Planned pair order creation failed: {order_error}", exc_info=True)
            result['action_taken'] = 'planned_pair_order_creation_error'
            return result
        
        return result

    async def _process_entry_signal(self, signal: Signal, current_price: float) -> Dict:
        """Process entry signal through risk management and order creation"""
        result = {
            'trade_created': False,
            'action_taken': 'signal_processing'
        }
        
        try:
            # Check risk management
            self.logger.info("🛡️ Starting risk management check...")
            risk_metrics = await self.risk_manager.check_trade_allowed(signal)
            self.logger.info(f"✅ Risk check completed: ${risk_metrics.position_size:.2f} size, can_trade: {risk_metrics.can_trade}")
            
            if not risk_metrics.can_trade:
                self.logger.warning(f"🚫 Trade not allowed: {risk_metrics.reason}")
                result['action_taken'] = 'risk_check_failed'
                return result
                
        except Exception as risk_error:
            self.logger.error(f"❌ CRITICAL: Risk check failed: {risk_error}", exc_info=True)
            self.logger.error(f"🔍 Risk Manager State: current_capital={self.risk_manager.current_capital}")
            result['action_taken'] = 'risk_check_error'
            return result
        
        # Additional safety check for zero position size
        if risk_metrics.position_size <= 0:
            self.logger.error(f"❌ Invalid position size from risk manager: ${risk_metrics.position_size:.2f}")
            self.logger.error(f"🔍 Debug info: Current capital=${self.risk_manager.current_capital:.2f}")
            result['action_taken'] = 'invalid_position_size'
            return result

        # Validate entry conditions
        try:
            self.logger.info("🔍 Validating entry conditions...")
            entry_valid = self.order_manager.validate_entry_conditions(signal, current_price)
            self.logger.info(f"✅ Entry conditions check: {entry_valid}")
            
            if not entry_valid:
                self.logger.warning("🚫 Entry conditions not met")
                result['action_taken'] = 'entry_conditions_failed'
                return result
                
        except Exception as entry_error:
            self.logger.error(f"❌ CRITICAL: Entry validation failed: {entry_error}", exc_info=True)
            result['action_taken'] = 'entry_validation_error'
            return result

        # Create entry order
        try:
            self.logger.info("🎯 All conditions met! Creating PERP entry order...")
            self.logger.debug(f"📊 Order details: Signal price=${signal.entry_price:.6f}, Current price=${current_price:.6f}, Position size=${risk_metrics.position_size:.2f}")
            
            position = await self.order_manager.create_entry_order(signal, risk_metrics, current_price)
            
            if position:
                self.logger.info("✅ Order created, setting up position...")
                await self.position_manager.create_position(position)
                self.logger.info("✅ PERP Position created successfully")
                result['trade_created'] = True
                result['trade_action'] = 'position_created'
                result['action_taken'] = 'trade_executed'
                

            else:
                self.logger.error("❌ Failed to create position - order manager returned None")
                result['action_taken'] = 'order_creation_failed'
                
        except Exception as order_error:
            self.logger.error(f"❌ CRITICAL: Order creation failed: {order_error}", exc_info=True)
            result['action_taken'] = 'order_creation_error'
            return result
        
        return result
    
    async def handle_active_position(self, position, current_price: float, mode: str = "perp") -> None:
        """Handle active position management - unified for both PERP and planned pair"""
        try:
            mode_symbol = "🎯" if mode == "planned_pair" else "⚙️"
            mode_name = "planned pair" if mode == "planned_pair" else "PERP"
            
            self.logger.debug(f"{mode_symbol} Managing active {mode_name} position: {position.symbol}")
            
            # Update position PnL
            self.position_manager.update_position_pnl(position, current_price)
            
            # Update orders status
            await self.position_manager.update_orders_status(position)
            
            # Check for filled DCA orders and adjust TP/SL if enabled
            if self.config.risk.adjust_tp_sl_on_dca:
                await self._process_filled_dca_orders(position)
            
            # Process DCA orders if enabled (unified for both modes)
            if self.config.dca.enabled:
                await self._process_dca_unified_orders(position)
            
            # Update trailing stops if enabled
            if self.config.risk.use_trailing_stop:
                await self.position_manager.update_trailing_stops(position, current_price, self.config)
            
            # Check for manual close conditions
            should_close, reason = self.position_manager.should_close_position(position)
            if should_close:
                await self.position_manager.close_position(position, reason)
            
        except Exception as e:
            self.logger.error(f"❌ Handle active {mode_name} position error: {e}", exc_info=True)
    
    async def handle_active_position_planned_pair(self, position, current_price: float) -> None:
        """Handle active position with planned pair management - calls unified handler"""
        await self.handle_active_position(position, current_price, mode="planned_pair")
    
    async def _process_dca_unified_orders(self, position) -> None:
        """
        Unified DCA processing combining planned pair and general strategies
        Based on optimized logic from bot_dca.py with Signal Validation
        """
        try:
            # Validate DCA configuration first
            if not self._validate_dca_config():
                return
            
            # Ensure we have sufficient market data
            if not await self._ensure_sufficient_market_data(position.symbol):
                self.logger.warning("⚠️ Skipping DCA processing due to insufficient market data")
                return
            
            # Collect enabled DCA strategies
            enabled_strategies = self._get_enabled_dca_strategies()
            if not enabled_strategies:
                self.logger.debug("📊 No enabled DCA strategies with valid timeframes")
                return
            
            self.logger.info(f"🎯 Processing {len(enabled_strategies)} DCA strategies: {list(enabled_strategies.keys())}")
            
            # Fetch timeframe data for all strategies
            required_timeframes = {config['timeframe'] for config in enabled_strategies.values()}
            timeframe_data = await self._fetch_all_timeframe_data(position.symbol, required_timeframes)
            
            if not timeframe_data:
                self.logger.warning("⚠️ No valid timeframe data available for DCA processing")
                return
            
            # Get current open orders once
            open_orders = await self.position_manager.exchange.get_open_orders(position.symbol)
            side = "buy" if self.config.direction == "LONG" else "sell"
            
            # Process each enabled DCA strategy
            for dca_type, dca_config in enabled_strategies.items():
                await self._process_single_dca_strategy(
                    position, dca_type, dca_config, timeframe_data, open_orders, side
                )
            
        except Exception as e:
            self.logger.error(f"❌ Unified DCA processing error: {e}", exc_info=True)
    
    def _validate_dca_config(self) -> bool:
        """Validate DCA configuration"""
        if not self.config.dca.enabled:
            self.logger.debug("📊 DCA disabled in configuration")
            return False
        
        if not self.config.dca.strategies:
            self.logger.debug("📊 No DCA strategies configured")
            return False
        
        return True
    
    async def _ensure_sufficient_market_data(self, symbol: str) -> bool:
        """Ensure we have sufficient market data for DCA processing"""
        try:
            candles = self.strategy.candle_manager.get_candles()
            
            if not candles or len(candles) < DCAConstants.MIN_CANDLES_REQUIRED:
                self.logger.warning(f"⚠️ Insufficient candle data ({len(candles) if candles else 0} candles), fetching fresh data...")
                return await self._fetch_and_update_candles(symbol)
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Market data validation error: {e}")
            return False
    
    async def _fetch_and_update_candles(self, symbol: str) -> bool:
        """Fetch fresh candles and update candle manager"""
        try:
            market_data_manager = MarketDataManager(self.position_manager.exchange)
            fresh_candles = await market_data_manager.fetch_ohlcv(
                symbol, 
                self.config.indicators.primary_timeframe, 
                DCAConstants.FRESH_CANDLES_COUNT
            )
            
            if fresh_candles and len(fresh_candles) >= DCAConstants.MIN_CANDLES_REQUIRED:
                self.logger.info(f"✅ Successfully fetched {len(fresh_candles)} fresh candles")
                self.strategy.candle_manager.candles = fresh_candles
                return True
            else:
                self.logger.error(f"❌ Failed to fetch sufficient fresh data: {len(fresh_candles) if fresh_candles else 0} candles")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Failed to fetch fresh candle data: {e}")
            return False
    
    def _get_enabled_dca_strategies(self) -> Dict[str, Dict]:
        """Get enabled DCA strategies with valid timeframes"""
        enabled_strategies = {}
        
        for dca_type, dca_config in self.config.dca.strategies.items():
            if not dca_config.get('enabled', False):
                self.logger.debug(f"📊 DCA strategy {dca_type} is disabled")
                continue
            
            timeframe = dca_config.get('timeframe')
            if not timeframe:
                self.logger.warning(f"⚠️ DCA strategy {dca_type} missing timeframe")
                continue
            
            enabled_strategies[dca_type] = dca_config
        
        return enabled_strategies
    
    async def _fetch_all_timeframe_data(self, symbol: str, timeframes: set) -> Dict:
        """Fetch and calculate indicators for all required timeframes"""
        try:
            timeframe_data = {}
            primary_timeframe = self.config.indicators.primary_timeframe
            
            for timeframe in timeframes:
                tf_data = await self._get_timeframe_data(symbol, timeframe, primary_timeframe)
                if tf_data:
                    timeframe_data[timeframe] = tf_data
                    self.logger.debug(f"📊 Loaded {timeframe} data: {len(tf_data['candles'])} candles")
                else:
                    self.logger.warning(f"⚠️ Failed to load {timeframe} data")
            
            self.logger.info(f"📊 Successfully loaded {len(timeframe_data)} timeframes: {list(timeframe_data.keys())}")
            return timeframe_data
            
        except Exception as e:
            self.logger.error(f"❌ Fetch timeframe data error: {e}")
            return {}
    
    async def _get_timeframe_data(self, symbol: str, timeframe: str, primary_timeframe: str) -> Optional[Dict]:
        """Get candles and indicators for a specific timeframe"""
        try:
            # Use cached data for primary timeframe
            if timeframe == primary_timeframe:
                candles = self.strategy.candle_manager.get_candles()
                if candles and len(candles) >= DCAConstants.MIN_CANDLES_REQUIRED:
                    return self._create_timeframe_data_object(candles)
            
            # Fetch fresh data for other timeframes
            market_data_manager = MarketDataManager(self.position_manager.exchange)
            candles = await market_data_manager.fetch_ohlcv(symbol, timeframe, DCAConstants.FRESH_CANDLES_COUNT)
            
            if candles and len(candles) >= DCAConstants.MIN_CANDLES_REQUIRED:
                return self._create_timeframe_data_object(candles)
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Get {timeframe} data error: {e}")
            return None
    
    def _create_timeframe_data_object(self, candles: list) -> Optional[Dict]:
        """Create standardized timeframe data object with indicators"""
        try:
            # Calculate indicators once per timeframe
            indicators = TechnicalIndicatorCalculator.calculate_all_indicators(
                candles, self.config.indicators
            )
            
            return {
                'indicators': indicators,
                'current_price': candles[-1].close,
                'candles': candles
            }
            
        except Exception as e:
            self.logger.error(f"❌ Create timeframe data error: {e}")
            return None
    
    async def _process_single_dca_strategy(self, position, dca_type: str, dca_config: Dict, 
                                         timeframe_data: Dict, open_orders: list, side: str) -> None:
        """Process a single DCA strategy with enhanced logic from bot_dca.py"""
        try:
            # Calculate DCA parameters once
            dca_params = await self._calculate_dca_parameters(dca_type, dca_config, timeframe_data)
            if not dca_params:
                return
            
            # Check for existing DCA order
            existing_dca_order = self._find_existing_dca_order(open_orders, dca_type, side)
            
            if existing_dca_order:
                await self._handle_existing_dca_order(
                    existing_dca_order, position.symbol, dca_type, dca_params, side
                )
            else:
                await self._create_new_dca_order_stateless(
                    position.symbol, dca_type, dca_params['price'], dca_params['size'], side
                )
            
        except Exception as e:
            self.logger.error(f"❌ Process DCA {dca_type} error: {e}", exc_info=True)
    
    async def _calculate_dca_parameters(self, dca_type: str, dca_config: Dict, timeframe_data: Dict) -> Optional[Dict]:
        """Calculate DCA parameters (price, size) for a strategy using unified amount calculation"""
        try:
            timeframe = dca_config['timeframe']
            
            # Validate timeframe data
            if timeframe not in timeframe_data:
                self.logger.warning(f"⚠️ No data available for DCA {dca_type} timeframe {timeframe}")
                return None
            
            tf_data = timeframe_data[timeframe]
            indicators = tf_data['indicators']
            
            # Calculate DCA price
            dca_price = self._calculate_dca_price(dca_type, indicators, dca_config)
            if not dca_price or dca_price <= 0:
                self.logger.warning(f"⚠️ Invalid DCA price for {dca_type}: {dca_price}")
                return None
            
            # Use unified DCA amount calculation from position calculator via risk manager
            dca_result = self.risk_manager.position_calculator.calculate_dca_position_size(
                dca_type, dca_config, dca_price, level=0
            )
            
            if not dca_result.get('valid', False):
                self.logger.warning(f"⚠️ Invalid DCA calculation for {dca_type}")
                return None
            
            return {
                'price': dca_price,
                'size': dca_result['position_size'],
                'amount': dca_result['amount'],
                'method': dca_result['method']
            }
            
        except Exception as e:
            self.logger.error(f"❌ Calculate DCA parameters error for {dca_type}: {e}")
            return None
    
    async def _handle_existing_dca_order(self, existing_order: Dict, symbol: str, dca_type: str, 
                                       dca_params: Dict, side: str) -> None:
        """Handle existing DCA order - update if price changed significantly"""
        try:
            order_id = existing_order['id']
            current_order_price = self._safe_float(existing_order.get('price'))
            new_dca_price = dca_params['price']
            
            self.logger.debug(f"📊 DCA {dca_type} order already exists: {order_id[-8:]}")
            
            # Check if price needs updating
            if self._should_update_dca_price(current_order_price, new_dca_price):
                price_diff = abs(current_order_price - new_dca_price) / new_dca_price
                self.logger.info(
                    f"🔄 Updating DCA {dca_type} order price: "
                    f"${current_order_price:.6f} -> ${new_dca_price:.6f} "
                    f"(diff: {price_diff*100:.2f}%)"
                )
                
                # For Bybit, amount should be in USD (quote currency) not contracts
                position_size_contracts = dca_params['size']  # This is contracts
                usd_amount = position_size_contracts * new_dca_price  # Convert to USD
                
                self.logger.debug(f"🔍 Modify DCA {dca_type}: contracts={position_size_contracts:.6f}, price=${new_dca_price:.6f}, USD amount=${usd_amount:.2f}")
                
                # Update existing order - pass USD amount instead of contracts
                modified_order_id = await self.order_manager.modify_dca_order(
                    order_id=order_id,
                    symbol=symbol,
                    side=side,
                    entry_price=new_dca_price,
                    position_size=usd_amount,  # Changed: pass USD amount instead of contracts
                    dca_type=dca_type
                )
                
                if modified_order_id:
                    self.logger.info(f"✅ DCA {dca_type} order updated successfully")
                    
                    # Track modified DCA order for fill detection (if adjust_tp_sl_on_dca is enabled) - use contracts for tracking
                    self._track_dca_order_if_enabled(symbol, dca_type, modified_order_id, new_dca_price, position_size_contracts)
                else:
                    self.logger.error(f"❌ Failed to update DCA {dca_type} order")
            else:
                self.logger.debug(f"📊 DCA {dca_type} price unchanged: ${current_order_price:.6f}")
                
        except Exception as e:
            self.logger.error(f"❌ Handle existing DCA {dca_type} order error: {e}", exc_info=True)
    
    def _should_update_dca_price(self, current_price: float, new_price: float, 
                               tolerance: float = DCAConstants.PRICE_UPDATE_TOLERANCE) -> bool:
        """Check if DCA order price should be updated based on tolerance"""
        try:
            if current_price <= 0 or new_price <= 0:
                return False
            
            price_diff = abs(current_price - new_price) / new_price
            return price_diff > tolerance
            
        except Exception as e:
            self.logger.error(f"❌ Price comparison error: {e}")
            return False
    
    def _find_existing_dca_order(self, open_orders: list, dca_type: str, side: str) -> Optional[Dict]:
        """Find existing DCA order in open orders list"""
        try:
            if not open_orders:
                return None
            
            # Look for limit orders with matching side
            for order in open_orders:
                if not self._is_potential_dca_order(order, side):
                    continue
                
                order_price = self._safe_float(order.get('price'))
                self.logger.debug(f"🔍 Found potential DCA {dca_type} order: {order['id'][-8:]} @ ${order_price:.6f}")
                return order
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Find existing DCA order error: {e}")
            return None
    
    def _is_potential_dca_order(self, order: Dict, expected_side: str) -> bool:
        """Check if an order could be our DCA order"""
        try:
            # Check side
            order_side = order.get('side', '').lower()
            if order_side != expected_side.lower():
                return False
            
            # Check type (should be limit order)
            order_type = order.get('type', '').lower()
            if order_type != 'limit':
                return False
            
            # Check price is valid
            order_price = self._safe_float(order.get('price'))
            if order_price <= 0:
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Order validation error: {e}")
            return False
    
    def _track_dca_order_if_enabled(self, symbol: str, dca_type: str, order_id: str, price: float, size: float) -> None:
        """Track DCA order for fill detection if TP/SL adjustment is enabled"""
        if self.config.risk.adjust_tp_sl_on_dca and order_id:
            usd_amount = price * size
            self.position_manager.track_dca_order(symbol, dca_type, order_id, price, usd_amount)
            self.logger.debug(f"📝 DCA {dca_type} order tracked for TP/SL adjustment")
    
    def _adjust_position_size_for_balance(self, target_usd_amount: float, 
                                        available_balance: float, 
                                        leverage: float) -> float:
        """Adjust position size based on available balance"""
        max_affordable_usd = available_balance * leverage * 0.95  # 95% of available to leave buffer
        
        if target_usd_amount > max_affordable_usd:
            self.logger.warning(
                f"⚠️ Adjusting position size: Target=${target_usd_amount:.2f} > "
                f"Affordable=${max_affordable_usd:.2f} (Balance=${available_balance:.2f} @ {leverage}x)"
            )
            return max_affordable_usd
        
        return target_usd_amount
    
    async def _create_new_dca_order_stateless(self, symbol: str, dca_type: str, dca_price: float, 
                                            position_size: float, side: str) -> None:
        """Create new DCA order in stateless architecture"""
        try:
            # Validate position size
            if position_size <= 0:
                self.logger.error(f"❌ DCA {dca_type}: Invalid position size {position_size}")
                return
            
            # For Bybit, amount should be in USD (quote currency) not contracts
            # position_size is already in contracts, convert back to USD amount
            usd_amount = position_size * dca_price
            
            self.logger.debug(f"🔍 DCA {dca_type}: contracts={position_size:.6f}, price=${dca_price:.6f}, USD amount=${usd_amount:.2f}")
            
            # Validate account balance before creating order
            required_margin = usd_amount / (self.config.risk.leverage or 1.0)
            
            try:
                balance = await self.position_manager.exchange.fetch_balance()
                available_balance = 0.0
                
                # Improved balance parsing with better error handling
                if balance is None:
                    self.logger.warning(f"⚠️ Balance fetch returned None for DCA {dca_type}")
                    return
                    
                if isinstance(balance, dict):
                    # Try different balance structures with priority order
                    usdt_balance = None
                    
                    # Priority 1: Check total balance first (most reliable for Bybit)
                    if 'total' in balance and isinstance(balance['total'], dict):
                        usdt_balance = balance['total'].get('USDT')
                        if usdt_balance is not None:
                            try:
                                available_balance = float(usdt_balance) if usdt_balance != '' else 0.0
                                self.logger.debug(f"💰 Using total balance: ${available_balance:.2f}")
                            except (ValueError, TypeError):
                                usdt_balance = None
                    
                    # Priority 2: Check free balance if total not available
                    if usdt_balance is None and 'free' in balance and isinstance(balance['free'], dict):
                        usdt_balance = balance['free'].get('USDT')
                        if usdt_balance is not None:
                            try:
                                available_balance = float(usdt_balance) if usdt_balance != '' else 0.0
                                self.logger.debug(f"💰 Using free balance: ${available_balance:.2f}")
                            except (ValueError, TypeError):
                                usdt_balance = None
                        
                    # Priority 3: Check USDT dict structure
                    if usdt_balance is None and 'USDT' in balance and isinstance(balance['USDT'], dict):
                        # Try free first, then total as fallback
                        for key in ['free', 'total']:
                            if key in balance['USDT']:
                                usdt_balance = balance['USDT'].get(key)
                                if usdt_balance is not None:
                                    try:
                                        available_balance = float(usdt_balance) if usdt_balance != '' else 0.0
                                        self.logger.debug(f"💰 Using USDT.{key} balance: ${available_balance:.2f}")
                                        break
                                    except (ValueError, TypeError):
                                        continue
                        
                    # Priority 4: Parse Bybit coin object from info
                    if usdt_balance is None and 'info' in balance:
                        try:
                            info = balance['info']
                            if isinstance(info, dict) and 'result' in info:
                                result = info['result']
                                if isinstance(result, dict) and 'list' in result and result['list']:
                                    account = result['list'][0]
                                    if 'coin' in account and isinstance(account['coin'], list):
                                        for coin in account['coin']:
                                            if isinstance(coin, dict) and coin.get('coin') == 'USDT':
                                                wallet_balance = coin.get('walletBalance')
                                                if wallet_balance is not None:
                                                    try:
                                                        available_balance = float(wallet_balance)
                                                        self.logger.debug(f"💰 Using Bybit walletBalance: ${available_balance:.2f}")
                                                        break
                                                    except (ValueError, TypeError):
                                                        continue
                        except Exception as e:
                            self.logger.debug(f"Could not parse Bybit coin object: {e}")
                            
                    # Priority 5: Direct USDT value as last resort
                    if usdt_balance is None and available_balance == 0.0:
                        usdt_balance = balance.get('USDT')
                        if usdt_balance is not None:
                            try:
                                available_balance = float(usdt_balance) if usdt_balance != '' else 0.0
                                self.logger.debug(f"💰 Using direct USDT balance: ${available_balance:.2f}")
                            except (ValueError, TypeError):
                                pass
                    
                    # Final check if no balance was found
                    if available_balance == 0.0 and usdt_balance is None:
                        self.logger.warning(f"⚠️ USDT balance not found in balance response: {balance}")
                        
                else:
                    self.logger.warning(f"⚠️ Invalid balance format: {type(balance)} - {balance}")
                    return
                    
                self.logger.debug(f"💰 Balance check: Available=${available_balance:.2f}, Required=${required_margin:.2f} (for ${usd_amount:.2f} @ {self.config.risk.leverage}x)")
                
                if available_balance < required_margin:
                    self.logger.warning(
                        f"⚠️ Insufficient balance for DCA {dca_type}: "
                        f"Available=${available_balance:.2f}, Required=${required_margin:.2f} "
                        f"(${usd_amount:.2f} @ {self.config.risk.leverage}x leverage)"
                    )
                    return
                    
            except Exception as balance_error:
                self.logger.warning(f"⚠️ Could not verify balance for DCA {dca_type}: {balance_error}")
                # Log more details for debugging
                self.logger.debug(f"Balance error details: {type(balance_error).__name__}: {balance_error}")
                return  # Don't create order if balance check fails
            
            # Create DCA order via order manager - pass USD amount instead of contracts
            order_id = await self.order_manager.create_dca_order(
                symbol=symbol,
                side=side,
                entry_price=dca_price,
                position_size=usd_amount,  # Changed: pass USD amount instead of contracts
                dca_type=dca_type
            )
            
            if order_id:
                order_id_short = order_id[-8:] if order_id and len(order_id) >= 8 else order_id
                self.logger.info(f"✅ DCA {dca_type} order created: ID {order_id_short}")
                
                # Track DCA order for fill detection (if enabled) - use contracts for tracking
                self._track_dca_order_if_enabled(symbol, dca_type, order_id, dca_price, position_size)
            else:
                self.logger.error(f"❌ Failed to create DCA {dca_type} order")
                
        except Exception as e:
            self.logger.error(f"❌ Create DCA {dca_type} order error: {e}", exc_info=True)
    
    async def _adjust_tp_sl_after_dca(self, position: Position, new_dca_price: float, dca_amount: float) -> None:
        """Adjust TP/SL after DCA order execution"""
        try:
            # Validate inputs
            if not position or new_dca_price <= 0 or dca_amount <= 0:
                self.logger.warning(f"⚠️ Invalid DCA adjustment parameters: price={new_dca_price}, amount={dca_amount}")
                return
            
            # Get current position from exchange to get updated contract size
            current_position = await self.position_manager.get_active_position(position.symbol)
            if not current_position:
                self.logger.warning(f"⚠️ No active position found for TP/SL adjustment: {position.symbol}")
                return
            
            # Use current exchange position data for accurate calculations
            current_entry_price = current_position.entry_price
            current_contracts = current_position.contracts
            
            self.logger.info(f"📊 DCA TP/SL Adjustment:")
            self.logger.info(f"   Current position: {current_contracts:.6f} contracts @ ${current_entry_price:.6f}")
            self.logger.info(f"   DCA filled: {dca_amount:.6f} contracts @ ${new_dca_price:.6f}")
            
            # Calculate new TP/SL based on current average price from exchange
            # The exchange already calculated the new average entry price for us
            new_avg_price = current_entry_price  # This is already the weighted average from exchange
            
            # Calculate new TP/SL based on direction and multipliers
            if position.side.lower() == 'long':
                # For LONG: TP above avg price, SL below
                new_tp = new_avg_price * self.config.planned_pair.long['take_profit_multiplier']
                new_sl = new_avg_price * self.config.planned_pair.long['stop_loss_multiplier']
            else:
                # For SHORT: TP below avg price, SL above
                new_tp = new_avg_price * self.config.planned_pair.short['take_profit_multiplier']
                new_sl = new_avg_price * self.config.planned_pair.short['stop_loss_multiplier']
            
            self.logger.info(f"   📊 New levels: TP=${new_tp:.6f}, SL=${new_sl:.6f}")
            
            # Update TP/SL orders if they exist and prices changed
            updated = False
            
            # Handle Take Profit
            if position.tp_order_id and abs(new_tp - (position.take_profit or 0)) > 0.000001:  # Small tolerance for float comparison
                try:
                    await self.order_manager.cancel_order(position.tp_order_id, position.symbol)
                    position.take_profit = new_tp
                    new_tp_id = await self.order_manager.create_take_profit_order(position)
                    if new_tp_id:
                        position.tp_order_id = new_tp_id
                        updated = True
                        self.logger.info(f"🎯 TP adjusted: ${new_tp:.6f}")
                    else:
                        self.logger.error("❌ Failed to create new TP order after DCA")
                except Exception as tp_error:
                    self.logger.error(f"❌ TP adjustment error: {tp_error}")
            
            # Handle Stop Loss
            if position.sl_order_id and abs(new_sl - (position.stop_loss or 0)) > 0.000001:  # Small tolerance for float comparison
                try:
                    await self.order_manager.update_stop_loss(position, new_sl)
                    updated = True
                    self.logger.info(f"🛡️ SL adjusted: ${new_sl:.6f}")
                except Exception as sl_error:
                    self.logger.error(f"❌ SL adjustment error: {sl_error}")
            
            # Update position metadata if any changes were made
            if updated:
                try:
                    self.position_manager.add_position_metadata(position.symbol, {
                        'stop_loss': position.stop_loss,
                        'take_profit': position.take_profit,
                        'sl_order_id': position.sl_order_id,
                        'tp_order_id': position.tp_order_id,
                        'last_dca_adjustment': datetime.now(),
                        'entry_price_after_dca': new_avg_price
                    })
                    self.logger.info(f"✅ TP/SL successfully adjusted after DCA fill")
                except Exception as metadata_error:
                    self.logger.error(f"❌ Metadata update error: {metadata_error}")
            else:
                self.logger.debug(f"📊 No TP/SL adjustment needed - levels unchanged")
                
        except Exception as e:
            self.logger.error(f"❌ Adjust TP/SL after DCA error: {e}", exc_info=True)
    
    def _calculate_dca_price(self, dca_type: str, indicators, dca_config: Dict) -> Optional[float]:
        """Calculate DCA price based on strategy type - enhanced with more strategies"""
        try:
            if dca_type == 'EMA_89':
                # Use EMA slow (89) - index 1 in ema_periods [34, 89, 120]
                return indicators.ema_slow
            
            elif dca_type == 'BB_LOWER':
                # Use Bollinger lower band
                return indicators.bollinger_lower
            
            elif dca_type == 'EMA_34':
                # Use EMA fast (34) for more aggressive entries
                return indicators.ema_fast
            
            elif dca_type == 'EMA_120':
                # Use EMA trend (120) for very conservative entries
                return indicators.ema_trend
            
            elif dca_type == 'BB_MIDDLE':
                # Use Bollinger middle band (SMA)
                return indicators.bollinger_middle
            
            elif dca_type == 'RSI_OVERSOLD':
                # Special case: only create order when RSI < 30
                if indicators.rsi < 30:
                    return indicators.bollinger_lower  # Use BB lower as entry
                return None
            
            elif dca_type == 'MACD_SIGNAL':
                # Use MACD signal for entries
                if indicators.macd > indicators.macd_signal:  # Bullish
                    return indicators.ema_slow
                return None
            
            else:
                self.logger.warning(f"⚠️ Unknown DCA strategy type: {dca_type}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Calculate DCA price error for {dca_type}: {e}")
            return None
    
    async def _process_filled_dca_orders(self, position) -> None:
        """Process filled DCA orders and adjust TP/SL if configured"""
        try:
            # Get filled DCA orders from position manager
            filled_dca_orders = self.position_manager.get_and_clear_filled_dca_orders(position.symbol)
            
            if not filled_dca_orders:
                return
                
            self.logger.info(f"🎯 Processing {len(filled_dca_orders)} filled DCA orders for TP/SL adjustment")
            
            # Process each filled DCA order
            for dca_info in filled_dca_orders:
                dca_type = dca_info['dca_type']
                fill_price = dca_info['fill_price']
                fill_amount = dca_info['fill_amount']
                
                self.logger.info(f"📊 DCA {dca_type} filled: ${fill_amount:.2f} @ ${fill_price:.6f}")
                
                # Calculate DCA amount in contracts (not USD)
                dca_contracts = fill_amount / fill_price if fill_price > 0 else 0
                
                if dca_contracts > 0:
                    # Adjust TP/SL after DCA
                    await self._adjust_tp_sl_after_dca(position, fill_price, dca_contracts)
                else:
                    self.logger.warning(f"⚠️ Invalid DCA contracts calculated for {dca_type}: {dca_contracts}")
                    
        except Exception as e:
            self.logger.error(f"❌ Process filled DCA orders error: {e}", exc_info=True) 