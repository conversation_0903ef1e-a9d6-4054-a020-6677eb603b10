"""Global logging setup and formatters for trading bot"""
import sys
import logging
from typing import List


class GlobalColoredFormatter(logging.Formatter):
    """Enhanced colored formatter for console output"""
    
    COLORS = {
        'DEBUG': '\033[36m',    # Cyan
        'INFO': '\033[32m',     # Green  
        'WARNING': '\033[33m',  # Yellow
        'ERROR': '\033[31m',    # Red
        'CRITICAL': '\033[35m', # Magenta
        'RESET': '\033[0m'      # Reset
    }
    
    def format(self, record):
        color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        reset = self.COLORS['RESET']
        timestamp = self.formatTime(record, '%Y-%m-%d %H:%M:%S')
        
        # Component-specific prefixes
        component_map = {
            'TradingOrchestrator': '🎯 ORCHESTRATOR',
            'DCAStrategy': '🧠 STRATEGY    ',
            'AccountManager': '💰 ACCOUNT     ',
            'OrderManager': '📋 ORDERS      ',
            'PositionManager': '📊 POSITIONS   ',
            'ExchangePositionManager': '📊 EXCHANGE    ',
            'MarketDataManager': '📈 MARKET      ',
            'RiskManager': '🛡️ RISK         ',
            'PositionCalculator': '📊 POS CALC    ',
            'AmountCalculator': '📊 AMOUNT CALC '
        }
        
        prefix = component_map.get(record.name, f'📋 {record.name:<11}')
        formatted = f"{color}[{timestamp}] | {record.levelname:<8} | {prefix} | {record.getMessage()}{reset}"
        
        return formatted


class LoggingSetup:
    """Manages global logging configuration for trading bot"""
    
    @staticmethod
    def setup_global_console_logging(debug: bool = False) -> None:
        """Setup global console logging for all components"""
        root_logger = logging.getLogger()
        
        # Check if console handler already exists
        for handler in root_logger.handlers:
            if isinstance(handler, logging.StreamHandler) and handler.stream == sys.stdout:
                return
        
        # Create global console handler
        console_handler = logging.StreamHandler(sys.stdout)
        console_level = logging.DEBUG if debug else logging.INFO
        console_handler.setLevel(console_level)
        
        # Set formatter
        console_handler.setFormatter(GlobalColoredFormatter())
        root_logger.addHandler(console_handler)
        
        # Set log levels for components
        log_level_enum = logging.DEBUG if debug else logging.INFO
        components = [
            'TradingOrchestrator', 'DCAStrategy', 'AccountManager', 
            'OrderManager', 'PositionManager', 'ExchangePositionManager',
            'MarketDataManager', 'RiskManager', 'PositionCalculator', 'AmountCalculator'
        ]
        
        for component in components:
            logging.getLogger(component).setLevel(log_level_enum)
    
    @staticmethod
    def get_component_loggers(debug: bool = False) -> List[logging.Logger]:
        """Get list of configured component loggers"""
        components = [
            'TradingOrchestrator', 'DCAStrategy', 'AccountManager', 
            'OrderManager', 'PositionManager', 'ExchangePositionManager',
            'MarketDataManager', 'RiskManager', 'PositionCalculator', 'AmountCalculator'
        ]
        
        loggers = []
        log_level = logging.DEBUG if debug else logging.INFO
        
        for component in components:
            logger = logging.getLogger(component)
            logger.setLevel(log_level)
            loggers.append(logger)
        
        return loggers 