"""Trading loop management and execution logic"""
import asyncio
import logging
from datetime import datetime
from typing import Dict

from src.application.engine.position_handler import <PERSON><PERSON><PERSON><PERSON>andler
from src.application.managers.market_data_manager import MarketDataManager
# from src.application.managers.position_manager import PositionManager  # Removed with stateless migration
from src.infrastructure.data.statistics_tracker import StatisticsTracker
from typing import Union
from src.application.managers.exchange_position_manager import ExchangePositionManager


class TradingLoopManager:
    """Manages the main trading loop execution and error handling"""
    
    def __init__(self, config, position_handler: <PERSON>sition<PERSON>and<PERSON>, 
                 market_data_manager: MarketDataManager, 
                 position_manager: ExchangePositionManager,
                 stats_tracker: StatisticsTracker):
        self.config = config
        self.position_handler = position_handler
        self.market_data_manager = market_data_manager
        self.position_manager = position_manager
        self.stats_tracker = stats_tracker
        self.logger = logging.getLogger('TradingOrchestrator')
        
        # Loop control
        self.loop_count = 0
        self.consecutive_errors = 0
        self.max_consecutive_errors = 5
    
    async def run_main_loop(self, is_running_ref, shutdown_event: asyncio.Event) -> None:
        """Execute the main trading loop"""
        self.logger.info("🔄 Starting main trading loop...")
        
        while is_running_ref():
            try:
                self.loop_count += 1
                # Always show loop iteration at INFO level
                self.logger.info(f"📊 Loop iteration #{self.loop_count}")
                
                # Check for shutdown
                if shutdown_event.is_set():
                    self.logger.info("🛑 Shutdown signal detected")
                    break
                
                # Execute single loop iteration
                loop_result = await self._execute_loop_iteration()
                
                # Display loop result summary
                self._display_loop_result(loop_result)
                
                # Reset error counter on successful iteration
                self.consecutive_errors = 0
                
                # Generate performance report periodically
                await self._handle_periodic_tasks()
                
                # Sleep with shutdown check
                await self._sleep_with_shutdown_check(self.config.trading_loop_interval_seconds, shutdown_event)
                
            except Exception as e:
                if not await self._handle_loop_error(e):
                    break  # Circuit breaker activated
        
        self.logger.info("🏁 Main trading loop ended")
    
    async def _execute_loop_iteration(self) -> Dict:
        """Execute single iteration of trading loop"""
        # Update market data
        current_price = await self.market_data_manager.update_market_data(self.config.symbol)
        if not current_price:
            retry_delay = min(30, self.config.trading_loop_interval_seconds * 3)
            self.logger.error(f"❌ Failed to get current price - retrying in {retry_delay}s")
            await asyncio.sleep(retry_delay)
            return {'action_taken': 'price_fetch_failed', 'error': True}
        
        self.logger.info(f"💰 Current price: ${current_price:.8f}")
        
        # Get current position (stateless approach)
        position = await self.position_manager.get_active_position(self.config.symbol)
        
        # Initialize loop result tracking
        loop_result = {
            'position_status': 'active' if position else 'no_position',
            'signal_generated': False,
            'action_taken': 'monitoring',
            'current_price': current_price
        }
        
        if not position:
            # No position - look for entry signals
            self.logger.info("🔍 No active position - analyzing market for entry signals...")
            # MARK: use handle_no_position for complex trading logic
            signal_result = await self.position_handler.handle_no_position_planned_pair(
                current_price, 
                self.market_data_manager.exchange
            )
            loop_result.update(signal_result)
        else:
            # Has position - manage it
            self.logger.info(f"⚙️ Managing active position: {position.symbol}")
            await self.position_handler.handle_active_position_planned_pair(position, current_price)
            loop_result['action_taken'] = 'position_management'
        
        return loop_result
    
    def _display_loop_result(self, loop_result: Dict) -> None:
        """Display comprehensive loop result summary"""
        try:
            current_price = loop_result.get('current_price', 0)
            
            # Skip display if error occurred
            if loop_result.get('error'):
                return
            
            # Create result summary
            status_emoji = "📊" if loop_result['position_status'] == 'active' else "🔍"
            action = loop_result['action_taken'].replace('_', ' ').title()
            
            # Different display logic based on position status
            if loop_result['position_status'] == 'active':
                # Has active position - focus on position monitoring
                result_status = "🔄 MONITORING POSITION"
                self.logger.info(
                    f"{status_emoji} Loop #{self.loop_count} Result: {result_status} | "
                    f"Action: {action} | Price: ${current_price:.8f}"
                )
            else:
                # No position - show signal status
                signal_status = "✅ SIGNAL GENERATED" if loop_result['signal_generated'] else "❌ NO SIGNAL"
                self.logger.info(
                    f"{status_emoji} Loop #{self.loop_count} Result: {signal_status} | "
                    f"Action: {action} | Price: ${current_price:.8f}"
                )
            
            # Additional details if available
            if 'signal_direction' in loop_result:
                signal_price = loop_result.get('signal_price', current_price)
                self.logger.info(f"🎯 Signal Details: {loop_result['signal_direction']} @ ${signal_price:.8f}")
            
            if loop_result.get('trade_created'):
                trade_action = loop_result.get('trade_action', 'Position Created')
                self.logger.info(f"📋 Trade Action: {trade_action}")
            
            # Separator for readability
            self.logger.info("─" * 80)
            
        except Exception as e:
            self.logger.error(f"❌ Error displaying loop result: {e}")
    
    async def _handle_loop_error(self, error: Exception) -> bool:
        """Handle loop errors and circuit breaker logic. Returns False if should stop."""
        self.consecutive_errors += 1
        self.logger.error(f"❌ Main loop error #{self.consecutive_errors}: {error}", exc_info=True)
        
        if self.consecutive_errors >= self.max_consecutive_errors:
            self.logger.error(f"🚨 CIRCUIT BREAKER: {self.consecutive_errors} consecutive errors!")
            self.logger.error("🛑 Stopping bot to prevent infinite error loop")
            return False  # Signal to stop
        
        self.logger.warning(f"⚠️ Error #{self.consecutive_errors}/{self.max_consecutive_errors} in main loop, continuing...")
        error_delay = min(30, self.config.trading_loop_interval_seconds * 3)
        await asyncio.sleep(error_delay)  # Longer sleep on error
        return True  # Continue running
    
    async def _handle_periodic_tasks(self) -> None:
        """Handle periodic tasks like performance reports"""
        try:
            # Generate performance report periodically
            if datetime.now().minute == 0:  # Every hour
                self.logger.info("📊 Generating hourly performance report...")
                self.stats_tracker.generate_performance_report()
        except Exception as e:
            self.logger.error(f"❌ Periodic task error: {e}")
    
    async def _sleep_with_shutdown_check(self, seconds: int, shutdown_event: asyncio.Event) -> None:
        """Sleep with shutdown event check"""
        try:
            await asyncio.wait_for(shutdown_event.wait(), timeout=seconds)
            # Shutdown event was set
            self.logger.info("🛑 Shutdown event received during wait")
        except asyncio.TimeoutError:
            # Normal timeout, continue
            pass 