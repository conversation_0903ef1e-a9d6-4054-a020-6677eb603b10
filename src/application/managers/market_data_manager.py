"""Market data management and updates"""
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from src.infrastructure.exchange.exchange_connector import ExchangeConnector


class MarketDataManager:
    """Manages market data fetching and caching"""
    
    def __init__(self, exchange: ExchangeConnector):
        self.exchange = exchange
        self.logger = logging.getLogger('MarketDataManager')
        
        # Price cache
        self.current_prices: Dict[str, float] = {}
        self.last_price_update: Dict[str, datetime] = {}
        
        # Market data cache
        self._ticker_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_duration = 5  # 5 seconds cache
    
    async def update_market_data(self, symbol: str) -> Optional[float]:
        """Update market data for symbol and return current price"""
        try:
            self.logger.debug(f"🌐 Fetching ticker for {symbol}...")
            
            # Check cache first
            if self._is_cache_valid(symbol):
                cached_price = self.current_prices.get(symbol)
                if cached_price:
                    self.logger.debug(f"📋 Using cached price for {symbol}: ${cached_price:.8f}")
                    return cached_price
            
            # Fetch fresh data
            ticker = await self.exchange.fetch_ticker(symbol)
            current_price = ticker['last']
            
            # Update cache
            self.current_prices[symbol] = current_price
            self.last_price_update[symbol] = datetime.now()
            self._ticker_cache[symbol] = ticker
            
            self.logger.debug(f"✅ Market data updated: {symbol} = ${current_price:.8f}")
            return current_price
            
        except Exception as e:
            self.logger.error(f"❌ Market data update error for {symbol}: {e}", exc_info=True)
            # Return cached price if available
            return self.current_prices.get(symbol)
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current price from cache"""
        return self.current_prices.get(symbol)
    
    def get_ticker_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get full ticker data from cache"""
        return self._ticker_cache.get(symbol)
    
    async def fetch_ohlcv(self, symbol: str, timeframe: str, limit: int = 100):
        """Fetch historical candle data"""
        try:
            self.logger.debug(f"📊 Fetching {limit} {timeframe} candles for {symbol}")
            candles = await self.exchange.fetch_ohlcv(symbol, timeframe, limit)
            self.logger.debug(f"✅ Fetched {len(candles)} candles for {symbol}")
            return candles
        except Exception as e:
            self.logger.error(f"❌ Fetch candles error for {symbol}: {e}")
            return []
    
    def _is_cache_valid(self, symbol: str) -> bool:
        """Check if cached data is still valid"""
        if symbol not in self.last_price_update:
            return False
        
        last_update = self.last_price_update[symbol]
        seconds_since_update = (datetime.now() - last_update).total_seconds()
        
        return seconds_since_update < self._cache_duration
    
    def get_price_change_24h(self, symbol: str) -> Optional[Dict[str, float]]:
        """Get 24h price change data"""
        ticker = self._ticker_cache.get(symbol)
        if not ticker:
            return None
        
        try:
            return {
                'change': float(ticker.get('change') or 0),
                'percentage': float(ticker.get('percentage') or 0),
                'high': float(ticker.get('high') or 0),
                'low': float(ticker.get('low') or 0),
                'volume': float(ticker.get('baseVolume') or 0)
            }
        except (TypeError, ValueError) as e:
            self.logger.warning(f"⚠️ Error parsing price change data: {e}")
            return None
    
    def log_market_summary(self, symbol: str) -> None:
        """Log market summary for symbol"""
        try:
            ticker = self._ticker_cache.get(symbol)
            current_price = self.current_prices.get(symbol)
            
            if not ticker or not current_price:
                self.logger.warning(f"⚠️ No market data available for {symbol}")
                return
            
            change_data = self.get_price_change_24h(symbol)
            if change_data:
                self.logger.info(f"📈 Market Summary for {symbol}:")
                self.logger.info(f"   └─ 💰 Current Price: ${current_price:.8f}")
                self.logger.info(f"   └─ 📊 24h Change: {change_data['percentage']:+.2f}% (${change_data['change']:+.8f})")
                self.logger.info(f"   └─ 📈 24h High: ${change_data['high']:.8f}")
                self.logger.info(f"   └─ 📉 24h Low: ${change_data['low']:.8f}")
                self.logger.info(f"   └─ 📦 24h Volume: {change_data['volume']:,.2f}")
            else:
                self.logger.info(f"📈 Current Price for {symbol}: ${current_price:.8f}")
                
        except Exception as e:
            self.logger.error(f"❌ Market summary error: {e}")
    
    def clear_cache(self, symbol: Optional[str] = None) -> None:
        """Clear price cache for symbol or all symbols"""
        if symbol:
            self.current_prices.pop(symbol, None)
            self.last_price_update.pop(symbol, None)
            self._ticker_cache.pop(symbol, None)
            self.logger.debug(f"🧹 Cleared cache for {symbol}")
        else:
            self.current_prices.clear()
            self.last_price_update.clear()
            self._ticker_cache.clear()
            self.logger.debug("🧹 Cleared all market data cache")