"""Account and balance management"""
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from src.core.risk.risk_manager import RiskManager
from src.infrastructure.exchange.exchange_connector import ExchangeConnector


class AccountManager:
    """Manages account information, balance, and capital"""
    
    def __init__(self, risk_manager: RiskManager, exchange: ExchangeConnector):
        self.risk_manager = risk_manager
        self.exchange = exchange
        self.logger = logging.getLogger('AccountManager')
        
        # Cache for account info
        self._last_balance_fetch: Optional[datetime] = None
        self._balance_cache: Optional[Dict[str, Any]] = None
        self._cache_duration = 300  # 5 minutes
    
    async def initialize_capital_from_exchange(self) -> None:
        """Initialize current capital from exchange balance at startup"""
        try:
            self.logger.info("💰 Fetching initial capital from exchange...")
            
            # Get balance data from exchange
            balance = await self.exchange.fetch_balance()
            usdt_total = self._parse_usdt_balance(balance)
            
            # Set the current capital in risk manager
            if usdt_total > 0:
                self.risk_manager.current_capital = usdt_total
                self.logger.info(f"✅ Initial capital set from exchange: ${usdt_total:.2f}")
            else:
                # Critical error: no capital available
                self.logger.error("🚨 CRITICAL: No USDT balance found in exchange account!")
                self.logger.error("💡 Please deposit USDT to your exchange account before trading")
                raise Exception("No USDT balance available for trading")
                
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize capital from exchange: {e}")
            # No fallback available since we removed initial_capital from config
            self.logger.error("💡 Please ensure your exchange account has USDT balance")
            raise Exception("Cannot initialize capital: no exchange balance and no fallback available")
    
    async def fetch_and_log_account_info(self) -> Dict[str, float]:
        """Fetch account information and update current capital"""
        try:
            self.logger.info("📊 Fetching account information...")
            
            # Get balance data first
            balance = await self.exchange.fetch_balance()
            
            # Parse USDT balance safely - Handle Bybit Unified Account
            usdt_free, usdt_used, usdt_total = self._parse_detailed_balance(balance)
            
            # Update risk manager's current capital
            if usdt_total > 0:
                old_capital = self.risk_manager.current_capital
                self.risk_manager.current_capital = usdt_total
                if old_capital != usdt_total:
                    change = usdt_total - old_capital
                    change_pct = (change / old_capital) * 100 if old_capital > 0 else 0
                    self.logger.info(
                        f"💰 Capital updated from balance: ${old_capital:.2f} → ${usdt_total:.2f} "
                        f"({change:+.2f} / {change_pct:+.2f}%)"
                    )
            
            # Log account information
            self.logger.info(f"💰 Account Balance Summary:")
            self.logger.info(f"   └─ 💵 Free USDT: ${usdt_free:.2f}")
            self.logger.info(f"   └─ 🔒 Used USDT: ${usdt_used:.2f}")
            self.logger.info(f"   └─ 📊 Total USDT: ${usdt_total:.2f}")
            
            # Critical check: Stop if no USDT available
            if usdt_total <= 0:
                self.logger.error("🚨 ========== CRITICAL ERROR ==========")
                self.logger.error("❌ Total USDT = $0.00 - Cannot trade!")
                self.logger.error("💡 Please deposit USDT to your account")
                self.logger.error("🛑 Stopping application...")
                self.logger.error("=====================================")
                raise Exception("No USDT balance available for trading")
            
            # Log risk management info
            self._log_risk_management_status()
            
            # Balance health check
            self._check_balance_health(usdt_free, usdt_total)
            
            return {
                'free': usdt_free,
                'used': usdt_used,
                'total': usdt_total
            }
            
        except Exception as e:
            self.logger.error(f"❌ Failed to fetch account info: {e}")
            raise
    
    def _parse_usdt_balance(self, balance: Dict[str, Any]) -> float:
        """Parse USDT total balance from exchange response"""
        try:
            balance_dict = dict(balance) if isinstance(balance, dict) else {}
            
            # Method 1: Try Bybit Unified Account structure first
            if self._is_bybit_unified_structure(balance_dict):
                return self._parse_bybit_unified_balance(balance_dict)
            
            # Method 2: Standard structure (fallback)
            elif 'USDT' in balance_dict and isinstance(balance_dict['USDT'], dict):
                usdt_info = balance_dict['USDT']
                total_val = usdt_info.get('total', 0)
                return float(total_val) if total_val is not None else 0.0
            
            # Method 3: Free/Used/Total structure (fallback)
            elif self._is_standard_balance_structure(balance_dict):
                total_balance = balance_dict.get('total', {})
                total_val = total_balance.get('USDT')
                return float(total_val) if total_val is not None else 0.0
            
            else:
                # Fallback: unknown structure, cannot determine balance
                self.logger.warning(f"Unknown balance structure: {list(balance_dict.keys())}")
                return 0.0
                
        except (KeyError, TypeError, ValueError) as e:
            self.logger.warning(f"Could not parse balance for capital initialization: {e}")
            return 0.0
    
    def _parse_detailed_balance(self, balance: Dict[str, Any]) -> tuple[float, float, float]:
        """Parse detailed USDT balance (free, used, total) from exchange response"""
        usdt_free = 0.0
        usdt_used = 0.0
        usdt_total = 0.0
        
        try:
            balance_dict = dict(balance) if isinstance(balance, dict) else {}
            
            # Method 1: Try Bybit Unified Account structure first
            if self._is_bybit_unified_structure(balance_dict):
                account_info = balance_dict['info']['result']['list'][0]
                
                # Find USDT coin info
                if 'coin' in account_info:
                    for coin_info in account_info['coin']:
                        if coin_info.get('coin') == 'USDT':
                            # Bybit Unified Account structure
                            wallet_balance = float(coin_info.get('walletBalance', 0))
                            locked_balance = float(coin_info.get('locked', 0))
                            
                            usdt_total = wallet_balance
                            usdt_used = locked_balance
                            usdt_free = wallet_balance - locked_balance
                            
                            # Also get totalAvailableBalance for comparison
                            total_available = float(account_info.get('totalAvailableBalance', 0))
                            
                            self.logger.debug(f"🔍 Bybit Unified Account parsed:")
                            self.logger.debug(f"   Wallet Balance: ${wallet_balance:.2f}")
                            self.logger.debug(f"   Locked: ${locked_balance:.2f}")
                            self.logger.debug(f"   Available: ${usdt_free:.2f}")
                            self.logger.debug(f"   Total Available (account): ${total_available:.2f}")
                            break
            
            # Method 2: Standard structure (fallback)
            elif 'USDT' in balance_dict and isinstance(balance_dict['USDT'], dict):
                usdt_info = balance_dict['USDT']
                
                # Handle null values from Bybit
                free_val = usdt_info.get('free')
                used_val = usdt_info.get('used')
                total_val = usdt_info.get('total', 0)
                
                if free_val is not None and used_val is not None:
                    # Standard case with real values
                    usdt_free = float(free_val)
                    usdt_used = float(used_val)
                    usdt_total = float(total_val) or (usdt_free + usdt_used)
                else:
                    # Bybit case with null free/used but valid total
                    usdt_total = float(total_val) if total_val is not None else 0.0
                    usdt_free = usdt_total  # Assume all is free if not specified
                    usdt_used = 0.0
            
            # Method 3: Free/Used/Total structure (fallback)
            elif self._is_standard_balance_structure(balance_dict):
                free_balance = balance_dict.get('free', {})
                used_balance = balance_dict.get('used', {})
                total_balance = balance_dict.get('total', {})
                
                free_val = free_balance.get('USDT')
                used_val = used_balance.get('USDT')
                total_val = total_balance.get('USDT')
                
                if free_val is not None and used_val is not None:
                    usdt_free = float(free_val)
                    usdt_used = float(used_val)
                    usdt_total = float(total_val) if total_val is not None else (usdt_free + usdt_used)
                elif total_val is not None:
                    usdt_total = float(total_val)
                    usdt_free = usdt_total  # Assume all is free
                    usdt_used = 0.0
            
            else:
                # Fallback: use current capital
                self.logger.debug(f"Unknown balance structure: {list(balance_dict.keys())}")
                usdt_free = self.risk_manager.current_capital
                usdt_used = 0.0
                usdt_total = usdt_free
                
        except (KeyError, TypeError, ValueError) as e:
            self.logger.warning(f"Could not parse detailed balance: {e}")
            # Fallback to current capital
            usdt_free = self.risk_manager.current_capital
            usdt_used = 0.0
            usdt_total = usdt_free
        
        return usdt_free, usdt_used, usdt_total
    
    def _is_bybit_unified_structure(self, balance_dict: Dict[str, Any]) -> bool:
        """Check if balance has Bybit Unified Account structure"""
        return (
            'info' in balance_dict and 
            isinstance(balance_dict['info'], dict) and 
            'result' in balance_dict['info'] and
            isinstance(balance_dict['info']['result'], dict) and
            'list' in balance_dict['info']['result'] and
            len(balance_dict['info']['result']['list']) > 0
        )
    
    def _parse_bybit_unified_balance(self, balance_dict: Dict[str, Any]) -> float:
        """Parse balance from Bybit Unified Account structure"""
        account_info = balance_dict['info']['result']['list'][0]
        
        if 'coin' in account_info:
            for coin_info in account_info['coin']:
                if coin_info.get('coin') == 'USDT':
                    wallet_balance = float(coin_info.get('walletBalance', 0))
                    return wallet_balance
        
        return 0.0
    
    def _is_standard_balance_structure(self, balance_dict: Dict[str, Any]) -> bool:
        """Check if balance has standard free/used/total structure"""
        return (
            'free' in balance_dict and 
            'used' in balance_dict and 
            'total' in balance_dict
        )
    
    def _log_risk_management_status(self) -> None:
        """Log risk management status information"""
        config = self.risk_manager.config
        
        self.logger.info(f"🛡️  Risk Management Status:")
        self.logger.info(f"   └─ 💰 Current Capital: ${self.risk_manager.current_capital:.2f}")
        self.logger.info(f"   └─ 📏 Min Position Size: ${config.risk.min_position_size:.2f}")
        self.logger.info(f"   └─ 📊 Max Position Size: ${config.risk.max_position_size:.2f}")
        self.logger.info(f"   └─ 📈 Max Position %: {config.risk.max_position_percentage:.1f}%")
        
        # Calculate effective max position size
        max_from_percentage = self.risk_manager.current_capital * (config.risk.max_position_percentage / 100)
        effective_max = min(config.risk.max_position_size, max_from_percentage)
        self.logger.info(f"   └─ 🎯 Effective Max Size: ${effective_max:.2f}")
    
    def _check_balance_health(self, usdt_free: float, usdt_total: float) -> None:
        """Check and log balance health warnings"""
        if usdt_free == 0 and usdt_total > 0:
            self.logger.error("🚨 ========== BALANCE WARNING ==========")
            self.logger.error("⚠️  Free USDT = $0.00 but Total USDT > $0")
            self.logger.error("💡 Your USDT might be locked in:")
            self.logger.error("   • Active orders or positions")
            self.logger.error("   • Staking/lending/earn products")
            self.logger.error("   • Margin/futures isolated accounts")
            self.logger.error("🔧 Bot will try to trade but may fail")
            self.logger.error("======================================")
        elif usdt_free < 10:
            self.logger.warning("⚠️  Low free balance detected! Consider adding funds.")
        else:
            self.logger.info("✅ Account balance looks healthy!")