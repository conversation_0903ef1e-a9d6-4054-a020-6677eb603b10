"""Exchange-driven position manager - fetches real-time data from exchange"""
import logging
from typing import Dict, List, Optional, Tuple
from datetime import datetime

from src.core.models import Posi<PERSON>, PositionState, OrderSide
from src.infrastructure.exchange.exchange_connector import ExchangeConnector
from src.application.managers.order_manager import OrderManager


class ExchangePositionManager:
    """Stateless position manager - fetches data directly from exchange"""
    
    def __init__(self, exchange: ExchangeConnector, order_manager: Optional[OrderManager] = None):
        self.exchange = exchange
        self.order_manager = order_manager
        self.logger = logging.getLogger('ExchangePositionManager')
        
        # Minimal cache for order tracking (only what exchange doesn't provide)
        self.strategy_metadata: Dict[str, Dict] = {}  # symbol -> metadata
    
    def _safe_float(self, value, default: float = 0.0) -> float:
        """Safely convert value to float, handling None and other edge cases"""
        if value is None:
            return default
        try:
            return float(value)
        except (TypeError, ValueError):
            self.logger.warning(f"⚠️ Could not convert value to float: {value} (type: {type(value)})")
            return default
    
    def _is_position_active(self, pos_data: Dict) -> bool:
        """Check if position data represents an active position"""
        size = self._safe_float(pos_data.get('size'))
        contracts = self._safe_float(pos_data.get('contracts'))
        notional = self._safe_float(pos_data.get('notional'))
        
        return not (size == 0 and contracts == 0 and notional == 0)
    
    def _apply_metadata_to_position(self, position: Position, symbol: str) -> None:
        """Apply stored metadata to position object"""
        if symbol in self.strategy_metadata:
            metadata = self.strategy_metadata[symbol]
            position.entry_order_id = metadata.get('entry_order_id')
            position.sl_order_id = metadata.get('sl_order_id')
            position.tp_order_id = metadata.get('tp_order_id')
            position.stop_loss = metadata.get('stop_loss')
            position.take_profit = metadata.get('take_profit')
            position.created_at = metadata.get('created_at', datetime.now())
    
    async def get_active_position(self, symbol: str) -> Optional[Position]:
        """Get active position for symbol directly from exchange"""
        try:
            # Fetch real-time positions from exchange
            positions = await self.exchange.fetch_positions()
            
            # Find position for our symbol
            for pos_data in positions:
                if pos_data.get('symbol') != symbol:
                    continue
                
                # Check if position is actually active
                if not self._is_position_active(pos_data):
                    continue  # Not an active position
                
                # Convert exchange data to Position object
                position = self._convert_exchange_position(pos_data, symbol)
                
                # Apply strategy metadata if available
                self._apply_metadata_to_position(position, symbol)
                
                self.logger.debug(
                    f"📊 Exchange position: {symbol} - {position.side} "
                    f"{position.contracts:.6f} @ ${position.entry_price:.8f}, "
                    f"PnL: ${position.pnl:.6f} ({position.pnl_percentage:.2f}%)"
                )
                
                return position
            
            # No active position found
            return None
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get position for {symbol}: {e}")
            return None
    
    def _convert_exchange_position(self, pos_data: Dict, symbol: str) -> Position:
        """Convert exchange position data to Position object"""
        # Extract key fields with safe conversion
        side = pos_data.get('side', 'long').lower()
        contracts = self._safe_float(pos_data.get('contracts'))
        entry_price = self._safe_float(pos_data.get('entryPrice'))
        mark_price = self._safe_float(pos_data.get('markPrice'), entry_price)
        unrealized_pnl = self._safe_float(pos_data.get('unrealizedPnl'))
        
        # Calculate PnL percentage with safe conversion
        notional = self._safe_float(pos_data.get('notional'))
        pnl_percentage = (unrealized_pnl / notional * 100) if notional > 0 else 0
        
        # Log debug info for troubleshooting
        self.logger.debug(
            f"🔍 Converting position data: {symbol} - "
            f"contracts={contracts}, entry_price={entry_price}, "
            f"mark_price={mark_price}, pnl={unrealized_pnl}, notional={notional}"
        )
        
        # Create Position object with exchange data
        position = Position(
            symbol=symbol,
            side=side,
            entry_price=entry_price,
            current_price=mark_price,
            contracts=contracts,
            pnl=unrealized_pnl,  # Use exchange PnL directly
            pnl_percentage=pnl_percentage,
            state=PositionState.OPEN,  # Active position is OPEN
            opened_at=datetime.now(),  # Will be updated with metadata if available
        )
        
        return position
    
    async def get_all_active_positions(self) -> Dict[str, Position]:
        """Get all active positions from exchange"""
        try:
            positions = await self.exchange.fetch_positions()
            active_positions = {}
            
            for pos_data in positions:
                symbol = pos_data.get('symbol', '')
                if not symbol:
                    continue
                
                # Check if position is active using helper method
                if not self._is_position_active(pos_data):
                    continue
                
                # Convert to Position object
                position = self._convert_exchange_position(pos_data, symbol)
                
                # Apply metadata using helper method
                self._apply_metadata_to_position(position, symbol)
                
                active_positions[symbol] = position
            
            self.logger.debug(f"📊 Found {len(active_positions)} active positions on exchange")
            return active_positions
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get all positions: {e}")
            return {}
    
    def add_position_metadata(self, symbol: str, metadata: Dict) -> None:
        """Store minimal metadata for position (order IDs, strategy info)"""
        self.strategy_metadata[symbol] = {
            'entry_order_id': metadata.get('entry_order_id'),
            'sl_order_id': metadata.get('sl_order_id'),
            'tp_order_id': metadata.get('tp_order_id'),
            'stop_loss': metadata.get('stop_loss'),
            'take_profit': metadata.get('take_profit'),
            'created_at': metadata.get('created_at', datetime.now()),
            'strategy_data': metadata.get('strategy_data', {})
        }
        
        self.logger.debug(f"📝 Added metadata for {symbol}")
    
    def remove_position_metadata(self, symbol: str) -> None:
        """Remove metadata when position is closed"""
        if symbol in self.strategy_metadata:
            del self.strategy_metadata[symbol]
            self.logger.debug(f"🗑️ Removed metadata for {symbol}")
    
    async def close_position(self, symbol: str, reason: str = "Manual close") -> bool:
        """Close position on exchange"""
        try:
            # Get current position
            position = await self.get_active_position(symbol)
            if not position:
                self.logger.warning(f"⚠️ No position found to close: {symbol}")
                return False
            
            # Calculate close side
            close_side = OrderSide.SELL if position.side == 'long' else OrderSide.BUY
            
            # Close with market order
            self.logger.info(f"🔄 Closing position: {symbol} - {reason}")
            
            order_params = {'reduceOnly': True}
            
            order = await self.exchange.create_market_order(
                symbol=symbol,
                side=close_side,
                amount=position.contracts,
                params=order_params
            )
            
            if order:
                self.logger.info(
                    f"✅ Position closed: {symbol} - {close_side.value} "
                    f"{position.contracts:.6f} @ ${order.average_price:.8f}"
                )
                
                # Remove metadata
                self.remove_position_metadata(symbol)
                return True
            else:
                self.logger.error(f"❌ Failed to close position: {symbol}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Close position error: {e}", exc_info=True)
            return False
    
    def should_close_position(self, position: Position) -> Tuple[bool, str]:
        """Check if position should be closed based on risk rules"""
        # Emergency stop at -5%
        if position.pnl_percentage <= -5:
            reason = f"Emergency stop triggered: {position.pnl_percentage:.2f}%"
            self.logger.warning(f"🚨 {reason}")
            return True, reason
        
        # Position held too long (24 hours)
        if position.opened_at:
            hours_open = (datetime.now() - position.opened_at).total_seconds() / 3600
            if hours_open > 24:
                reason = f"Position held too long: {hours_open:.1f} hours"
                self.logger.info(f"⏰ {reason}")
                return True, reason
        
        return False, ""
    
    async def get_open_orders(self, symbol: str) -> List[Dict]:
        """Get open orders for symbol"""
        try:
            return await self.exchange.get_open_orders(symbol)
        except Exception as e:
            self.logger.error(f"❌ Failed to get open orders for {symbol}: {e}")
            return []
    
    def get_position_by_symbol(self, symbol: str) -> Optional[Position]:
        """Get position by symbol (compatibility method for TradingLoopManager)"""
        # This is a synchronous wrapper - might need to be called differently
        # For now, return None to indicate async call needed
        # The calling code should use get_active_position instead
        self.logger.debug(f"get_position_by_symbol called for {symbol} - use get_active_position for stateless approach")
        return None
    
    def get_position_summary(self) -> Dict:
        """Get summary of tracked positions"""
        return {
            'tracked_symbols': list(self.strategy_metadata.keys()),
            'metadata_count': len(self.strategy_metadata),
            'last_updated': datetime.now().isoformat()
        }
    
    # Compatibility methods for PositionHandler interface
    
    async def create_position(self, position: Position) -> str:
        """Create position metadata (stateless approach doesn't need this)"""
        try:
            # Store minimal metadata for strategy tracking
            metadata = {
                'entry_order_id': position.entry_order_id,
                'sl_order_id': position.sl_order_id,
                'tp_order_id': position.tp_order_id,
                'stop_loss': position.stop_loss,
                'take_profit': position.take_profit,
                'created_at': position.created_at or datetime.now(),
                'side': position.side
            }
            
            self.add_position_metadata(position.symbol, metadata)
            
            self.logger.info(f"📋 Position metadata added: {position.symbol} ({position.side})")
            
            # Automatically create TP/SL orders if values are set
            await self.create_tp_sl_orders(position)
            
            return position.symbol  # Use symbol as ID
            
        except Exception as e:
            self.logger.error(f"❌ Create position error: {e}")
            raise
    
    async def create_tp_sl_orders(self, position: Position) -> None:
        """Create TP and SL orders for position (skip if bracket orders already exist)"""
        try:
            # Skip creating separate TP/SL orders if position has bracket orders
            has_bracket_tp = position.tp_order_id == "BRACKET_TP"
            has_bracket_sl = position.sl_order_id == "BRACKET_SL"
            
            if has_bracket_tp or has_bracket_sl:
                self.logger.info(f"⚡ Skipping separate TP/SL creation - position has Full bracket orders: {position.symbol}")
                return
            
            if not self.order_manager:
                self.logger.warning("⚠️ Order manager not available for creating TP/SL orders")
                return
                
            # Create stop loss order if value is set
            if position.stop_loss and not position.sl_order_id:
                sl_order_id = await self.order_manager.create_stop_loss_order(position)
                if sl_order_id:
                    position.sl_order_id = sl_order_id
                    # Update metadata
                    if position.symbol in self.strategy_metadata:
                        self.strategy_metadata[position.symbol]['sl_order_id'] = sl_order_id
                    self.logger.info(f"🛡️ Stop loss order created for {position.symbol}")
                else:
                    self.logger.warning(f"⚠️ Failed to create stop loss order for {position.symbol}")
            
            # Create take profit order if value is set
            if position.take_profit and not position.tp_order_id:
                tp_order_id = await self.order_manager.create_take_profit_order(position)
                if tp_order_id:
                    position.tp_order_id = tp_order_id
                    # Update metadata
                    if position.symbol in self.strategy_metadata:
                        self.strategy_metadata[position.symbol]['tp_order_id'] = tp_order_id
                    self.logger.info(f"🎯 Take profit order created for {position.symbol}")
                else:
                    self.logger.warning(f"⚠️ Failed to create take profit order for {position.symbol}")
                    
        except Exception as e:
            self.logger.error(f"❌ Create TP/SL orders error: {e}", exc_info=True)
    
    def update_position_pnl(self, position: Position, current_price: float) -> None:
        """Update position PnL - No-op for stateless approach (data comes from exchange)"""
        # In stateless approach, PnL is always fresh from exchange
        # This method exists for compatibility but does nothing
        self.logger.debug(
            f"📊 Position PnL (from exchange): {position.symbol} "
            f"${position.pnl:.6f} ({position.pnl_percentage:.2f}%)"
        )
    
    async def update_orders_status(self, position: Position) -> None:
        """Update orders status using exchange data"""
        try:
            # Get open orders for the symbol
            open_orders = await self.get_open_orders(position.symbol)
            
            # Check entry order status
            if position.entry_order_id:
                entry_order_exists = any(
                    order.get('id') == position.entry_order_id 
                    for order in open_orders
                )
                
                if not entry_order_exists and position.state == PositionState.PENDING:
                    # Entry order filled or cancelled
                    current_position = await self.get_active_position(position.symbol)
                    if current_position:
                        # Order was filled
                        position.state = PositionState.OPEN
                        position.opened_at = datetime.now()
                        self.logger.info(f"✅ Entry order filled: {position.symbol}")
                    else:
                        # Order was cancelled
                        self.logger.warning(f"⚠️ Entry order cancelled: {position.symbol}")
            
            # Check DCA orders status for filled orders (new logic)
            if position.symbol in self.strategy_metadata:
                filled_dca_orders = await self._detect_filled_dca_orders(position.symbol, open_orders)
                if filled_dca_orders:
                    # Store filled DCA info for position handler to process
                    metadata = self.strategy_metadata[position.symbol]
                    if 'filled_dca_orders' not in metadata:
                        metadata['filled_dca_orders'] = []
                    metadata['filled_dca_orders'].extend(filled_dca_orders)
                    
                    for dca_info in filled_dca_orders:
                        self.logger.info(f"🎯 DCA order filled: {dca_info['dca_type']} @ ${dca_info['fill_price']:.6f} (${dca_info['fill_amount']:.2f})")
            
        except Exception as e:
            self.logger.error(f"❌ Update orders status error: {e}")
    
    async def _detect_filled_dca_orders(self, symbol: str, open_orders: list) -> list:
        """Detect DCA orders that have been filled since last check"""
        try:
            filled_dca_orders = []
            metadata = self.strategy_metadata.get(symbol, {})
            
            # Get tracked DCA orders from metadata
            tracked_dca_orders = metadata.get('tracked_dca_orders', {})
            if not tracked_dca_orders:
                return filled_dca_orders
            
            # Check each tracked DCA order
            for dca_type, dca_order_info in tracked_dca_orders.items():
                order_id = dca_order_info.get('order_id')
                if not order_id:
                    continue
                
                # Check if order still exists in open orders
                order_exists = any(order.get('id') == order_id for order in open_orders)
                
                if not order_exists and not dca_order_info.get('filled_detected', False):
                    # Order is no longer open and we haven't detected fill before
                    # This means it was likely filled
                    
                    # Mark as filled to avoid duplicate detection
                    dca_order_info['filled_detected'] = True
                    dca_order_info['filled_at'] = datetime.now()
                    
                    # Get fill info from order history (if possible) or estimate
                    fill_price = dca_order_info.get('price', 0.0)
                    fill_amount = dca_order_info.get('amount', 0.0)
                    
                    filled_dca_orders.append({
                        'dca_type': dca_type,
                        'order_id': order_id,
                        'fill_price': fill_price,
                        'fill_amount': fill_amount,
                        'filled_at': dca_order_info['filled_at']
                    })
                    
                    self.logger.debug(f"🔍 Detected filled DCA: {dca_type} order {order_id[-8:]} @ ${fill_price:.6f}")
            
            return filled_dca_orders
            
        except Exception as e:
            self.logger.error(f"❌ Detect filled DCA orders error: {e}")
            return []
    
    def track_dca_order(self, symbol: str, dca_type: str, order_id: str, price: float, amount: float) -> None:
        """Track DCA order for fill detection"""
        try:
            if symbol not in self.strategy_metadata:
                self.strategy_metadata[symbol] = {}
            
            metadata = self.strategy_metadata[symbol]
            if 'tracked_dca_orders' not in metadata:
                metadata['tracked_dca_orders'] = {}
            
            metadata['tracked_dca_orders'][dca_type] = {
                'order_id': order_id,
                'price': price,
                'amount': amount,
                'created_at': datetime.now(),
                'filled_detected': False
            }
            
            self.logger.debug(f"📝 Tracking DCA order: {dca_type} - {order_id[-8:]} @ ${price:.6f}")
            
        except Exception as e:
            self.logger.error(f"❌ Track DCA order error: {e}")
    
    def get_and_clear_filled_dca_orders(self, symbol: str) -> list:
        """Get filled DCA orders and clear them from metadata"""
        try:
            metadata = self.strategy_metadata.get(symbol, {})
            filled_orders = metadata.get('filled_dca_orders', [])
            
            if filled_orders:
                # Clear the filled orders list
                metadata['filled_dca_orders'] = []
                self.logger.debug(f"🧹 Cleared {len(filled_orders)} filled DCA orders for {symbol}")
            
            return filled_orders
            
        except Exception as e:
            self.logger.error(f"❌ Get filled DCA orders error: {e}")
            return []
    
    async def update_trailing_stops(self, position: Position, current_price: float, config) -> None:
        """Update trailing stop loss if enabled"""
        if not config.risk.use_trailing_stop or not position.stop_loss:
            return
        
        try:
            trailing_distance = config.risk.trailing_stop_distance / 100
            
            if position.side == 'long':
                # For long, move stop up
                new_stop = current_price * (1 - trailing_distance)
                if new_stop > position.stop_loss:
                    if self.order_manager:
                        await self.order_manager.update_stop_loss(position, new_stop)
                    position.stop_loss = new_stop
                    self.logger.info(f"📈 Trailing stop updated: {position.symbol} -> ${new_stop:.6f}")
            else:
                # For short, move stop down
                new_stop = current_price * (1 + trailing_distance)
                if new_stop < position.stop_loss:
                    if self.order_manager:
                        await self.order_manager.update_stop_loss(position, new_stop)
                    position.stop_loss = new_stop
                    self.logger.info(f"📉 Trailing stop updated: {position.symbol} -> ${new_stop:.6f}")
                    
        except Exception as e:
            self.logger.error(f"❌ Update trailing stop error: {e}") 