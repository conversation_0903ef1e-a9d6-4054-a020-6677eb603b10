"""Order creation and management"""
import logging
from datetime import datetime
from typing import Optional, Dict, Any
from src.core.models import (
    Position, Order, OrderType, OrderSide, Signal, SignalDirection,
    TradeConfig
)
from src.core.risk.risk_manager import RiskManager
from src.core.risk.risk_metrics import RiskMetrics
from src.infrastructure.exchange.exchange_connector import ExchangeConnector


class OrderManager:
    """Manages order creation, execution, and lifecycle"""
    
    def __init__(self, config: TradeConfig, risk_manager: RiskManager, 
                 exchange: ExchangeConnector):
        self.config = config
        self.risk_manager = risk_manager
        self.exchange = exchange
        self.logger = logging.getLogger('OrderManager')
    
    async def create_entry_order(self, signal: Signal, risk_metrics: RiskMetrics,
                               current_price: float) -> Optional[Position]:
        """Create entry order for new position with optional bracket TP/SL"""
        try:
            # Validate signal entry price
            if signal.entry_price <= 0:
                self.logger.error(f"❌ Invalid signal entry price: ${signal.entry_price:.6f}")
                return None
            
            # Validate risk metrics
            if risk_metrics.position_size <= 0:
                self.logger.error(f"❌ Invalid position size: ${risk_metrics.position_size:.2f}")
                return None
            
            # Calculate order size in contracts using position calculator
            # Note: risk_metrics.position_size is now amount in USD, need to convert to contracts
            order_size = risk_metrics.position_size / signal.entry_price

            self.logger.info(f"🛡️ Position amount calculated: ${risk_metrics.position_size:.2f}")
            self.logger.debug(f"📊 Order contracts: ${risk_metrics.position_size:.2f} / ${signal.entry_price:.6f} = {order_size:.6f}")
            
            # Validate order size
            if order_size <= 0:
                self.logger.error(f"❌ Invalid order size calculated: {order_size:.6f}")
                return None
            
            # Create order params for perpetual futures with optional TP/SL
            order_params = self._get_perp_order_params(signal)
            
            # Validate signal direction before creating OrderSide
            if signal.direction is None:
                self.logger.error("❌ Signal direction is None")
                return None
            
            if signal.direction not in [SignalDirection.LONG, SignalDirection.SHORT]:
                self.logger.error(f"❌ Invalid signal direction: {signal.direction}")
                return None
            
            self.logger.debug(f"🔍 Signal direction: {signal.direction} (type: {type(signal.direction)})")
            
            # Create OrderSide safely
            order_side = OrderSide.BUY if signal.direction == SignalDirection.LONG else OrderSide.SELL
            
            # Debug log to verify OrderSide creation
            self.logger.debug(f"🔍 OrderSide created: {order_side} (type: {type(order_side)}) (value: {order_side.value})")
            
            # Create order
            order = Order(
                symbol=self.config.symbol,
                side=order_side,
                type=OrderType.MARKET if self.config.order_type == 'market' else OrderType.LIMIT,
                price=signal.entry_price if self.config.order_type == 'limit' else current_price,
                amount=order_size
            )
            
            # Additional debug log
            self.logger.debug(f"🔍 Order created with side: {order.side} (type: {type(order.side)}) (value: {order.side.value})")
            
            # Log bracket order details if TP/SL attached (Full mode)
            if 'takeProfit' in order_params or 'stopLoss' in order_params:
                self.logger.info(f"🎯 Creating Full bracket order with TP/SL attached:")
                if 'takeProfit' in order_params:
                    self.logger.info(f"   📈 Take Profit: ${order_params['takeProfit']}")
                if 'stopLoss' in order_params:
                    self.logger.info(f"   🛡️ Stop Loss: ${order_params['stopLoss']}")
                self.logger.info(f"   🔧 Mode: {order_params.get('tpslMode', 'Full')} (entire position)")
            
            # Place order based on order type
            if self.config.order_type == 'market':
                order_type_msg = "Full bracket market" if ('takeProfit' in order_params or 'stopLoss' in order_params) else "market"
                self.logger.info(f"🎯 Creating PERP {order_type_msg} order: {order.side.value} {order.amount:.6f} {self.config.symbol}")
                exchange_order = await self.exchange.create_market_order(
                    order.symbol, order.side, order.amount, order_params
                )
            else:
                order_type_msg = "Full bracket limit" if ('takeProfit' in order_params or 'stopLoss' in order_params) else "limit"
                self.logger.info(f"🎯 Creating PERP {order_type_msg} order: {order.side.value} {order.amount:.6f} @ ${order.price:.4f}")
                exchange_order = await self.exchange.create_limit_order(
                    order.symbol, order.side, order.amount, order.price, order_params
                )
            
            if exchange_order:
                # Create position with TP/SL from signal (may be overridden by bracket order)
                position = Position(
                    symbol=self.config.symbol,
                    side=signal.direction.value,
                    entry_price=signal.entry_price,
                    current_price=current_price,
                    contracts=order_size,
                    stop_loss=signal.stop_loss,
                    take_profit=signal.take_profit,
                    entry_order_id=exchange_order.order_id
                )
                
                # Check if bracket orders were attached
                has_bracket_orders = 'takeProfit' in order_params or 'stopLoss' in order_params
                if has_bracket_orders:
                    self.logger.info("✅ Full bracket order created - TP/SL managed by exchange")
                    # Mark that this position has bracket TP/SL managed by exchange
                    position.tp_order_id = "BRACKET_TP" if 'takeProfit' in order_params else None
                    position.sl_order_id = "BRACKET_SL" if 'stopLoss' in order_params else None
                else:
                    self.logger.info("✅ Entry order created - TP/SL will be created separately")
                
                # Log order
                order_id_short = exchange_order.order_id[:8] if exchange_order.order_id else "None"
                self.logger.info(
                    f"✅ PERP Entry order created: {order.side.value} {order.amount:.6f} @ ${order.price:.4f} "
                    f"Order ID: {order_id_short}"
                )
                
                return position
            else:
                self.logger.error("❌ Failed to create entry order - exchange returned None")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ CRITICAL: Entry order creation failed: {e}", exc_info=True)
            return None
    
    async def create_stop_loss_order(self, position: Position) -> Optional[str]:
        """Create stop loss order for position"""
        if not position.stop_loss:
            return None
            
        try:
            sl_side = OrderSide.SELL if position.side == 'long' else OrderSide.BUY
            order_params = self._get_perp_order_params()
            
            # Add stop loss specific params
            order_params.update({
                'stopPrice': position.stop_loss,
                'reduceOnly': True  # Important for futures
            })
            
            sl_order = await self.exchange.create_stop_loss_order(
                symbol=position.symbol,
                side=sl_side,
                amount=position.contracts,
                stop_price=position.stop_loss,
                params=order_params
            )
            
            if sl_order:
                self.logger.info(f"🛡️ Stop loss created at ${position.stop_loss:.4f} for PERP position")
                return sl_order.order_id
            
        except Exception as e:
            self.logger.error(f"❌ Create stop loss error: {e}")
            
        return None
    
    async def create_take_profit_order(self, position: Position) -> Optional[str]:
        """Create take profit order for position"""
        if not position.take_profit:
            return None
            
        try:
            tp_side = OrderSide.SELL if position.side == 'long' else OrderSide.BUY
            order_params = self._get_perp_order_params()
            
            # Add reduce only flag for futures
            order_params.update({
                'reduceOnly': True  # Important for futures
            })
            
            tp_order = await self.exchange.create_limit_order(
                symbol=position.symbol,
                side=tp_side,
                amount=position.contracts,
                price=position.take_profit,
                params=order_params
            )
            
            if tp_order:
                self.logger.info(f"🎯 Take profit created at ${position.take_profit:.4f} for PERP position")
                return tp_order.order_id
                
        except Exception as e:
            self.logger.error(f"❌ Create take profit error: {e}")
            
        return None
    
    async def close_position_market(self, position: Position) -> Optional[Order]:
        """Close position with market order"""
        try:
            # Create market order to close
            close_side = OrderSide.SELL if position.side == 'long' else OrderSide.BUY
            order_params = self._get_perp_order_params()
            
            # Add reduce only flag for futures
            order_params.update({
                'reduceOnly': True  # Important for futures
            })
            
            self.logger.info(f"🔄 Closing PERP position with market order: {close_side.value} {position.contracts:.6f}")
            
            close_order = await self.exchange.create_market_order(
                symbol=position.symbol,
                side=close_side,
                amount=position.contracts,
                params=order_params
            )
            
            if close_order:
                order_id_short = close_order.order_id[:8] if close_order.order_id else "None"
                self.logger.info(f"✅ PERP Position closed with market order ID: {order_id_short}")
                return close_order
            else:
                self.logger.error("❌ Failed to close position - exchange returned None")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Close position error: {e}", exc_info=True)
            return None
    
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order"""
        try:
            result = await self.exchange.cancel_order(order_id, symbol)
            if result:
                self.logger.info(f"✅ Order cancelled: {order_id[:8]}")
                return True
            else:
                self.logger.warning(f"⚠️ Failed to cancel order: {order_id[-8:] if order_id and len(order_id) >= 8 else order_id}")
                return False
        except Exception as e:
            self.logger.error(f"❌ Cancel order error for {order_id[-8:] if order_id and len(order_id) >= 8 else order_id}: {e}")
            return False
    
    async def update_stop_loss(self, position: Position, new_stop: float) -> bool:
        """Update stop loss order"""
        try:
            # Cancel old order if exists
            if position.sl_order_id:
                await self.cancel_order(position.sl_order_id, position.symbol)
            
            # Create new stop loss
            position.stop_loss = new_stop
            new_sl_order_id = await self.create_stop_loss_order(position)
            
            if new_sl_order_id:
                position.sl_order_id = new_sl_order_id
                self.logger.info(f"📈 Stop loss updated to ${new_stop:.4f}")
                return True
            else:
                self.logger.error("❌ Failed to create new stop loss order")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Update stop loss error: {e}")
            return False
    
    def _get_perp_order_params(self, signal: Signal = None) -> Dict[str, Any]:
        """Get order parameters for perpetual futures trading with optional Full mode TP/SL bracket"""
        
        # Base exchange-specific params
        if self.config.exchange.lower() == 'bybit':
            params = {
                'category': 'linear',  # Perpetual futures category
                'leverage': self.config.risk.leverage or 1.0
            }
        elif self.config.exchange.lower() == 'binance':
            params = {
                'type': 'future',  # Futures trading
                'leverage': self.config.risk.leverage or 1.0
            }
        else:
            # Default params for other exchanges
            params = {
                'leverage': self.config.risk.leverage or 1.0
            }
        
        # Add Full mode bracket TP/SL if enabled and signal has TP/SL values
        if (signal and 
            self.config.risk.tp_sl_immediately and 
            self.config.exchange.lower() == 'bybit'):  # Only for exchanges that support it
            
            # Add Take Profit if available
            if signal.take_profit and signal.take_profit > 0:
                params['takeProfit'] = str(signal.take_profit)
                params['tpOrderType'] = 'Market'  # Market order when TP triggered
                
                # Use Full mode instead of Partial (closes entire position)
                params['tpslMode'] = 'Full'
                
                self.logger.debug(f"🎯 Added Full bracket TP: ${signal.take_profit:.6f}")
            
            # Add Stop Loss if available  
            if signal.stop_loss and signal.stop_loss > 0:
                params['stopLoss'] = str(signal.stop_loss)
                params['slOrderType'] = 'Market'  # Market order when SL triggered
                
                # Use Full mode instead of Partial (closes entire position)
                params['tpslMode'] = 'Full'
                
                self.logger.debug(f"🛡️ Added Full bracket SL: ${signal.stop_loss:.6f}")
            
            # Add additional Bybit-specific TP/SL params if both TP and SL are set
            if signal.take_profit and signal.stop_loss:
                params['tpTriggerBy'] = 'LastPrice'  # Use last price to trigger
                params['slTriggerBy'] = 'LastPrice'  # Use last price to trigger
                
                self.logger.debug("📊 Full bracket order configured: Both TP & SL attached to entry order")
        
        return params
    
    def validate_entry_conditions(self, signal: Signal, current_price: float) -> bool:
        """Validate entry conditions before creating order"""
        # Validate prices
        if signal.entry_price <= 0:
            self.logger.error(f"❌ Invalid signal entry price: ${signal.entry_price:.6f}")
            return False
            
        if current_price <= 0:
            self.logger.error(f"❌ Invalid current price: ${current_price:.6f}")
            return False
        
        # Price should be near entry point
        price_deviation = abs(current_price - signal.entry_price) / signal.entry_price
        
        if price_deviation > 0.01:  # 1% max deviation
            self.logger.debug(f"❌ Price deviation too high: {price_deviation:.2%}")
            return False
        
        # For LONG, current price should be above entry
        if signal.direction == SignalDirection.LONG and current_price < signal.entry_price:
            return True
        
        # For SHORT, current price should be below entry  
        if signal.direction == SignalDirection.SHORT and current_price > signal.entry_price:
            return True
        
        self.logger.debug(f"❌ Entry conditions not met: {signal.direction.value} signal at ${signal.entry_price:.6f}, current ${current_price:.6f}")
        return False

    async def _check_sufficient_balance(self, symbol: str, usd_amount: float, leverage: float = 1.0) -> tuple[bool, float]:
        """Check if account has sufficient balance for order"""
        try:
            balance = await self.exchange.fetch_balance()
            
            if balance is None:
                return False, 0.0
                
            available_balance = 0.0
            
            if isinstance(balance, dict):
                # Try different balance structures
                usdt_balance = None
                
                # Priority 1: Check total balance first (most reliable for Bybit)
                if 'total' in balance and isinstance(balance['total'], dict):
                    usdt_balance = balance['total'].get('USDT')
                    if usdt_balance is not None:
                        try:
                            available_balance = float(usdt_balance) if usdt_balance != '' else 0.0
                            self.logger.debug(f"💰 Using total balance: ${available_balance:.2f}")
                        except (ValueError, TypeError):
                            usdt_balance = None
                
                # Priority 2: Check free balance if total not available
                if usdt_balance is None and 'free' in balance and isinstance(balance['free'], dict):
                    usdt_balance = balance['free'].get('USDT')
                    if usdt_balance is not None:
                        try:
                            available_balance = float(usdt_balance) if usdt_balance != '' else 0.0
                            self.logger.debug(f"💰 Using free balance: ${available_balance:.2f}")
                        except (ValueError, TypeError):
                            usdt_balance = None
                
                # Priority 3: Check USDT dict structure
                if usdt_balance is None and 'USDT' in balance and isinstance(balance['USDT'], dict):
                    # Try free first, then total as fallback
                    for key in ['free', 'total']:
                        if key in balance['USDT']:
                            usdt_balance = balance['USDT'].get(key)
                            if usdt_balance is not None:
                                try:
                                    available_balance = float(usdt_balance) if usdt_balance != '' else 0.0
                                    self.logger.debug(f"💰 Using USDT.{key} balance: ${available_balance:.2f}")
                                    break
                                except (ValueError, TypeError):
                                    continue
                
                # Priority 4: Parse Bybit coin object from info
                if usdt_balance is None and 'info' in balance:
                    try:
                        info = balance['info']
                        if isinstance(info, dict) and 'result' in info:
                            result = info['result']
                            if isinstance(result, dict) and 'list' in result and result['list']:
                                account = result['list'][0]
                                if 'coin' in account and isinstance(account['coin'], list):
                                    for coin in account['coin']:
                                        if isinstance(coin, dict) and coin.get('coin') == 'USDT':
                                            wallet_balance = coin.get('walletBalance')
                                            if wallet_balance is not None:
                                                try:
                                                    available_balance = float(wallet_balance)
                                                    self.logger.debug(f"💰 Using Bybit walletBalance: ${available_balance:.2f}")
                                                    break
                                                except (ValueError, TypeError):
                                                    continue
                    except Exception as e:
                        self.logger.debug(f"Could not parse Bybit coin object: {e}")
                
                # Priority 5: Direct USDT value as last resort
                if usdt_balance is None and available_balance == 0.0:
                    usdt_balance = balance.get('USDT')
                    if usdt_balance is not None:
                        try:
                            available_balance = float(usdt_balance) if usdt_balance != '' else 0.0
                            self.logger.debug(f"💰 Using direct USDT balance: ${available_balance:.2f}")
                        except (ValueError, TypeError):
                            pass
                        
            required_margin = usd_amount / leverage
            return available_balance >= required_margin, available_balance
            
        except Exception as e:
            self.logger.error(f"❌ Balance check error: {e}")
            return False, 0.0

    async def create_dca_order(self, symbol: str, side: str, entry_price: float,
                             position_size: float, dca_type: str) -> Optional[str]:
        """Create a DCA order with limit price"""
        try:
            # position_size is already USD amount from position_handler, not contracts
            usd_amount = position_size  # Fixed: position_size is already in USD
            leverage = getattr(self.config.risk, 'leverage', 1.0)

            # Check balance before creating order
            has_balance, available_balance = await self._check_sufficient_balance(symbol, usd_amount, leverage)
            
            if not has_balance:
                self.logger.warning(
                    f"⚠️ Insufficient balance for DCA {dca_type}: "
                    f"Available=${available_balance:.2f}, Required=${usd_amount/leverage:.2f} "
                    f"(${usd_amount:.2f} @ {leverage}x leverage)"
                )
                return None
            
            order_params = self._get_perp_order_params()
            
            # Validate side parameter
            if side is None:
                self.logger.error(f"❌ DCA {dca_type}: side parameter is None")
                return None
            
            if not isinstance(side, str):
                self.logger.error(f"❌ DCA {dca_type}: side must be string, got {type(side)}")
                return None
            
            side_lower = side.lower()
            if side_lower not in ['buy', 'sell']:
                self.logger.error(f"❌ DCA {dca_type}: invalid side '{side}', must be 'buy' or 'sell'")
                return None
            
            # Convert side to OrderSide enum
            order_side = OrderSide.BUY if side_lower == 'buy' else OrderSide.SELL

            # Convert USD amount to contracts for order execution
            contracts = position_size / entry_price  # position_size is USD amount

            # Debug log
            self.logger.debug(f"🔍 DCA {dca_type}: Converting side '{side}' to OrderSide.{order_side.name} (value: {order_side.value})")
            self.logger.debug(f"🔍 DCA {dca_type}: USD amount=${position_size:.2f}, contracts={contracts:.6f} @ ${entry_price:.6f}")

            order = await self.exchange.create_limit_order(
                symbol=symbol,
                side=order_side,
                amount=contracts,  # Fixed: use contracts instead of USD amount
                price=entry_price,
                params=order_params
            )
            
            if order:
                order_id_short = order.order_id[-8:] if order.order_id and len(order.order_id) >= 8 else order.order_id
                self.logger.info(f"📈 DCA {dca_type} order created: {side} {contracts:.6f} contracts (${position_size:.2f}) @ ${entry_price:.6f} - ID: {order_id_short}")
                return order.order_id
            else:
                self.logger.error(f"❌ Failed to create DCA {dca_type} order")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Create DCA {dca_type} order error: {e}", exc_info=True)
            self.logger.error(f"🔍 DCA Debug - side: {side}, entry_price: {entry_price}, position_size: {position_size}")
            return None

    async def modify_dca_order(self, order_id: str, symbol: str, side: str, 
                             entry_price: float, position_size: float, dca_type: str) -> Optional[str]:
        """Modify existing DCA order"""
        try:
            # Validate side parameter
            if side is None:
                self.logger.error(f"❌ Modify DCA {dca_type}: side parameter is None")
                return None
            
            if not isinstance(side, str):
                self.logger.error(f"❌ Modify DCA {dca_type}: side must be string, got {type(side)}")
                return None
            
            side_lower = side.lower()
            if side_lower not in ['buy', 'sell']:
                self.logger.error(f"❌ Modify DCA {dca_type}: invalid side '{side}', must be 'buy' or 'sell'")
                return None
            
            # Convert side to OrderSide enum
            order_side = OrderSide.BUY if side_lower == 'buy' else OrderSide.SELL

            # Convert USD amount to contracts for order execution
            contracts = position_size / entry_price  # position_size is USD amount

            # Debug log
            self.logger.debug(f"🔍 Modify DCA {dca_type}: Converting side '{side}' to OrderSide.{order_side.name} (value: {order_side.value})")
            self.logger.debug(f"🔍 Modify DCA {dca_type}: USD amount=${position_size:.2f}, contracts={contracts:.6f} @ ${entry_price:.6f}")

            modified_order = await self.exchange.edit_order(
                order_id=order_id,
                symbol=symbol,
                type='limit',
                side=order_side,
                amount=contracts,  # Fixed: use contracts instead of USD amount
                price=entry_price
            )
            
            if modified_order and modified_order.order_id:
                self.logger.info(f"🔄 DCA {dca_type} order modified: {order_id[-8:] if order_id and len(order_id) >= 8 else order_id} -> {side} {contracts:.6f} contracts (${position_size:.2f}) @ ${entry_price:.6f}")
                return modified_order.order_id
            else:
                self.logger.error(f"❌ Failed to modify DCA {dca_type} order {order_id[-8:] if order_id and len(order_id) >= 8 else order_id}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Modify DCA {dca_type} order error for {order_id[-8:] if order_id and len(order_id) >= 8 else order_id}: {e}", exc_info=True)
            self.logger.error(f"🔍 Modify DCA Debug - order_id: {order_id[-8:] if order_id and len(order_id) >= 8 else order_id}, side: {side}, entry_price: {entry_price}, position_size: {position_size}")
            return None