#!/usr/bin/env python3
"""
Telegram Integration Helper for Bot.sh
Provides functions to set/get credential profiles for bot.sh integration
"""

import os
import tempfile
import json
from pathlib import Path
from typing import Optional

class TelegramBotIntegration:
    """Helper class for Telegram bot to bot.sh integration"""
    
    def __init__(self):
        self.profile_file = "/tmp/traderbot_telegram_profile"
        self.session_profile_template = "/tmp/traderbot_telegram_profile_{}"
    
    def set_global_profile(self, profile_name: str) -> bool:
        """Set global profile for bot.sh to use"""
        try:
            with open(self.profile_file, 'w') as f:
                f.write(profile_name)
            return True
        except Exception as e:
            print(f"Error setting global profile: {e}")
            return False
    
    def set_session_profile(self, profile_name: str, session_id: str = None) -> bool:
        """Set session-specific profile for bot.sh to use"""
        try:
            if session_id:
                profile_file = self.session_profile_template.format(session_id)
            else:
                profile_file = self.profile_file
                
            with open(profile_file, 'w') as f:
                f.write(profile_name)
            return True
        except Exception as e:
            print(f"Error setting session profile: {e}")
            return False
    
    def get_current_profile(self) -> Optional[str]:
        """Get currently set profile"""
        try:
            if os.path.exists(self.profile_file):
                with open(self.profile_file, 'r') as f:
                    return f.read().strip()
        except Exception:
            pass
        return None
    
    def clear_profile(self) -> bool:
        """Clear current profile setting"""
        try:
            if os.path.exists(self.profile_file):
                os.remove(self.profile_file)
            return True
        except Exception as e:
            print(f"Error clearing profile: {e}")
            return False
    
    def create_bot_command(self, symbol: str, amount: float = 50, 
                          test_mode: bool = False, profile: str = None) -> str:
        """Create bot.sh command with proper credential integration"""
        
        cmd_parts = ["./bot.sh", "start", symbol, "--amount", str(amount)]
        
        if test_mode:
            cmd_parts.append("--test")
        else:
            cmd_parts.append("--live")
            
        if profile:
            cmd_parts.extend(["--profile", profile])
            
        return " ".join(cmd_parts)
    
    def execute_bot_command(self, symbol: str, amount: float = 50, 
                           test_mode: bool = False, profile: str = None) -> dict:
        """Execute bot.sh command and return result"""
        import subprocess
        
        # Set profile if provided
        if profile:
            self.set_global_profile(profile)
        
        # Create command
        cmd = self.create_bot_command(symbol, amount, test_mode, profile)
        
        try:
            result = subprocess.run(
                cmd, 
                shell=True, 
                capture_output=True, 
                text=True,
                cwd=os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            )
            
            return {
                'success': result.returncode == 0,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'command': cmd,
                'return_code': result.returncode
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'command': cmd
            }

# CLI interface
if __name__ == "__main__":
    import sys
    
    integration = TelegramBotIntegration()
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python3 telegram_integration.py set-profile <profile_name>")
        print("  python3 telegram_integration.py get-profile")
        print("  python3 telegram_integration.py clear-profile")
        print("  python3 telegram_integration.py create-command <symbol> [amount] [test|live] [profile]")
        sys.exit(1)
    
    command = sys.argv[1]
    
    if command == "set-profile" and len(sys.argv) >= 3:
        profile = sys.argv[2]
        if integration.set_global_profile(profile):
            print(f"✅ Profile set to: {profile}")
        else:
            print(f"❌ Failed to set profile: {profile}")
    
    elif command == "get-profile":
        profile = integration.get_current_profile()
        if profile:
            print(f"Current profile: {profile}")
        else:
            print("No profile set")
    
    elif command == "clear-profile":
        if integration.clear_profile():
            print("✅ Profile cleared")
        else:
            print("❌ Failed to clear profile")
    
    elif command == "create-command" and len(sys.argv) >= 3:
        symbol = sys.argv[2]
        amount = float(sys.argv[3]) if len(sys.argv) > 3 else 50
        test_mode = sys.argv[4] == "test" if len(sys.argv) > 4 else False
        profile = sys.argv[5] if len(sys.argv) > 5 else None
        
        cmd = integration.create_bot_command(symbol, amount, test_mode, profile)
        print(f"Command: {cmd}")
    
    else:
        print("❌ Invalid command")
        sys.exit(1)
