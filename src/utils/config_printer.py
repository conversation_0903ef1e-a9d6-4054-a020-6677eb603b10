"""Config printer utility - Đơn giản và robust"""
import os
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent.parent))

try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.tree import Tree
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False

from src.infrastructure.config.config_manager import ConfigManager


def print_config(config_file: str = 'configs/config.json'):
    """Print configuration in sections"""
    if RICH_AVAILABLE:
        print_config_rich(config_file)
    else:
        print_config_simple(config_file)


def print_config_rich(config_file: str):
    """Print config using Rich formatting"""
    console = Console()
    
    try:
        # Load config
        config_manager = ConfigManager(config_file)
        config = config_manager.load_config()
        
        # Header
        console.print("\n")
        header = Panel(
            "[bold cyan]🚀 Trading Bot Configuration[/bold cyan]\n"
            f"[dim]Loaded from: {config_file}[/dim]",
            style="cyan",
            expand=False
        )
        console.print(header)
        
        # Basic Configuration
        print_basic_config_rich(console, config)
        
        # Indicators Section
        print_indicators_config_rich(console, config)
        
        # DCA Section
        print_dca_config_rich(console, config)
        
        # Risk Management Section
        print_risk_config_rich(console, config)
        
        # API Status
        print_api_status_rich(console, config_manager)
        
        # Footer
        footer = Panel(
            "[bold green]✅ Configuration loaded successfully![/bold green]\n"
            "[dim]Ready for trading operations[/dim]",
            style="green",
            expand=False
        )
        console.print(footer)
        
    except Exception as e:
        error_panel = Panel(
            f"[bold red]❌ Error loading config:[/bold red]\n"
            f"[red]{str(e)}[/red]",
            style="red"
        )
        console.print(error_panel)


def print_basic_config_rich(console, config):
    """Print basic configuration section"""
    table = Table(title="🔧 Basic Configuration", show_header=True, header_style="bold blue")
    table.add_column("Setting", style="cyan", width=20)
    table.add_column("Value", style="white", width=30)
    table.add_column("Description", style="dim", width=40)
    
    basic_settings = [
        ("Symbol", config.symbol, "Trading pair"),
        ("Exchange", config.exchange.upper(), "Trading exchange"),
        ("Direction", config.direction.upper(), "Trading direction"),
        ("Amount", f"${config.amount}", "Order amount in USDT"),
        ("Order Type", getattr(config, 'order_type', 'limit'), "Default order type"),
        ("Test Mode", "🟢 ON" if config.use_test_mode else "🔴 OFF", "Safe testing mode"),
        ("Sandbox", "🟢 ON" if getattr(config, 'use_sandbox', False) else "🔴 OFF", "Use exchange sandbox"),
        ("Log Level", getattr(config, 'log_level', 'INFO'), "Logging verbosity"),
    ]
    
    for setting, value, desc in basic_settings:
        table.add_row(setting, str(value), desc)
    
    console.print(table)
    console.print()


def print_indicators_config_rich(console, config):
    """Print indicators configuration section"""
    indicators = config.indicators
    
    # Technical Indicators Tree
    tree = Tree("📈 Technical Indicators")
    
    # RSI Branch
    rsi_branch = tree.add(f"🔴 RSI (Period: {indicators.rsi_period})")
    
    # EMA Branch
    ema_branch = tree.add("📊 Moving Averages")
    for period in indicators.ema_periods:
        ema_branch.add(f"EMA {period} periods")
    
    # Timeframes Branch
    tf_branch = tree.add("⏰ Timeframes")
    for tf in indicators.timeframes:
        tf_branch.add(f"{tf}")
    
    # Bollinger Bands Branch
    bb_branch = tree.add("🎯 Bollinger Bands")
    bb_branch.add(f"Period: {indicators.bollinger_bands['period']}")
    bb_branch.add(f"Standard Deviation: {indicators.bollinger_bands['std']}")
    
    # MACD Branch
    macd_branch = tree.add("⚡ MACD")
    macd_branch.add(f"Fast: {indicators.macd['fast']}")
    macd_branch.add(f"Slow: {indicators.macd['slow']}")
    macd_branch.add(f"Signal: {indicators.macd['signal']}")
    
    # Signal Thresholds Branch
    threshold_branch = tree.add("🎯 Signal Thresholds")
    threshold_branch.add(f"LONG Signal: {indicators.long_signal_threshold}")
    threshold_branch.add(f"SHORT Signal: {indicators.short_signal_threshold}")
    threshold_branch.add(f"Validation: {indicators.signal_validation_threshold}")
    
    # Technical Thresholds Branch
    tech_threshold_branch = tree.add("📊 Technical Thresholds")
    tech_threshold_branch.add(f"RSI Oversold: <{indicators.rsi_oversold_threshold}")
    tech_threshold_branch.add(f"RSI Overbought: >{indicators.rsi_overbought_threshold}")
    tech_threshold_branch.add(f"Bollinger Lower: <{indicators.bollinger_lower_threshold}")
    tech_threshold_branch.add(f"Bollinger Upper: >{indicators.bollinger_upper_threshold}")
    tech_threshold_branch.add(f"Volume Multiplier: {indicators.volume_threshold_multiplier}x")
    
    console.print(tree)
    console.print()


def print_dca_config_rich(console, config):
    """Print DCA configuration section"""
    dca = config.dca
    
    # DCA Overview Table
    table = Table(title="💰 DCA (Dollar Cost Averaging)", show_header=True, header_style="bold yellow")
    table.add_column("Setting", style="cyan", width=25)
    table.add_column("Value", style="white", width=20)
    table.add_column("Impact", style="dim", width=45)
    
    dca_settings = [
        ("Status", "🟢 ENABLED" if dca.enabled else "🔴 DISABLED", "DCA strategy active"),
        ("Max Orders", str(dca.max_orders), "Maximum DCA orders per position"),
        ("Price Deviation", f"{dca.price_deviation}%", "Price drop to trigger DCA"),
        ("Volume Scale", f"{dca.volume_scale}x", "Each DCA order size multiplier"),
    ]
    
    for setting, value, impact in dca_settings:
        table.add_row(setting, value, impact)
    
    console.print(table)
    
    # DCA Calculation Example
    if dca.enabled:
        calc_panel = create_dca_calculation_panel(config)
        console.print(calc_panel)
    
    console.print()


def create_dca_calculation_panel(config):
    """Create DCA calculation example panel"""
    base_amount = config.amount
    multiplier = config.dca.volume_scale
    max_orders = config.dca.max_orders
    
    calc_text = "[bold]💡 DCA Order Calculation:[/bold]\n\n"
    
    total = 0
    for i in range(max_orders + 1):  # +1 for initial order
        if i == 0:
            amount = base_amount
            calc_text += f"Initial Order: ${amount}\n"
        else:
            amount = base_amount * (multiplier ** i)
            calc_text += f"DCA Level {i}: ${amount:.2f}\n"
        total += amount
    
    calc_text += f"\n[bold yellow]Total Max Investment: ${total:.2f}[/bold yellow]"
    
    # Risk warning if total exceeds reasonable amount
    if hasattr(config, 'risk') and hasattr(config.risk, 'max_position_size'):
        if total > config.risk.max_position_size:
            calc_text += f"\n[bold red]⚠️  WARNING: Exceeds max position size (${config.risk.max_position_size})[/bold red]"
    
    return Panel(calc_text, title="🧮 DCA Calculation", border_style="yellow")


def print_risk_config_rich(console, config):
    """Print risk management configuration"""
    risk = config.risk
    
    # Risk Table
    table = Table(title="🛡️ Risk Management", show_header=True, header_style="bold red")
    table.add_column("Risk Parameter", style="cyan", width=25)
    table.add_column("Value", style="white", width=15)
    table.add_column("Protection Level", style="dim", width=40)
    
    risk_settings = [
        ("Capital Source", "Exchange Balance", "Capital fetched from exchange"),
        ("Min Position Size", f"${risk.min_position_size}", "Minimum position value"),
        ("Max Position Size", f"${risk.max_position_size}", "Maximum position value"),
        ("Max Position %", f"{risk.max_position_percentage}%", "Max % of capital per position"),
        ("Stop Loss", f"{risk.default_stop_loss}%", "Maximum loss per position"),
        ("Take Profit", f"{risk.default_take_profit}%", "Target profit per position"),
        ("Risk Per Trade", f"{risk.risk_per_trade}%", "Percentage of capital at risk"),
        ("Max Daily Loss", f"{risk.daily_loss_limit}%", "Daily loss limit"),
        ("Trailing Stop", "🟢 ON" if risk.use_trailing_stop else "🔴 OFF", "Dynamic stop loss"),
    ]
    
    for param, value, protection in risk_settings:
        table.add_row(param, str(value), protection)
    
    console.print(table)
    
    # Risk Level Assessment
    risk_level = assess_risk_level(config)
    risk_panel = Panel(
        f"[bold]Risk Assessment: {risk_level['level']}[/bold]\n"
        f"{risk_level['description']}",
        title="⚖️ Risk Level",
        border_style=risk_level['color']
    )
    console.print(risk_panel)
    console.print()


def assess_risk_level(config):
    """Assess overall risk level of configuration"""
    risk_score = 0
    
    # Check stop loss
    if config.risk.default_stop_loss <= 1:
        risk_score += 1  # Very tight stop loss
    elif config.risk.default_stop_loss <= 2:
        risk_score += 2  # Reasonable stop loss
    else:
        risk_score += 3  # Loose stop loss
    
    # Check risk per trade
    if config.risk.risk_per_trade <= 1:
        risk_score += 1  # Conservative
    elif config.risk.risk_per_trade <= 2:
        risk_score += 2  # Moderate
    else:
        risk_score += 3  # Aggressive
    
    # Check DCA multiplier
    if config.dca.enabled and config.dca.volume_scale > 2:
        risk_score += 2  # High DCA scaling
    
    # Determine risk level
    if risk_score <= 3:
        return {
            "level": "🟢 LOW RISK",
            "description": "Conservative settings with good protection",
            "color": "green"
        }
    elif risk_score <= 5:
        return {
            "level": "🟡 MODERATE RISK",
            "description": "Balanced approach with reasonable protection",
            "color": "yellow"
        }
    else:
        return {
            "level": "🔴 HIGH RISK",
            "description": "Aggressive settings - monitor closely",
            "color": "red"
        }


def print_api_status_rich(console, config_manager):
    """Print API credentials status"""
    credentials = config_manager.get_api_credentials()
    
    table = Table(title="🔑 API Configuration", show_header=True, header_style="bold magenta")
    table.add_column("Parameter", style="cyan", width=20)
    table.add_column("Status", style="white", width=15)
    table.add_column("Notes", style="dim", width=40)
    
    api_key_status = "🟢 SET" if credentials.get('api_key') else "🔴 MISSING"
    api_secret_status = "🟢 SET" if credentials.get('api_secret') else "🔴 MISSING"
    
    # Get dynamic environment variable names
    api_key_var = credentials.get('api_key_var', 'BYBIT_API_KEY')
    api_secret_var = credentials.get('api_secret_var', 'BYBIT_API_SECRET')
    
    api_info = [
        ("API Key", api_key_status, f"{api_key_var} environment variable"),
        ("API Secret", api_secret_status, f"{api_secret_var} environment variable"),
        ("Test Mode", "🟢 SAFE" if config_manager._raw_config.get('use_test_mode') else "⚠️ LIVE", "Trading mode status"),
    ]
    
    for param, status, notes in api_info:
        table.add_row(param, status, notes)
    
    console.print(table)
    console.print()


def print_config_simple(config_file: str):
    """Print config using simple 3-column table format"""
    try:
        config_manager = ConfigManager(config_file)
        config = config_manager.load_config()
        
        print("\n" + "="*105)
        print("🚀 TRADING BOT CONFIGURATION")
        print("="*105)
        

        
        # Create sections for 3-column layout
        # Section 1: Basic Settings
        basic_section = [
            "🔧 BASIC SETTINGS",
            f"📊 Symbol: {config.symbol}",
            f"🏦 Exchange: {config.exchange.upper()}",
            f"📈 Direction: {config.direction}",
            f"💰 Amount: ${config.amount}",
            f"🧪 Test Mode: {'🟢 ON' if config.use_test_mode else '🔴 OFF'}",
            f"📋 Order Type: {getattr(config, 'order_type', 'limit')}",
            "",  # Empty for padding
            "",  # Empty for padding
        ]
        
        # Section 2: Technical Indicators  
        ema_str = ', '.join(map(str, config.indicators.ema_periods))
        tf_str = ', '.join(config.indicators.timeframes)
        tech_section = [
            "📈 TECHNICAL INDICATORS",
            f"📊 RSI Period: {config.indicators.rsi_period}",
            f"📈 EMA Periods: {ema_str}",
            f"⏰ Timeframes: {tf_str}",
            f"🎯 Bollinger: {config.indicators.bollinger_bands['period']} periods",
            f"⚡ MACD Fast: {config.indicators.macd['fast']}",
            f"⚡ MACD Slow: {config.indicators.macd['slow']}",
            f"⚡ MACD Signal: {config.indicators.macd['signal']}",
            "",  # Empty for padding
        ]
        
        # Section 3: Signal Thresholds
        signal_section = [
            "🎯 SIGNAL THRESHOLDS",
            f"🔸 LONG Signal: {config.indicators.long_signal_threshold}",
            f"🔸 SHORT Signal: {config.indicators.short_signal_threshold}",
            f"🔸 Validation: {config.indicators.signal_validation_threshold}",
            f"📊 RSI Oversold: <{config.indicators.rsi_oversold_threshold}",
            f"📊 RSI Overbought: >{config.indicators.rsi_overbought_threshold}",
            f"🎯 Bollinger Lower: <{config.indicators.bollinger_lower_threshold}",
            f"🎯 Bollinger Upper: >{config.indicators.bollinger_upper_threshold}",
            f"📈 Volume Multiplier: {config.indicators.volume_threshold_multiplier}x",
        ]
        
        # Print 3-column sections
        print(f"\n{'='*34} {'='*34} {'='*35}")
        max_len = max(len(basic_section), len(tech_section), len(signal_section))
        
        for i in range(max_len):
            left = basic_section[i] if i < len(basic_section) else ""
            middle = tech_section[i] if i < len(tech_section) else ""
            right = signal_section[i] if i < len(signal_section) else ""
            
            # Make title bold by showing in uppercase
            if i == 0:
                print(f"{left:<35} {middle:<35} {right:<35}")
                print(f"{'─'*34} {'─'*34} {'─'*35}")
            else:
                print(f"{left:<35} {middle:<35} {right:<35}")
        
        # Second row of sections
        print(f"\n{'='*34} {'='*34} {'='*35}")
        
        # Section 4: DCA Configuration
        dca_section = [
            "💰 DCA CONFIGURATION",
            f"🟢 Status: {'ENABLED' if config.dca.enabled else 'DISABLED'}",
            f"🔢 Max Orders: {config.dca.max_orders}",
            f"📉 Price Deviation: {config.dca.price_deviation}%",
            f"📊 Volume Scale: {config.dca.volume_scale}x",
            "",  # Empty for padding
            "",  # Empty for padding
            "",  # Empty for padding
            "",  # Empty for padding
        ]
        
        # Section 5: Risk Management
        risk_section = [
            "🛡️ RISK MANAGEMENT",
            f"💰 Capital Source: Exchange Balance",
            f"📏 Min Position: ${config.risk.min_position_size}",
            f"📊 Max Position: ${config.risk.max_position_size}",
            f"📈 Max Position %: {config.risk.max_position_percentage}%",
            f"🛑 Stop Loss: {config.risk.default_stop_loss}%",
            f"🎯 Take Profit: {config.risk.default_take_profit}%",
            f"⚖️ Risk Per Trade: {config.risk.risk_per_trade}%",
            f"📉 Daily Loss Limit: {config.risk.daily_loss_limit}%",
        ]
        
        # Section 6: API Status
        credentials = config_manager.get_api_credentials()
        api_key_var = credentials.get('api_key_var', 'BYBIT_API_KEY')
        api_secret_var = credentials.get('api_secret_var', 'BYBIT_API_SECRET')
        
        api_section = [
            "🔑 API STATUS",
            f"🔑 API Key: {'🟢 SET' if credentials.get('api_key') else '🔴 MISSING'}",
            f"🔐 API Secret: {'🟢 SET' if credentials.get('api_secret') else '🔴 MISSING'}",
            f"🧪 Test Mode: {'🟢 SAFE' if config.use_test_mode else '⚠️ LIVE'}",
            f"📊 Log Level: {getattr(config, 'log_level', 'INFO')}",
            f"🔧 API Key Var: {api_key_var}",
            f"🔧 API Secret Var: {api_secret_var}",
            f"🔄 Trailing Stop: {'🟢 ON' if config.risk.use_trailing_stop else '🔴 OFF'}",
            f"⏰ Cooldown: {config.risk.loss_cooldown_minutes} min",
        ]
        
        # Print second row of 3-column sections
        max_len2 = max(len(dca_section), len(risk_section), len(api_section))
        
        for i in range(max_len2):
            left = dca_section[i] if i < len(dca_section) else ""
            middle = risk_section[i] if i < len(risk_section) else ""
            right = api_section[i] if i < len(api_section) else ""
            
            if i == 0:
                print(f"{left:<35} {middle:<35} {right:<35}")
                print(f"{'─'*34} {'─'*34} {'─'*35}")
            else:
                print(f"{left:<35} {middle:<35} {right:<35}")
        
        # DCA Calculation section (if enabled)
        if config.dca.enabled:
            print(f"\n{'='*105}")
            print("💡 DCA CALCULATION")
            print("─" * 105)
            
            calc_items = []
            total = 0
            for i in range(config.dca.max_orders + 1):
                if i == 0:
                    amount = config.amount
                    calc_items.append(f"🏁 Initial Order: ${amount}")
                else:
                    amount = config.amount * (config.dca.volume_scale ** i)
                    calc_items.append(f"📈 DCA Level {i}: ${amount:.2f}")
                total += amount
            calc_items.append(f"💰 TOTAL MAX: ${total:.2f}")
            
            # Add warning if total exceeds max position size
            if total > config.risk.max_position_size:
                calc_items.append(f"⚠️  WARNING: Exceeds max position (${config.risk.max_position_size:.2f})")
            
            # Ensure we have enough items for 3-column layout
            while len(calc_items) % 3 != 0:
                calc_items.append("")
            
            # Print in 3 columns
            for i in range(0, len(calc_items), 3):
                left = calc_items[i] if i < len(calc_items) else ""
                middle = calc_items[i + 1] if i + 1 < len(calc_items) else ""
                right = calc_items[i + 2] if i + 2 < len(calc_items) else ""
                print(f"{left:<35} {middle:<35} {right:<35}")
        
        print("\n" + "="*105)
        print("✅ Configuration loaded successfully!")
        print("="*105)
        
    except Exception as e:
        print(f"\n❌ Error loading config: {e}")


if __name__ == '__main__':
    import sys
    
    config_file = sys.argv[1] if len(sys.argv) > 1 else 'configs/config.json'
    print_config(config_file) 