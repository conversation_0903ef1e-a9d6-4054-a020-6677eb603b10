"""
Credential utilities for AutoTrader application
Centralized credential management functions
"""

import json
import os
from pathlib import Path
from typing import Dict, Optional, Tuple

from .constants import (
    CREDENTIALS_DIR,
    CREDENTIAL_FILE_PERMISSIONS,
    BYBIT_API_KEY_VAR,
    BYBIT_API_SECRET_VAR,
    BYBIT_SECRET_KEY_VAR,
    DEFAULT_CREDENTIAL_PROFILE,
    get_credential_file_path,
    ensure_directories
)


class CredentialManager:
    """Centralized credential management"""
    
    def __init__(self):
        ensure_directories()
    
    def store_credentials(self, profile: str, api_key: str, api_secret: str, 
                         display_name: str = None, created_by: str = 'system') -> bool:
        """Store credentials in JSON format"""
        try:
            if not display_name:
                display_name = profile
            
            cred_data = {
                'profile': profile,
                'api_key': api_key,
                'api_secret': api_secret,
                'display_name': display_name,
                'created': created_by,
                'version': '1.0'
            }
            
            json_file = get_credential_file_path(profile, 'json')
            json_file.write_text(json.dumps(cred_data, indent=2))
            json_file.chmod(CREDENTIAL_FILE_PERMISSIONS)
            
            return True
        except Exception:
            return False
    
    def load_credentials(self, profile: str) -> Optional[Dict[str, str]]:
        """Load credentials from file"""
        # Try JSON format first
        json_file = get_credential_file_path(profile, 'json')
        if json_file.exists():
            try:
                cred_data = json.loads(json_file.read_text())
                return {
                    'api_key': cred_data.get('api_key', ''),
                    'api_secret': cred_data.get('api_secret', ''),
                    'display_name': cred_data.get('display_name', profile),
                    'profile': profile
                }
            except Exception:
                pass
        
        # Try legacy .env format
        env_file = get_credential_file_path(profile, 'env')
        if env_file.exists():
            try:
                credentials = {}
                for line in env_file.read_text().splitlines():
                    line = line.strip()
                    if line and '=' in line:
                        key, value = line.split('=', 1)
                        credentials[key.strip()] = value.strip()
                
                # Get display name from .display file
                display_file = get_credential_file_path(profile, 'display')
                display_name = profile
                if display_file.exists():
                    display_name = display_file.read_text().strip()
                
                return {
                    'api_key': credentials.get(BYBIT_API_KEY_VAR, ''),
                    'api_secret': credentials.get(BYBIT_API_SECRET_VAR, ''),
                    'display_name': display_name,
                    'profile': profile
                }
            except Exception:
                pass
        
        return None
    
    def list_profiles(self) -> list:
        """List all available credential profiles"""
        profiles = []
        
        # Get JSON profiles
        for json_file in CREDENTIALS_DIR.glob('*.json'):
            try:
                cred_data = json.loads(json_file.read_text())
                profiles.append({
                    'profile': cred_data.get('profile', json_file.stem),
                    'display_name': cred_data.get('display_name', json_file.stem),
                    'format': 'json'
                })
            except Exception:
                profiles.append({
                    'profile': json_file.stem,
                    'display_name': json_file.stem,
                    'format': 'json',
                    'error': True
                })
        
        # Get legacy .env profiles
        for env_file in CREDENTIALS_DIR.glob('*.env'):
            profile = env_file.stem
            # Skip if already have JSON version
            if any(p['profile'] == profile for p in profiles):
                continue
                
            display_file = get_credential_file_path(profile, 'display')
            display_name = profile
            if display_file.exists():
                try:
                    display_name = display_file.read_text().strip()
                except Exception:
                    pass
            
            profiles.append({
                'profile': profile,
                'display_name': display_name,
                'format': 'env'
            })
        
        return sorted(profiles, key=lambda x: x['profile'])
    
    def delete_profile(self, profile: str) -> bool:
        """Delete a credential profile"""
        try:
            deleted = False
            
            # Delete JSON file
            json_file = get_credential_file_path(profile, 'json')
            if json_file.exists():
                json_file.unlink()
                deleted = True
            
            # Delete legacy files
            env_file = get_credential_file_path(profile, 'env')
            if env_file.exists():
                env_file.unlink()
                deleted = True
            
            display_file = get_credential_file_path(profile, 'display')
            if display_file.exists():
                display_file.unlink()
                deleted = True
            
            return deleted
        except Exception:
            return False
    
    def set_environment_variables(self, profile: str) -> bool:
        """Load credentials into environment variables"""
        cred_data = self.load_credentials(profile)
        if not cred_data:
            return False
        
        os.environ[BYBIT_API_KEY_VAR] = cred_data['api_key']
        os.environ[BYBIT_API_SECRET_VAR] = cred_data['api_secret']
        return True
    
    def get_export_commands(self, profile: str) -> Optional[Tuple[str, str]]:
        """Get export commands for shell"""
        cred_data = self.load_credentials(profile)
        if not cred_data:
            return None
        
        return (
            f"export {BYBIT_API_KEY_VAR}='{cred_data['api_key']}'",
            f"export {BYBIT_API_SECRET_VAR}='{cred_data['api_secret']}'"
        )


# Global instance
credential_manager = CredentialManager()


# Convenience functions
def store_credentials(profile: str, api_key: str, api_secret: str, 
                     display_name: str = None, created_by: str = 'system') -> bool:
    """Store credentials"""
    return credential_manager.store_credentials(profile, api_key, api_secret, display_name, created_by)


def load_credentials(profile: str) -> Optional[Dict[str, str]]:
    """Load credentials"""
    return credential_manager.load_credentials(profile)


def list_profiles() -> list:
    """List credential profiles"""
    return credential_manager.list_profiles()


def delete_profile(profile: str) -> bool:
    """Delete credential profile"""
    return credential_manager.delete_profile(profile)


def set_environment_variables(profile: str) -> bool:
    """Set environment variables from profile"""
    return credential_manager.set_environment_variables(profile)


def get_export_commands(profile: str) -> Optional[Tuple[str, str]]:
    """Get export commands"""
    return credential_manager.get_export_commands(profile)


def resolve_credentials(profile: str = None, cmd_api_key: str = None, 
                       cmd_api_secret: str = None) -> Tuple[bool, str, str]:
    """
    Resolve credentials with priority system:
    1. Profile credentials (highest priority)
    2. Command line arguments
    3. Environment variables
    
    Returns: (success, api_key, api_secret)
    """
    # Priority 1: Profile credentials
    if profile:
        cred_data = load_credentials(profile)
        if cred_data:
            return True, cred_data['api_key'], cred_data['api_secret']
    
    # Priority 2: Command line arguments
    if cmd_api_key and cmd_api_secret:
        return True, cmd_api_key, cmd_api_secret
    
    # Priority 3: Environment variables
    env_api_key = os.environ.get(BYBIT_API_KEY_VAR, '')
    env_api_secret = os.environ.get(BYBIT_API_SECRET_VAR) or os.environ.get(BYBIT_SECRET_KEY_VAR, '')
    
    if env_api_key and env_api_secret:
        return True, env_api_key, env_api_secret
    
    return False, '', ''
