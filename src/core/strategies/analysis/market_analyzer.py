"""Market analysis and signal generation logic"""
import logging
from typing import Optional, List
from src.core.models import Signal, SignalDirection, TradeConfig, Candle
from src.core.strategies.indicators.technical_indicators import TechnicalIndicators
from src.core.strategies.calculators.tp_sl_calculator import TPSLCalculator


class MarketAnalyzer:
    """Analyzes market conditions and generates trading signals"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        self.indicators_config = config.indicators
        self.logger = logging.getLogger('DCAStrategy')
        
        # Initialize unified TP/SL calculator
        self.tp_sl_calculator = TPSLCalculator(config)
    
    def analyze_market_conditions(self, indicators: TechnicalIndicators, candles: List[Candle]) -> Optional[Signal]:
        """Main entry point for market analysis and signal generation"""
        current_price = candles[-1].close
        self.logger.debug(f"🔍 Market analysis starting with current price: ${current_price:.4f}")
        self.logger.debug(f"🎯 Bot direction: {self.config.direction}")
        
        # Determine market direction based on configured strategy
        if self.config.direction.upper() == 'LONG':
            self.logger.debug("📊 Analyzing LONG-only strategy...")
            return self._analyze_long_signal(indicators, current_price, candles)
        elif self.config.direction.upper() == 'SHORT':
            self.logger.debug("📊 Analyzing SHORT-only strategy...")
            return self._analyze_short_signal(indicators, current_price, candles)
        else:
            # Auto direction - choose best signal
            return self._analyze_auto_direction(indicators, current_price, candles)
    
    def analyze_planned_pair(self, indicators: TechnicalIndicators, candles: List[Candle]) -> Optional[Signal]:
        """
        Generate signal using planned pair logic - no threshold checking
        This is for stink bid strategy where we always want to place orders
        """
        current_price = candles[-1].close
        self.logger.debug(f"🎯 Planned pair analysis starting with current price: ${current_price:.4f}")
        
        # Use proper EMA values as in bot_dca.py
        # EMA_34 = indicators.ema_fast, EMA_89 = indicators.ema_slow, EMA_120 = indicators.ema_trend
        ema_34 = indicators.ema_fast
        ema_89 = indicators.ema_slow
        ema_120 = indicators.ema_trend  # Proper EMA 120 from config
        
        self.logger.info(f"\033[1;93m📊 EMAs: EMA_34=${ema_34:.5f}, EMA_89=${ema_89:.5f}, EMA_120=${ema_120:.5f}\033[0m")
        
        # Determine market direction based on EMA trend (exact logic from bot_dca.py)
        market_direction = 'LONG' if ema_89 > ema_120 else 'SHORT'
        self.logger.info(
            f"\033[1;93m📈 Market direction: {market_direction} (EMA_89 {'>' if ema_89 > ema_120 else '<'} EMA_120)\033[0m"
        )
        
        # Check configured direction - logic from bot_dca.py
        if self.config.direction.upper() == 'AUTO':
            signal_direction = market_direction
            self.logger.info(f"🎯 AUTO mode - using market direction: {signal_direction}")
        else:
            # Fixed: Only proceed if market direction matches config direction
            config_direction = self.config.direction.upper()
            if market_direction == config_direction:
                signal_direction = config_direction
                self.logger.info(f"\033[1;32m🎯 Direction match: market={market_direction} == config={config_direction}\033[0m")
            else:
                self.logger.info(f"\033[1;31m❌ Direction mismatch: market={market_direction} != config={config_direction}. Skipping signal.\033[0m")
                return None
        
        # Check if price is above or below EMA_34
        price_condition = (
            (signal_direction == "LONG" and current_price > ema_34) or
            (signal_direction == "SHORT" and current_price < ema_34)
        )
        if not price_condition:
            if signal_direction == "LONG":
                self.logger.info(f"❌ Price condition not met for LONG: Current price ${current_price:.4f} must be > EMA_34 ${ema_34:.4f} (difference: {current_price - ema_34:+.4f})")
            else:
                self.logger.info(f"❌ Price condition not met for SHORT: Current price ${current_price:.4f} must be < EMA_34 ${ema_34:.4f} (difference: {current_price - ema_34:+.4f})")
            return None
        # ----------------------------------------
        
        # Generate signal based on direction
        if signal_direction == 'LONG':
            return self._analyze_planned_pair_long(indicators, current_price, candles)
        elif signal_direction == 'SHORT':
            return self._analyze_planned_pair_short(indicators, current_price, candles)
        else:
            self.logger.warning(f"❌ Unknown direction: {signal_direction}")
            return None
    
    def _analyze_planned_pair_long(self, indicators: TechnicalIndicators, current_price: float, candles: List[Candle]) -> Optional[Signal]:
        """
        Planned pair LONG signal analysis - based on planned pair logic
        Entry condition: current_price > ema_34 (stink bid approach)
        """
        ema_34 = indicators.ema_fast
        
        # Entry condition from bot_dca.py: price > EMA_34
        price_above_ema = current_price > ema_34
        self.logger.debug(f"📈 LONG Entry Check: ${current_price:.4f} > EMA_34=${ema_34:.4f} = {price_above_ema}")
        
        # Calculate levels based on planned pair logic using unified calculator
        entry_price = ema_34  # Entry at EMA_34 (stink bid)
        
        # Use unified TP/SL calculator with appropriate strategy
        strategy_type = self.tp_sl_calculator.get_strategy_type_for_signal('planned_pair')
        take_profit, stop_loss = self.tp_sl_calculator.calculate_tp_sl(
            entry_price=entry_price,
            direction=SignalDirection.LONG,
            strategy_type=strategy_type,
            indicators=indicators,
            current_price=current_price
        )
        
        # Validate TP/SL levels
        validation = self.tp_sl_calculator.validate_tp_sl_levels(
            entry_price, take_profit, stop_loss, SignalDirection.LONG
        )
        
        if not validation['valid']:
            self.logger.error(f"❌ Invalid TP/SL levels: {validation['issues']}")
            return None
        
        if validation['warnings']:
            for warning in validation['warnings']:
                self.logger.warning(f"⚠️ TP/SL warning: {warning}")
        
        # Calculate signal strength (simple approach)
        signal_strength = 1.0 if price_above_ema else 0.6  # High strength for stink bid
        
        # Calculate risk-reward ratio for logging
        risk_reward = self.tp_sl_calculator.calculate_risk_reward_ratio(
            entry_price, take_profit, stop_loss, SignalDirection.LONG
        )
        
        self.logger.info(f"🎯 PLANNED PAIR LONG SIGNAL! Entry=${entry_price:.4f}, TP=${take_profit:.4f}, SL=${stop_loss:.4f} (R:R={risk_reward:.2f})")
        self.logger.info(f"📊 Price above EMA34: {price_above_ema}, Signal strength: {signal_strength:.2f}, Strategy: {strategy_type}")
        
        return self._create_planned_pair_signal(
            direction=SignalDirection.LONG,
            current_price=current_price,
            entry_price=entry_price,
            take_profit=take_profit,
            stop_loss=stop_loss,
            signal_strength=signal_strength,
            indicators=indicators,
            reasons=['planned_pair_long', 'stink_bid_ema34', f'price_above_ema={price_above_ema}', f'strategy={strategy_type}']
        )
    
    def _analyze_planned_pair_short(self, indicators: TechnicalIndicators, current_price: float, candles: List[Candle]) -> Optional[Signal]:
        """
        Planned pair SHORT signal analysis - based on planned pair logic
        Entry condition: current_price < ema_34 (stink bid approach)
        """
        ema_34 = indicators.ema_fast
        
        # Entry condition from bot_dca.py: price < EMA_34
        price_below_ema = current_price < ema_34
        self.logger.debug(f"📉 SHORT Entry Check: ${current_price:.4f} < EMA_34=${ema_34:.4f} = {price_below_ema}")
        
        # Calculate levels based on planned pair logic using unified calculator
        entry_price = ema_34  # Entry at EMA_34 (stink bid)
        
        # Use unified TP/SL calculator with appropriate strategy
        strategy_type = self.tp_sl_calculator.get_strategy_type_for_signal('planned_pair')
        take_profit, stop_loss = self.tp_sl_calculator.calculate_tp_sl(
            entry_price=entry_price,
            direction=SignalDirection.SHORT,
            strategy_type=strategy_type,
            indicators=indicators,
            current_price=current_price
        )
        
        # Validate TP/SL levels
        validation = self.tp_sl_calculator.validate_tp_sl_levels(
            entry_price, take_profit, stop_loss, SignalDirection.SHORT
        )
        
        if not validation['valid']:
            self.logger.error(f"❌ Invalid TP/SL levels: {validation['issues']}")
            return None
        
        if validation['warnings']:
            for warning in validation['warnings']:
                self.logger.warning(f"⚠️ TP/SL warning: {warning}")
        
        # Calculate signal strength (simple approach)
        signal_strength = 1.0 if price_below_ema else 0.6  # High strength for stink bid
        
        # Calculate risk-reward ratio for logging
        risk_reward = self.tp_sl_calculator.calculate_risk_reward_ratio(
            entry_price, take_profit, stop_loss, SignalDirection.SHORT
        )
        
        self.logger.info(f"🎯 PLANNED PAIR SHORT SIGNAL! Entry=${entry_price:.4f}, TP=${take_profit:.4f}, SL=${stop_loss:.4f} (R:R={risk_reward:.2f})")
        self.logger.info(f"📊 Price below EMA34: {price_below_ema}, Signal strength: {signal_strength:.2f}, Strategy: {strategy_type}")
        
        return self._create_planned_pair_signal(
            direction=SignalDirection.SHORT,
            current_price=current_price,
            entry_price=entry_price,
            take_profit=take_profit,
            stop_loss=stop_loss,
            signal_strength=signal_strength,
            indicators=indicators,
            reasons=['planned_pair_short', 'stink_bid_ema34', f'price_below_ema={price_below_ema}', f'strategy={strategy_type}']
        )
    
    def _create_planned_pair_signal(self, direction: SignalDirection, current_price: float, 
                              entry_price: float, take_profit: float, stop_loss: float,
                              signal_strength: float, indicators: TechnicalIndicators, 
                              reasons: List[str]) -> Signal:
        """Create a planned pair signal with stink bid parameters"""
        
        self.logger.debug(f"📊 Planned pair signal strength: {signal_strength:.2f} | Reasons: {', '.join(reasons)}")
        
        signal = Signal(
            symbol=self.config.symbol,
            direction=direction,
            strength=signal_strength,
            current_price=current_price,
            entry_price=entry_price,  # Entry at EMA_34 (stink bid)
            stop_loss=stop_loss,
            take_profit=take_profit,
            source='planned_pair',
            timeframe='1m',
            confidence=min(signal_strength, 1.0),
            indicators=indicators.__dict__
        )
        
        self.logger.info(f"✅ Planned pair signal created: {signal.direction.value.upper()} @ ${signal.entry_price:.4f} (stink bid)")
        return signal

    def _analyze_auto_direction(self, indicators: TechnicalIndicators, current_price: float, candles: List[Candle]) -> Optional[Signal]:
        """Analyze both directions and choose the best signal"""
        self.logger.debug("📊 Analyzing AUTO direction - checking both LONG and SHORT...")
        
        long_signal = self._analyze_long_signal(indicators, current_price, candles)
        short_signal = self._analyze_short_signal(indicators, current_price, candles)
        
        if not long_signal and not short_signal:
            self.logger.debug("❌ No signals generated (both long and short failed)")
            return None
        elif long_signal and not short_signal:
            self.logger.info("✅ LONG signal selected")
            return long_signal
        elif short_signal and not long_signal:
            self.logger.info("✅ SHORT signal selected")
            return short_signal
        else:
            # Both signals - choose stronger one
            if long_signal and short_signal:
                chosen = long_signal if long_signal.confidence > short_signal.confidence else short_signal
                self.logger.info(f"⚡ Both signals present, chose {chosen.direction.value} (stronger confidence)")
                return chosen
        return None
    
    def _analyze_long_signal(self, indicators: TechnicalIndicators, current_price: float, candles: List[Candle]) -> Optional[Signal]:
        """Analyze long signal conditions with detailed logging"""
        signal_strength = 0.0
        reasons = []
        
        self.logger.debug(f"🔍 Analyzing LONG signal conditions...")
        self.logger.debug(f"📊 Current price: ${current_price:.4f}")
        
        # RSI oversold condition
        signal_strength, reasons = self._check_rsi_oversold(
            indicators, signal_strength, reasons
        )
        
        # EMA bullish crossover
        signal_strength, reasons = self._check_ema_bullish(
            indicators, signal_strength, reasons
        )
        
        # Bollinger bands position
        signal_strength, reasons = self._check_bollinger_lower(
            indicators, current_price, signal_strength, reasons
        )
        
        # MACD bullish divergence
        signal_strength, reasons = self._check_macd_bullish(
            indicators, signal_strength, reasons
        )
        
        # Volume confirmation - temporarily skip for now
        # signal_strength, reasons = self._check_volume_confirmation(
        #     indicators, candles, signal_strength, reasons
        # )
        self.logger.debug("📦 Volume confirmation check - temporarily disabled")
        
        # Check signal strength threshold
        threshold = self.indicators_config.long_signal_threshold
        self.logger.debug(f"📊 Total signal strength: {signal_strength:.2f} (min: {threshold})")
        self.logger.debug(f"✅ Signal reasons: {', '.join(reasons) if reasons else 'None'}")
        
        if signal_strength < threshold:
            self.logger.debug(f"❌ Signal too weak: {signal_strength:.2f} < {threshold}")
            return None
        
        # Create and return signal
        return self._create_long_signal(current_price, signal_strength, reasons, indicators)
    
    def _analyze_short_signal(self, indicators: TechnicalIndicators, current_price: float, candles: List[Candle]) -> Optional[Signal]:
        """Analyze short signal conditions"""
        signal_strength = 0.0
        reasons = []
        
        self.logger.debug(f"🔍 Analyzing SHORT signal conditions...")
        
        # RSI overbought condition
        rsi_overbought = self.indicators_config.rsi_overbought_threshold
        if indicators.rsi > rsi_overbought:
            signal_strength += 0.3
            reasons.append('RSI overbought')
            self.logger.debug(f"✅ RSI overbought: {indicators.rsi:.1f} > {rsi_overbought} (+0.3)")
        
        # EMA bearish crossover
        if indicators.ema_fast < indicators.ema_slow:
            signal_strength += 0.2
            reasons.append('EMA bearish crossover')
            self.logger.debug(f"✅ EMA bearish: {indicators.ema_fast:.4f} < {indicators.ema_slow:.4f} (+0.2)")
        
        # Price near Bollinger upper band
        bb_position = (current_price - indicators.bollinger_lower) / (indicators.bollinger_upper - indicators.bollinger_lower)
        bb_upper_threshold = self.indicators_config.bollinger_upper_threshold
        if bb_position > bb_upper_threshold:
            signal_strength += 0.2
            reasons.append('Near Bollinger upper band')
            self.logger.debug(f"✅ Near Bollinger upper: {bb_position:.2f} > {bb_upper_threshold} (+0.2)")
        
        # MACD bearish divergence
        if indicators.macd < indicators.macd_signal and indicators.macd_histogram < 0:
            signal_strength += 0.15
            reasons.append('MACD bearish')
            self.logger.debug(f"✅ MACD bearish (+0.15)")
        
        # Volume confirmation - use candles passed to analyze method
        # Would need candles passed to method, skip for short signals for now
        volume_threshold = self.indicators_config.volume_threshold_multiplier
        self.logger.debug(f"📦 Volume threshold: {volume_threshold}x (short signal - skipping volume check)")
        
        # Check signal strength threshold
        threshold = self.indicators_config.short_signal_threshold
        if signal_strength < threshold:
            self.logger.debug(f"❌ SHORT signal too weak: {signal_strength:.2f} < {threshold}")
            return None
        
        # Log important result
        self.logger.info(f"🎯 SHORT SIGNAL! Strength: {signal_strength:.2f} | Reasons: {', '.join(reasons)}")
        
        return self._create_short_signal(current_price, signal_strength, reasons, indicators)
    
    def _check_rsi_oversold(self, indicators: TechnicalIndicators, signal_strength: float, reasons: List[str]) -> tuple:
        """Check RSI oversold condition for long signals"""
        rsi_oversold = self.indicators_config.rsi_oversold_threshold
        self.logger.debug(f"📈 RSI: {indicators.rsi:.1f} (oversold <{rsi_oversold})")
        
        if indicators.rsi < rsi_oversold:
            signal_strength += 0.3
            reasons.append('RSI oversold')
            self.logger.debug(f"✅ RSI oversold: {indicators.rsi:.1f} < {rsi_oversold} (+0.3)")
        else:
            self.logger.debug(f"❌ RSI not oversold: {indicators.rsi:.1f} >= {rsi_oversold}")
        
        return signal_strength, reasons
    
    def _check_ema_bullish(self, indicators: TechnicalIndicators, signal_strength: float, reasons: List[str]) -> tuple:
        """Check EMA bullish crossover for long signals"""
        self.logger.debug(f"📊 EMA Fast: {indicators.ema_fast:.4f}, EMA Slow: {indicators.ema_slow:.4f}")
        
        if indicators.ema_fast > indicators.ema_slow:
            signal_strength += 0.2
            reasons.append('EMA bullish crossover')
            self.logger.debug(f"✅ EMA bullish: {indicators.ema_fast:.4f} > {indicators.ema_slow:.4f} (+0.2)")
        else:
            self.logger.debug(f"❌ EMA not bullish: {indicators.ema_fast:.4f} <= {indicators.ema_slow:.4f}")
        
        return signal_strength, reasons
    
    def _check_bollinger_lower(self, indicators: TechnicalIndicators, current_price: float, 
                              signal_strength: float, reasons: List[str]) -> tuple:
        """Check if price is near Bollinger lower band for long signals"""
        bb_position = (current_price - indicators.bollinger_lower) / (indicators.bollinger_upper - indicators.bollinger_lower)
        bb_threshold = self.indicators_config.bollinger_lower_threshold
        self.logger.debug(f"📊 Bollinger position: {bb_position:.2f} (near lower <{bb_threshold})")
        
        if bb_position < bb_threshold:
            signal_strength += 0.2
            reasons.append('Near Bollinger lower band')
            self.logger.debug(f"✅ Near Bollinger lower: {bb_position:.2f} < {bb_threshold} (+0.2)")
        else:
            self.logger.debug(f"❌ Not near Bollinger lower: {bb_position:.2f} >= {bb_threshold}")
        
        return signal_strength, reasons
    
    def _check_macd_bullish(self, indicators: TechnicalIndicators, signal_strength: float, reasons: List[str]) -> tuple:
        """Check MACD bullish conditions for long signals"""
        self.logger.debug(f"📊 MACD: {indicators.macd:.6f}, Signal: {indicators.macd_signal:.6f}, Histogram: {indicators.macd_histogram:.6f}")
        
        if indicators.macd > indicators.macd_signal and indicators.macd_histogram > 0:
            signal_strength += 0.15
            reasons.append('MACD bullish')
            self.logger.debug(f"✅ MACD bullish (+0.15)")
        else:
            self.logger.debug(f"❌ MACD not bullish")
        
        return signal_strength, reasons
    
    def _check_volume_confirmation(self, indicators: TechnicalIndicators, candles: List[Candle],
                                  signal_strength: float, reasons: List[str]) -> tuple:
        """Check volume confirmation with actual candle data"""
        if not candles:
            self.logger.debug("📦 No candle data for volume check")
            return signal_strength, reasons
        
        current_volume = candles[-1].volume
        volume_threshold = self.indicators_config.volume_threshold_multiplier
        volume_ratio = current_volume / indicators.volume_sma if indicators.volume_sma > 0 else 0
        
        self.logger.debug(f"📊 Volume: {current_volume:.0f}, SMA: {indicators.volume_sma:.0f}, Ratio: {volume_ratio:.2f}")
        
        if current_volume > indicators.volume_sma * volume_threshold:
            signal_strength += 0.1
            reasons.append('High volume')
            self.logger.debug(f"✅ High volume: {volume_ratio:.2f} > {volume_threshold} (+0.1)")
        else:
            self.logger.debug(f"❌ Normal volume: {volume_ratio:.2f} <= {volume_threshold}")
        
        return signal_strength, reasons
    
    def _create_long_signal(self, current_price: float, signal_strength: float, 
                           reasons: List[str], indicators: TechnicalIndicators) -> Signal:
        """Create a long signal with proper parameters using unified TP/SL calculator"""
        self.logger.info(f"🎯 LONG SIGNAL! Strength: {signal_strength:.2f} | Reasons: {', '.join(reasons)}")
        
        # Calculate entry, stop loss, and take profit using unified calculator
        entry_price = current_price
        
        # Use default strategy for regular DCA signals
        strategy_type = self.tp_sl_calculator.get_strategy_type_for_signal('dca_strategy')
        take_profit, stop_loss = self.tp_sl_calculator.calculate_tp_sl(
            entry_price=entry_price,
            direction=SignalDirection.LONG,
            strategy_type=strategy_type,
            indicators=indicators,
            current_price=current_price
        )
        
        # Validate TP/SL levels
        validation = self.tp_sl_calculator.validate_tp_sl_levels(
            entry_price, take_profit, stop_loss, SignalDirection.LONG
        )
        
        if validation['warnings']:
            for warning in validation['warnings']:
                self.logger.warning(f"⚠️ TP/SL warning: {warning}")
        
        # Calculate risk-reward ratio for logging
        risk_reward = self.tp_sl_calculator.calculate_risk_reward_ratio(
            entry_price, take_profit, stop_loss, SignalDirection.LONG
        )
        
        self.logger.debug(f"📊 Trade Setup: Entry=${entry_price:.4f}, SL=${stop_loss:.4f}, TP=${take_profit:.4f} (R:R={risk_reward:.2f})")
        
        signal = Signal(
            symbol=self.config.symbol,
            direction=SignalDirection.LONG,
            strength=signal_strength,
            current_price=current_price,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            source='dca_strategy',
            timeframe='1m',
            confidence=min(signal_strength, 1.0),
            indicators=indicators.__dict__
        )
        
        self.logger.info(f"✅ Signal created: {signal.direction.value.upper()} @ ${signal.entry_price:.4f} (confidence: {signal.confidence:.1%}, R:R={risk_reward:.2f})")
        return signal
    
    def _create_short_signal(self, current_price: float, signal_strength: float,
                            reasons: List[str], indicators: TechnicalIndicators) -> Signal:
        """Create a short signal with proper parameters using unified TP/SL calculator"""
        
        # Calculate entry, stop loss, and take profit using unified calculator
        entry_price = current_price
        
        # Use default strategy for regular DCA signals
        strategy_type = self.tp_sl_calculator.get_strategy_type_for_signal('dca_strategy')
        take_profit, stop_loss = self.tp_sl_calculator.calculate_tp_sl(
            entry_price=entry_price,
            direction=SignalDirection.SHORT,
            strategy_type=strategy_type,
            indicators=indicators,
            current_price=current_price
        )
        
        # Validate TP/SL levels
        validation = self.tp_sl_calculator.validate_tp_sl_levels(
            entry_price, take_profit, stop_loss, SignalDirection.SHORT
        )
        
        if validation['warnings']:
            for warning in validation['warnings']:
                self.logger.warning(f"⚠️ TP/SL warning: {warning}")
        
        # Calculate risk-reward ratio for logging
        risk_reward = self.tp_sl_calculator.calculate_risk_reward_ratio(
            entry_price, take_profit, stop_loss, SignalDirection.SHORT
        )
        
        self.logger.info(f"🎯 SHORT SIGNAL! Strength: {signal_strength:.2f} | Reasons: {', '.join(reasons)} (R:R={risk_reward:.2f})")
        
        return Signal(
            symbol=self.config.symbol,
            direction=SignalDirection.SHORT,
            strength=signal_strength,
            current_price=current_price,
            entry_price=entry_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            source='dca_strategy',
            timeframe='1m',
            confidence=min(signal_strength, 1.0),
            indicators=indicators.__dict__
        )
    
    def _get_current_volume(self, candles: List[Candle]) -> Optional[float]:
        """Get current volume from candles"""
        return candles[-1].volume if candles else None 