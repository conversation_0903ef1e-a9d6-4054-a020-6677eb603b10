"""Unified Take Profit and Stop Loss calculation logic"""
from typing import Tuple, Dict, Any, Optional
from src.core.models import SignalDirection, TradeConfig
from src.core.strategies.indicators.technical_indicators import TechnicalIndicators
import logging


class TPSLCalculator:
    """Unified Take Profit and Stop Loss calculator for consistent risk management"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        self.risk_config = config.risk
        self.planned_pair_config = config.planned_pair
        self.logger = logging.getLogger('TPSLCalculator')
    
    def calculate_tp_sl(self, 
                       entry_price: float, 
                       direction: SignalDirection,
                       strategy_type: str = 'default',
                       indicators: Optional[TechnicalIndicators] = None,
                       current_price: Optional[float] = None) -> Tuple[float, float]:
        """
        Unified TP/SL calculation method
        
        Args:
            entry_price: Entry price for the position
            direction: LONG or SHORT
            strategy_type: 'default', 'planned_pair', 'volatility_based'
            indicators: Technical indicators for advanced calculations
            current_price: Current market price for reference
            
        Returns:
            Tuple[take_profit, stop_loss]
        """
        
        try:
            if strategy_type == 'planned_pair':
                return self._calculate_planned_pair_tp_sl(entry_price, direction, indicators)
            elif strategy_type == 'volatility_based' and indicators:
                return self._calculate_volatility_based_tp_sl(entry_price, direction, indicators)
            else:
                return self._calculate_default_tp_sl(entry_price, direction)
                
        except Exception as e:
            self.logger.error(f"❌ TP/SL calculation error: {e}, falling back to default")
            return self._calculate_default_tp_sl(entry_price, direction)
    
    def _calculate_default_tp_sl(self, entry_price: float, direction: SignalDirection) -> Tuple[float, float]:
        """Default TP/SL calculation using risk config percentages"""
        
        stop_loss_percent = self.risk_config.default_stop_loss / 100  # 2% -> 0.02
        take_profit_percent = self.risk_config.default_take_profit / 100  # 3% -> 0.03
        
        if direction == SignalDirection.LONG:
            # LONG: TP above entry, SL below entry
            take_profit = entry_price * (1 + take_profit_percent)
            stop_loss = entry_price * (1 - stop_loss_percent)
        else:
            # SHORT: TP below entry, SL above entry
            take_profit = entry_price * (1 - take_profit_percent)
            stop_loss = entry_price * (1 + stop_loss_percent)
        
        self.logger.debug(f"📊 Default TP/SL: {direction.value} @ ${entry_price:.4f} -> TP=${take_profit:.4f}, SL=${stop_loss:.4f}")
        return round(take_profit, 6), round(stop_loss, 6)
    
    def _calculate_planned_pair_tp_sl(self, entry_price: float, direction: SignalDirection, 
                                    indicators: Optional[TechnicalIndicators] = None) -> Tuple[float, float]:
        """Planned pair TP/SL calculation using config multipliers"""
        
        if direction == SignalDirection.LONG:
            tp_multiplier = self.planned_pair_config.long['take_profit_multiplier']  # 1.005
            sl_multiplier = self.planned_pair_config.long['stop_loss_multiplier']   # 0.995
            
            take_profit = entry_price * tp_multiplier
            stop_loss = entry_price * sl_multiplier
        else:
            tp_multiplier = self.planned_pair_config.short['take_profit_multiplier']  # 0.995
            sl_multiplier = self.planned_pair_config.short['stop_loss_multiplier']   # 1.005
            
            take_profit = entry_price * tp_multiplier
            stop_loss = entry_price * sl_multiplier
        
        self.logger.debug(f"📊 Planned Pair TP/SL: {direction.value} @ ${entry_price:.4f} -> TP=${take_profit:.4f}, SL=${stop_loss:.4f}")
        return round(take_profit, 6), round(stop_loss, 6)
    
    def _calculate_volatility_based_tp_sl(self, entry_price: float, direction: SignalDirection,
                                        indicators: TechnicalIndicators) -> Tuple[float, float]:
        """Volatility-based TP/SL using Bollinger Bands width"""
        
        try:
            # Calculate volatility factor using Bollinger Bands width
            bb_width = indicators.bollinger_upper - indicators.bollinger_lower
            if bb_width <= 0:
                self.logger.warning("⚠️ Invalid BB width, falling back to planned pair calculation")
                return self._calculate_planned_pair_tp_sl(entry_price, direction, indicators)
            
            # Normalize volatility
            volatility_factor = bb_width / entry_price
            
            # Base distances with volatility adjustment from config
            base_tp_percent = self.risk_config.volatility_base_tp_percent / 100  # Convert percentage to decimal
            base_sl_percent = self.risk_config.volatility_base_sl_percent / 100  # Convert percentage to decimal
            
            # Adjust based on volatility (higher volatility = larger distances)
            tp_percent = base_tp_percent + (volatility_factor * self.risk_config.volatility_tp_multiplier)
            sl_percent = base_sl_percent + (volatility_factor * self.risk_config.volatility_sl_multiplier)
            
            if direction == SignalDirection.LONG:
                take_profit = entry_price * (1 + tp_percent)
                stop_loss = entry_price * (1 - sl_percent)
            else:
                take_profit = entry_price * (1 - tp_percent)
                stop_loss = entry_price * (1 + sl_percent)
            
            # Apply planned pair bounds as safety limits
            planned_tp, planned_sl = self._calculate_planned_pair_tp_sl(entry_price, direction, indicators)
            
            if direction == SignalDirection.LONG:
                # For LONG: TP should be at least planned pair minimum, SL should not exceed planned pair maximum
                take_profit = max(take_profit, planned_tp)
                stop_loss = min(stop_loss, planned_sl)
            else:
                # For SHORT: TP should be at most planned pair minimum, SL should not be below planned pair maximum
                take_profit = min(take_profit, planned_tp)
                stop_loss = max(stop_loss, planned_sl)
            
            self.logger.debug(f"📊 Volatility TP/SL: {direction.value} @ ${entry_price:.4f} -> TP=${take_profit:.4f}, SL=${stop_loss:.4f} (volatility: {volatility_factor:.4f}, base_tp: {base_tp_percent:.3f}, base_sl: {base_sl_percent:.3f})")
            return round(take_profit, 6), round(stop_loss, 6)
            
        except Exception as e:
            self.logger.error(f"❌ Volatility TP/SL calculation error: {e}, falling back to planned pair")
            return self._calculate_planned_pair_tp_sl(entry_price, direction, indicators)
    
    def get_strategy_type_for_signal(self, signal_source: str) -> str:
        """Determine TP/SL strategy type based on signal source"""
        
        if signal_source == 'planned_pair':
            # Use appropriate strategy based on risk config
            if self.risk_config.tp_sl_strategy == 'volatility_based':
                return 'volatility_based'
            else:
                return 'planned_pair'
        else:
            # Regular DCA strategy signals
            return 'default'
    
    def calculate_risk_reward_ratio(self, entry_price: float, take_profit: float, 
                                  stop_loss: float, direction: SignalDirection) -> float:
        """Calculate risk-reward ratio for validation"""
        
        try:
            if direction == SignalDirection.LONG:
                potential_profit = take_profit - entry_price
                potential_loss = entry_price - stop_loss
            else:
                potential_profit = entry_price - take_profit
                potential_loss = stop_loss - entry_price
            
            if potential_loss <= 0:
                return 0.0
            
            risk_reward_ratio = potential_profit / potential_loss
            return round(risk_reward_ratio, 2)
            
        except Exception as e:
            self.logger.error(f"❌ Risk-reward calculation error: {e}")
            return 0.0
    
    def validate_tp_sl_levels(self, entry_price: float, take_profit: float, stop_loss: float, 
                            direction: SignalDirection) -> Dict[str, Any]:
        """Validate TP/SL levels for correctness"""
        
        validation_result = {
            'valid': True,
            'issues': [],
            'warnings': []
        }
        
        try:
            # Check direction consistency
            if direction == SignalDirection.LONG:
                if take_profit <= entry_price:
                    validation_result['valid'] = False
                    validation_result['issues'].append(f"LONG TP must be above entry: TP=${take_profit:.4f} <= Entry=${entry_price:.4f}")
                
                if stop_loss >= entry_price:
                    validation_result['valid'] = False
                    validation_result['issues'].append(f"LONG SL must be below entry: SL=${stop_loss:.4f} >= Entry=${entry_price:.4f}")
            else:
                if take_profit >= entry_price:
                    validation_result['valid'] = False
                    validation_result['issues'].append(f"SHORT TP must be below entry: TP=${take_profit:.4f} >= Entry=${entry_price:.4f}")
                
                if stop_loss <= entry_price:
                    validation_result['valid'] = False
                    validation_result['issues'].append(f"SHORT SL must be above entry: SL=${stop_loss:.4f} <= Entry=${entry_price:.4f}")
            
            # Check risk-reward ratio
            risk_reward = self.calculate_risk_reward_ratio(entry_price, take_profit, stop_loss, direction)
            if risk_reward < 1.0:
                validation_result['warnings'].append(f"Poor risk-reward ratio: {risk_reward:.2f} < 1.0")
            elif risk_reward < 1.5:
                validation_result['warnings'].append(f"Low risk-reward ratio: {risk_reward:.2f} < 1.5 (recommended)")
            
            # Check if levels are too close or too far
            if direction == SignalDirection.LONG:
                tp_distance_percent = (take_profit - entry_price) / entry_price * 100
                sl_distance_percent = (entry_price - stop_loss) / entry_price * 100
            else:
                tp_distance_percent = (entry_price - take_profit) / entry_price * 100
                sl_distance_percent = (stop_loss - entry_price) / entry_price * 100
            
            if tp_distance_percent < 0.1:  # Less than 0.1%
                validation_result['warnings'].append(f"TP very close to entry: {tp_distance_percent:.2f}%")
            if sl_distance_percent < 0.1:  # Less than 0.1%
                validation_result['warnings'].append(f"SL very close to entry: {sl_distance_percent:.2f}%")
            
            if tp_distance_percent > 10:  # More than 10%
                validation_result['warnings'].append(f"TP very far from entry: {tp_distance_percent:.2f}%")
            if sl_distance_percent > 5:  # More than 5%
                validation_result['warnings'].append(f"SL very far from entry: {sl_distance_percent:.2f}%")
            
        except Exception as e:
            validation_result['valid'] = False
            validation_result['issues'].append(f"Validation error: {e}")
        
        return validation_result 