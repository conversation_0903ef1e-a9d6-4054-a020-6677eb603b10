"""DCA (Dollar Cost Averaging) level calculations"""
from typing import List, Dict, Any
from src.core.models import SignalDirection, TradeConfig


class DCACalculator:
    """Calculate DCA levels and investment strategies"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        self.dca_config = config.dca
    
    def calculate_dca_levels(self, entry_price: float, direction: SignalDirection) -> List[Dict[str, float]]:
        """Calculate DCA levels for a given entry price and direction"""
        levels = []
        
        for i in range(self.dca_config.max_orders):
            level_data = self._calculate_single_level(entry_price, direction, i)
            levels.append(level_data)
        
        return levels
    
    def _calculate_single_level(self, entry_price: float, direction: SignalDirection, level_index: int) -> Dict[str, float]:
        """Calculate a single DCA level"""
        if direction == SignalDirection.LONG:
            # DCA down for long positions (buy more as price drops)
            level_price = entry_price * (1 - (self.dca_config.price_deviation * (level_index + 1)) / 100)
        else:
            # DCA up for short positions (sell more as price rises)
            level_price = entry_price * (1 + (self.dca_config.price_deviation * (level_index + 1)) / 100)
        
        # Calculate amount multiplier (compound scaling)
        amount_multiplier = self.dca_config.volume_scale ** level_index
        
        # Calculate percentage deviation from entry
        percentage_from_entry = abs(level_price - entry_price) / entry_price * 100
        
        return {
            'level': level_index + 1,
            'price': level_price,
            'amount_multiplier': amount_multiplier,
            'percentage_from_entry': percentage_from_entry
        }
    
    def calculate_total_investment(self, entry_price: float, direction: SignalDirection) -> Dict[str, float]:
        """Calculate total potential investment across all DCA levels"""
        levels = self.calculate_dca_levels(entry_price, direction)
        
        base_investment = entry_price  # Assuming base unit investment
        total_investment = base_investment
        total_multiplier = 1.0
        
        for level in levels:
            level_investment = base_investment * level['amount_multiplier']
            total_investment += level_investment
            total_multiplier += level['amount_multiplier']
        
        return {
            'base_investment': base_investment,
            'total_investment': total_investment,
            'total_multiplier': total_multiplier,
            'max_levels': len(levels),
            'average_price': self._calculate_average_entry_price(entry_price, levels)
        }
    
    def _calculate_average_entry_price(self, initial_entry: float, levels: List[Dict[str, float]]) -> float:
        """Calculate average entry price if all DCA levels are filled"""
        total_cost = initial_entry * 1.0  # Initial position with multiplier 1.0
        total_quantity = 1.0
        
        for level in levels:
            level_cost = level['price'] * level['amount_multiplier']
            total_cost += level_cost
            total_quantity += level['amount_multiplier']
        
        return total_cost / total_quantity if total_quantity > 0 else initial_entry
    
    def calculate_risk_metrics(self, entry_price: float, direction: SignalDirection) -> Dict[str, float]:
        """Calculate risk metrics for DCA strategy"""
        levels = self.calculate_dca_levels(entry_price, direction)
        investment_data = self.calculate_total_investment(entry_price, direction)
        
        # Calculate maximum drawdown scenario
        if direction == SignalDirection.LONG:
            # For long positions, worst case is price drops to lowest DCA level
            worst_price = levels[-1]['price'] if levels else entry_price
            max_drawdown_percent = (entry_price - worst_price) / entry_price * 100
        else:
            # For short positions, worst case is price rises to highest DCA level
            worst_price = levels[-1]['price'] if levels else entry_price
            max_drawdown_percent = (worst_price - entry_price) / entry_price * 100
        
        return {
            'max_drawdown_percent': max_drawdown_percent,
            'worst_case_price': worst_price,
            'total_risk_exposure': investment_data['total_investment'],
            'risk_per_level': self.dca_config.price_deviation,
            'levels_count': len(levels)
        }
    
    def validate_dca_configuration(self) -> Dict[str, Any]:
        """Validate DCA configuration parameters"""
        issues = []
        recommendations = []
        
        # Check price deviation
        if self.dca_config.price_deviation <= 0:
            issues.append("Price deviation must be positive")
        elif self.dca_config.price_deviation < 0.5:
            recommendations.append("Consider price deviation >= 0.5% for meaningful levels")
        elif self.dca_config.price_deviation > 5.0:
            recommendations.append("Price deviation > 5% may create very wide levels")
        
        # Check volume scale
        if self.dca_config.volume_scale <= 1.0:
            recommendations.append("Volume scale <= 1.0 means no position scaling")
        elif self.dca_config.volume_scale > 3.0:
            recommendations.append("Volume scale > 3.0 creates aggressive position scaling")
        
        # Check max orders
        if self.dca_config.max_orders <= 0:
            issues.append("Max orders must be positive")
        elif self.dca_config.max_orders > 10:
            recommendations.append("More than 10 DCA levels may be excessive")
        
        return {
            'valid': len(issues) == 0,
            'issues': issues,
            'recommendations': recommendations,
            'config_summary': {
                'max_orders': self.dca_config.max_orders,
                'price_deviation': f"{self.dca_config.price_deviation}%",
                'volume_scale': f"{self.dca_config.volume_scale}x"
            }
        } 