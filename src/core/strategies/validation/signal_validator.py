"""Signal validation and quality checks"""
import logging
from typing import List, Dict, Any
from src.core.models import Signal, Candle, TradeConfig


class SignalValidator:
    """Validates trading signals before execution"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        self.indicators_config = config.indicators
        self.logger = logging.getLogger('DCAStrategy')
    
    def validate_signal(self, signal: Signal, candles: List[Candle]) -> Dict[str, Any]:
        """Comprehensive signal validation"""
        validation_result = {
            'valid': True,
            'issues': [],
            'warnings': [],
            'score': 0.0,
            'checks_passed': 0,
            'total_checks': 0
        }
        
        # Run all validation checks
        self._check_price_deviation(signal, candles, validation_result)
        self._check_risk_reward_ratio(signal, validation_result)
        self._check_confidence_level(signal, validation_result)
        self._check_market_volatility(signal, candles, validation_result)
        self._check_signal_timing(signal, validation_result)
        
        # Calculate final score
        if validation_result['total_checks'] > 0:
            validation_result['score'] = validation_result['checks_passed'] / validation_result['total_checks']
        
        # Log validation results
        self._log_validation_results(signal, validation_result)
        
        return validation_result
    
    def _check_price_deviation(self, signal: Signal, candles: List[Candle], result: Dict[str, Any]) -> None:
        """Check if signal entry price deviates too much from current market price"""
        result['total_checks'] += 1
        
        if not candles:
            result['issues'].append("No candle data available for price validation")
            return
        
        current_price = candles[-1].close
        price_deviation = abs(signal.entry_price - current_price) / current_price
        max_deviation = 0.01  # 1% maximum deviation
        
        if price_deviation <= max_deviation:
            result['checks_passed'] += 1
            self.logger.debug(f"✅ Price deviation check passed: {price_deviation:.3%} <= {max_deviation:.1%}")
        else:
            result['issues'].append(f"Price deviation too high: {price_deviation:.3%} > {max_deviation:.1%}")
            self.logger.warning(f"❌ Price deviation too high: {price_deviation:.3%}")
    
    def _check_risk_reward_ratio(self, signal: Signal, result: Dict[str, Any]) -> None:
        """Check if risk-reward ratio meets minimum requirements"""
        result['total_checks'] += 1
        
        try:
            risk_reward = signal.risk_reward_ratio
            min_ratio = 1.5  # Minimum 1.5:1 ratio
            
            if risk_reward >= min_ratio:
                result['checks_passed'] += 1
                self.logger.debug(f"✅ Risk-reward ratio check passed: {risk_reward:.2f} >= {min_ratio}")
            else:
                result['issues'].append(f"Poor risk-reward ratio: {risk_reward:.2f} < {min_ratio}")
                self.logger.warning(f"❌ Poor risk-reward ratio: {risk_reward:.2f}")
                
        except Exception as e:
            result['warnings'].append(f"Could not calculate risk-reward ratio: {e}")
            self.logger.warning(f"⚠️ Risk-reward calculation error: {e}")
    
    def _check_confidence_level(self, signal: Signal, result: Dict[str, Any]) -> None:
        """Check if signal confidence meets minimum threshold"""
        result['total_checks'] += 1
        
        threshold = getattr(self.indicators_config, 'signal_validation_threshold', 0.6)
        
        if signal.confidence >= threshold:
            result['checks_passed'] += 1
            self.logger.debug(f"✅ Confidence level check passed: {signal.confidence:.1%} >= {threshold:.1%}")
        else:
            result['issues'].append(f"Low confidence: {signal.confidence:.1%} < {threshold:.1%}")
            self.logger.warning(f"❌ Low signal confidence: {signal.confidence:.1%}")
    
    def _check_market_volatility(self, signal: Signal, candles: List[Candle], result: Dict[str, Any]) -> None:
        """Check if market volatility is within acceptable range"""
        result['total_checks'] += 1
        
        if len(candles) < 20:
            result['warnings'].append("Insufficient data for volatility analysis")
            return
        
        try:
            # Calculate recent price volatility (last 20 candles)
            recent_prices = [c.close for c in candles[-20:]]
            price_changes = [abs(recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1] 
                           for i in range(1, len(recent_prices))]
            avg_volatility = sum(price_changes) / len(price_changes)
            
            # Check if volatility is reasonable (not too high/low)
            max_volatility = 0.05  # 5% average change
            min_volatility = 0.001  # 0.1% minimum change
            
            if min_volatility <= avg_volatility <= max_volatility:
                result['checks_passed'] += 1
                self.logger.debug(f"✅ Volatility check passed: {avg_volatility:.3%}")
            elif avg_volatility > max_volatility:
                result['warnings'].append(f"High market volatility: {avg_volatility:.3%}")
                self.logger.warning(f"⚠️ High volatility: {avg_volatility:.3%}")
            else:
                result['warnings'].append(f"Low market volatility: {avg_volatility:.3%}")
                self.logger.warning(f"⚠️ Low volatility: {avg_volatility:.3%}")
                
        except Exception as e:
            result['warnings'].append(f"Volatility calculation error: {e}")
            self.logger.warning(f"⚠️ Volatility analysis error: {e}")
    
    def _check_signal_timing(self, signal: Signal, result: Dict[str, Any]) -> None:
        """Check if signal timing is appropriate"""
        result['total_checks'] += 1
        
        # For now, just pass timing check as signals don't have timestamps yet
        result['checks_passed'] += 1
        self.logger.debug("✅ Signal timing check passed")
    
    def _log_validation_results(self, signal: Signal, result: Dict[str, Any]) -> None:
        """Log comprehensive validation results"""
        if result['valid'] and not result['issues']:
            self.logger.info(f"✅ Signal validation PASSED: {signal.direction.value} @ ${signal.entry_price:.4f}")
            self.logger.debug(f"📊 Validation score: {result['score']:.1%} ({result['checks_passed']}/{result['total_checks']} checks passed)")
        else:
            self.logger.warning(f"❌ Signal validation FAILED: {signal.direction.value} @ ${signal.entry_price:.4f}")
            
        # Log issues
        for issue in result['issues']:
            self.logger.warning(f"🚨 Issue: {issue}")
        
        # Log warnings
        for warning in result['warnings']:
            self.logger.warning(f"⚠️ Warning: {warning}")
    
    def quick_validate(self, signal: Signal, candles: List[Candle]) -> bool:
        """Quick validation - returns boolean for simple pass/fail"""
        validation_result = self.validate_signal(signal, candles)
        return validation_result['valid'] and len(validation_result['issues']) == 0 