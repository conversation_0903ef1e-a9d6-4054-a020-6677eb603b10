"""Refactored DCA Strategy using modular components"""
import logging
from typing import Optional, List, Dict, Any
from datetime import datetime, timedelta

from src.core.models import Signal, SignalDirection, Candle, TradeConfig

# Import specialized modules
from src.core.strategies.indicators import TechnicalIndicators, TechnicalIndicatorCalculator
from src.core.strategies.analysis import MarketAnalyzer
from src.core.strategies.calculators import DCACalculator
from src.core.strategies.validation import SignalValidator
from src.core.strategies.data import CandleManager
from src.core.strategies.logging import StrategyLogger


class DCAStrategy:
    """Professional DCA Strategy with modular components"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        self.indicators_config = config.indicators
        
        # Signal generation state
        self.last_signal_time: Optional[datetime] = None
        self.signal_cooldown = timedelta(minutes=config.signal_cooldown_minutes)
        
        # Initialize specialized components
        self._initialize_components()
        
        # Setup dedicated logger
        self._setup_strategy_logger()
    
    def _initialize_components(self) -> None:
        """Initialize all specialized components"""
        self.candle_manager = CandleManager(self.config, max_candles=200)
        self.market_analyzer = MarketAnalyzer(self.config)
        self.dca_calculator = DCACalculator(self.config)
        self.signal_validator = SignalValidator(self.config)
    
    def _setup_strategy_logger(self) -> None:
        """Setup dedicated logger for DCA Strategy"""
        from src.infrastructure.logging.logger import TradeLogger
        
        self.trade_logger = TradeLogger(
            name='DCAStrategy',
            log_dir='logs',
            log_level='DEBUG',
            console_enabled=False
        )
        self.logger = self.trade_logger.get_logger()
        
        # Initialize strategy logger after main logger is ready
        self.strategy_logger: StrategyLogger = StrategyLogger(self.logger, self.config)
    
    async def generate_signal(self, exchange) -> Optional[Signal]:
        """Main entry point for signal generation"""
        try:
            self.strategy_logger.log_signal_generation_start()
            
            # Update market data
            if not await self._update_market_data(exchange):
                return None
            
            # Check prerequisites
            if not self._check_signal_prerequisites():
                return None
            
            # Calculate technical indicators
            indicators = self._calculate_technical_indicators()
            if not indicators:
                return None
            
            # Log detailed indicators (debug only)
            latest_candle = self.candle_manager.get_latest_candle()
            if latest_candle:
                self.strategy_logger.log_detailed_indicators(indicators, latest_candle.close, latest_candle.volume)
            
            # Analyze market and generate signal
            signal = self._generate_market_signal(indicators)
            
            # Process and validate signal
            result = self._process_generated_signal(signal, indicators)
            
            # Log final result
            self.strategy_logger.log_signal_generation_result(result)
            return result
            
        except Exception as e:
            self.logger.error(f"❌ Signal generation error: {e}", exc_info=True)
            return None
    
    async def generate_planned_pair_signal(self, exchange) -> Optional[Signal]:
        """
        Generate signal using planned pair logic - no threshold checking
        This is for stink bid strategy where we always want to place orders
        """
        try:
            self.logger.info("🎯 Starting planned pair signal generation...")
            
            # Update market data
            if not await self._update_market_data(exchange):
                return None
            
            # Check basic prerequisites (but skip threshold checks)
            if not self._check_planned_pair_prerequisites():
                return None
            
            # Calculate technical indicators
            indicators = self._calculate_technical_indicators()
            if not indicators:
                return None
            
            # Log detailed indicators for planned pair
            latest_candle = self.candle_manager.get_latest_candle()
            if latest_candle:
                self.logger.debug(f"🎯 Planned pair - Current price: ${latest_candle.close:.4f}, Volume: {latest_candle.volume:.0f}")
            
            # Generate signal using planned pair style (no threshold)
            signal = self._generate_planned_pair_market_signal(indicators)
            
            # Basic validation for planned pair signal
            if signal:
                self.last_signal_time = datetime.now()
                self.logger.info(f"✅ Planned pair signal generated: {signal.direction.value.upper()} @ ${signal.entry_price:.4f} (stink bid)")
                
                # Log DCA levels for this signal
                try:
                    dca_levels = self.dca_calculator.calculate_dca_levels(signal.entry_price, signal.direction)
                    self.strategy_logger.log_signal_details(signal, indicators, dca_levels)
                except Exception as e:
                    self.logger.debug(f"⚠️ DCA levels calculation error: {e}")
            else:
                self.logger.info("❌ No planned pair signal generated")
            
            return signal
            
        except Exception as e:
            self.logger.error(f"❌ Planned pair signal generation error: {e}", exc_info=True)
            return None
    
    def _check_planned_pair_prerequisites(self) -> bool:
        """Check basic prerequisites for planned pair (skip cooldown for stink bid)"""
        # Only check data sufficiency, skip cooldown for stink bid strategy
        sufficient_data = self.candle_manager.has_sufficient_data()
        
        if not sufficient_data:
            candle_count = self.candle_manager.get_candles_count()
            self.logger.warning(f"⚠️ Insufficient candles for planned pair: {candle_count} < 50 required")
            return False
        
        self.logger.debug("✅ Planned pair prerequisites check passed (no cooldown)")
        return True
    
    def _generate_planned_pair_market_signal(self, indicators: TechnicalIndicators) -> Optional[Signal]:
        """Generate signal using planned pair style (no threshold)"""
        try:
            candles = self.candle_manager.get_candles()
            signal = self.market_analyzer.analyze_planned_pair(indicators, candles)
            return signal
        except Exception as e:
            self.logger.error(f"❌ Planned pair market signal error: {e}")
            return None
    
    def calculate_planned_pair_levels(self, entry_price: float, direction: str, indicators: TechnicalIndicators) -> Dict[str, Dict]:
        """
        Calculate DCA levels using planned pair logic
        Returns dict with EMA_89 and BB_LOWER levels
        """
        dca_levels = {}
        
        # EMA_89 DCA level (using ema_slow)
        dca_levels['EMA_89'] = {
            'price': indicators.ema_slow,
            'enabled': self.config.dca.strategies.get('EMA_89', {}).get('enabled', False),
            'amount': self.config.dca.strategies.get('EMA_89', {}).get('amount', 50),
            'timeframe': self.config.dca.strategies.get('EMA_89', {}).get('timeframe', '1h')
        }
        
        # BB_LOWER DCA level
        dca_levels['BB_LOWER'] = {
            'price': indicators.bollinger_lower,
            'enabled': self.config.dca.strategies.get('BB_LOWER', {}).get('enabled', False),
            'amount': self.config.dca.strategies.get('BB_LOWER', {}).get('amount', 50),
            'timeframe': self.config.dca.strategies.get('BB_LOWER', {}).get('timeframe', '15m')
        }
        
        self.logger.debug(f"🎯 Planned pair levels calculated:")
        for dca_type, dca_info in dca_levels.items():
            enabled_str = "✅" if dca_info['enabled'] else "❌"
            self.logger.debug(f"   {dca_type}: ${dca_info['price']:.6f} (${dca_info['amount']}) {enabled_str}")
        
        return dca_levels
    
    def check_planned_pair_entry_conditions(self, current_price: float, indicators: TechnicalIndicators, direction: str) -> bool:
        """
        Check entry conditions using planned pair logic
        Returns True if conditions are favorable, False otherwise
        """
        ema_34 = indicators.ema_fast
        
        if direction.upper() == 'LONG':
            # LONG: current_price > ema_34
            condition = current_price > ema_34
            self.logger.debug(f"🎯 Planned pair LONG entry: ${current_price:.4f} > EMA_34=${ema_34:.4f} = {condition}")
        elif direction.upper() == 'SHORT':
            # SHORT: current_price < ema_34
            condition = current_price < ema_34
            self.logger.debug(f"🎯 Planned pair SHORT entry: ${current_price:.4f} < EMA_34=${ema_34:.4f} = {condition}")
        else:
            self.logger.warning(f"❌ Unknown direction: {direction}")
            return False
        
        return condition

    async def _update_market_data(self, exchange) -> bool:
        """Update market data using candle manager"""
        success = await self.candle_manager.update_candles(exchange)
        count = self.candle_manager.get_candles_count()
        self.strategy_logger.log_market_data_update(count, success)
        return success
    
    def _check_signal_prerequisites(self) -> bool:
        """Check if we can generate signals"""
        # Check data sufficiency
        sufficient_data = self.candle_manager.has_sufficient_data()
        on_cooldown = self._is_signal_on_cooldown()
        candle_count = self.candle_manager.get_candles_count() if not sufficient_data else None
        
        self.strategy_logger.log_prerequisites_check(sufficient_data, on_cooldown, candle_count)
        
        return sufficient_data and not on_cooldown
    
    def _calculate_technical_indicators(self) -> Optional[TechnicalIndicators]:
        """Calculate technical indicators using specialized calculator"""
        try:
            candles = self.candle_manager.get_candles()
            indicators = TechnicalIndicatorCalculator.calculate_all_indicators(
                candles, self.indicators_config
            )
            
            self.strategy_logger.log_indicators_calculation(indicators)
            return indicators
            
        except Exception as e:
            self.strategy_logger.log_error("Indicator calculation", e)
            return None
    
    def _generate_market_signal(self, indicators: TechnicalIndicators) -> Optional[Signal]:
        """Generate signal using market analyzer"""
        self.strategy_logger.log_market_analysis_start()
        
        candles = self.candle_manager.get_candles()
        signal = self.market_analyzer.analyze_market_conditions(indicators, candles)
        
        return signal
    
    def _process_generated_signal(self, signal: Optional[Signal], indicators: TechnicalIndicators) -> Optional[Signal]:
        """Process and validate generated signal"""
        if not signal:
            return None
        
        try:
            # Validate signal
            candles = self.candle_manager.get_candles()
            validation_passed = self.signal_validator.quick_validate(signal, candles)
            
            if not validation_passed:
                self.strategy_logger.log_signal_validation_result(signal, False)
                return None
            
            # Update signal generation time
            self.last_signal_time = datetime.now()
            
            # Log detailed signal info (debug only)
            dca_levels = self.dca_calculator.calculate_dca_levels(signal.entry_price, signal.direction)
            self.strategy_logger.log_signal_details(signal, indicators, dca_levels)
            
            self.strategy_logger.log_signal_validation_result(signal, True)
            return signal
            
        except Exception as e:
            self.strategy_logger.log_error("Signal processing", e)
            return None
    
    def _is_signal_on_cooldown(self) -> bool:
        """Check if signal generation is on cooldown"""
        if not self.last_signal_time:
            return False
        return datetime.now() - self.last_signal_time < self.signal_cooldown
    

    
    # Public API methods for backward compatibility
    def get_dca_levels(self, entry_price: float, direction: SignalDirection) -> List[Dict[str, float]]:
        """Calculate DCA levels - backward compatibility method"""
        return self.dca_calculator.calculate_dca_levels(entry_price, direction)
    
    def validate_signal(self, signal: Signal) -> bool:
        """Validate signal - backward compatibility method"""
        candles = self.candle_manager.get_candles()
        return self.signal_validator.quick_validate(signal, candles)
    
    # Additional utility methods
    def get_market_stats(self) -> Dict[str, Any]:
        """Get current market statistics"""
        return {
            'candles_count': self.candle_manager.get_candles_count(),
            'price_range': self.candle_manager.get_price_range(),
            'volume_stats': self.candle_manager.get_volume_stats(),
            'has_sufficient_data': self.candle_manager.has_sufficient_data()
        }
    
    def get_dca_risk_metrics(self, entry_price: float, direction: SignalDirection) -> Dict[str, float]:
        """Get DCA risk metrics"""
        return self.dca_calculator.calculate_risk_metrics(entry_price, direction)
    
    def export_candle_data(self, periods: Optional[int] = None) -> List[Dict]:
        """Export candle data for analysis"""
        return self.candle_manager.export_candles_data(periods) 