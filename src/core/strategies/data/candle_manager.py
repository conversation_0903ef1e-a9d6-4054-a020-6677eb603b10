"""Candle data management and market data operations"""
import logging
from typing import List, Optional, Dict
from datetime import datetime
from src.core.models import Candle, TradeConfig


class CandleManager:
    """Manages candle data fetching, storage, and maintenance"""
    
    def __init__(self, config: TradeConfig, max_candles: int = 400):
        self.config = config
        self.max_candles = max_candles
        self.candles: List[Candle] = []
        self.timeframe = config.indicators.primary_timeframe  # Use configurable timeframe
        self.logger = logging.getLogger('DCAStrategy')
    
    async def update_candles(self, exchange) -> bool:
        """Update candle data from exchange"""
        try:
            self.logger.debug(f"🌐 Fetching OHLCV data for {self.config.symbol} ({self.timeframe}, limit=100)...")
            
            # Fetch latest candles from exchange  
            # Use higher limit for initial data accuracy, especially for EMA calculations
            limit = 300 if not self.candles else 100  # More data initially, then smaller updates
            new_candles = await exchange.fetch_ohlcv(
                self.config.symbol,
                timeframe=self.timeframe,
                limit=limit
            )
            
            if not new_candles:
                self.logger.warning("❌ No candle data received from exchange")
                return False
            
            self.logger.debug(f"📊 Fetched {len(new_candles)} candles from exchange")
            
            # Merge with existing candles
            self._merge_new_candles(new_candles)
            
            # Maintain candle limit
            self._trim_old_candles()
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Candle update error: {e}", exc_info=True)
            return False
    
    def _merge_new_candles(self, new_candles: List[Candle]) -> None:
        """Merge new candles with existing ones, avoiding duplicates"""
        if not self.candles:
            # First time initialization
            self.candles = new_candles
            self.logger.debug(f"📈 Initialized candles: {len(self.candles)} candles")
            return
        
        # Find last timestamp to avoid duplicates
        last_timestamp = self.candles[-1].timestamp
        new_count = 0
        
        for candle in new_candles:
            if candle.timestamp > last_timestamp:
                self.candles.append(candle)
                new_count += 1
        
        self.logger.debug(f"🔄 Added {new_count} new candles, total: {len(self.candles)}")
    
    def _trim_old_candles(self) -> None:
        """Keep only the most recent candles up to max_candles limit"""
        if len(self.candles) > self.max_candles:
            old_count = len(self.candles)
            self.candles = self.candles[-self.max_candles:]
            self.logger.debug(f"🗂️ Trimmed candles from {old_count} to {len(self.candles)} (max: {self.max_candles})")
    
    def get_candles(self) -> List[Candle]:
        """Get current candle data"""
        return self.candles.copy()  # Return copy to prevent external modification
    
    def get_latest_candle(self) -> Optional[Candle]:
        """Get the most recent candle"""
        return self.candles[-1] if self.candles else None
    
    def get_candles_count(self) -> int:
        """Get number of available candles"""
        return len(self.candles)
    
    def has_sufficient_data(self, min_candles: int = 50) -> bool:
        """Check if we have sufficient candle data for analysis"""
        return len(self.candles) >= min_candles
    
    def get_price_range(self, periods: int = 20) -> Dict[str, float]:
        """Get price range information for recent periods"""
        if not self.candles or len(self.candles) < periods:
            return {'high': 0.0, 'low': 0.0, 'range': 0.0}
        
        recent_candles = self.candles[-periods:]
        highs = [c.high for c in recent_candles]
        lows = [c.low for c in recent_candles]
        
        price_high = max(highs)
        price_low = min(lows)
        price_range = price_high - price_low
        
        return {
            'high': price_high,
            'low': price_low,
            'range': price_range,
            'range_percent': (price_range / price_low * 100) if price_low > 0 else 0.0
        }
    
    def get_volume_stats(self, periods: int = 20) -> Dict[str, float]:
        """Get volume statistics for recent periods"""
        if not self.candles or len(self.candles) < periods:
            return {'avg': 0.0, 'max': 0.0, 'min': 0.0}
        
        recent_candles = self.candles[-periods:]
        volumes = [c.volume for c in recent_candles]
        
        return {
            'avg': sum(volumes) / len(volumes),
            'max': max(volumes),
            'min': min(volumes),
            'current': recent_candles[-1].volume
        }
    
    def export_candles_data(self, periods: Optional[int] = None) -> List[Dict]:
        """Export candle data for analysis or debugging"""
        candles_to_export = self.candles
        if periods:
            candles_to_export = self.candles[-periods:]
        
        return [
            {
                'timestamp': c.timestamp,
                'datetime': c.timestamp.isoformat() if hasattr(c.timestamp, 'isoformat') else str(c.timestamp),
                'open': c.open,
                'high': c.high,
                'low': c.low,
                'close': c.close,
                'volume': c.volume
            }
            for c in candles_to_export
        ]
    
    def reset_candles(self) -> None:
        """Reset candle data (useful for testing or reinitialization)"""
        old_count = len(self.candles)
        self.candles.clear()
        self.logger.debug(f"🔄 Reset candle data: {old_count} candles cleared")
    
    def set_timeframe(self, timeframe: str) -> None:
        """Change the timeframe and reset candle data"""
        if timeframe != self.timeframe:
            self.logger.info(f"🕐 Changing timeframe from {self.timeframe} to {timeframe}")
            self.timeframe = timeframe
            self.reset_candles()  # Clear old data when timeframe changes
    
    def get_timeframe(self) -> str:
        """Get current timeframe"""
        return self.timeframe 