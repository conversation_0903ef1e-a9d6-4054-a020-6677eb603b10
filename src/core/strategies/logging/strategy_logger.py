"""Strategy logging utilities and formatters"""
import logging
from typing import Optional, List, Dict, Any
from src.core.models import Signal, TradeConfig
from src.core.strategies.indicators import TechnicalIndicators


class StrategyLogger:
    """Handles all logging operations for DCA Strategy"""
    
    def __init__(self, logger: logging.Logger, config: TradeConfig):
        self.logger = logger
        self.config = config
        self.indicators_config = config.indicators
    
    def log_detailed_indicators(self, indicators: TechnicalIndicators, current_price: float, current_volume: float) -> None:
        """Log detailed technical analysis (debug only)"""
        try:
            # Calculate additional metrics
            bb_position = (current_price - indicators.bollinger_lower) / (
                indicators.bollinger_upper - indicators.bollinger_lower
            ) if indicators.bollinger_upper != indicators.bollinger_lower else 0.5
            
            volume_ratio = current_volume / indicators.volume_sma if indicators.volume_sma > 0 else 0
            
            self.logger.debug("📊 === DETAILED TECHNICAL ANALYSIS ===")
            self.logger.debug(f"💰 Current Price: ${current_price:.4f}")
            self.logger.debug(
                f"📊 RSI: {indicators.rsi:.2f} "
                f"(Oversold: <{self.indicators_config.rsi_oversold_threshold}, "
                f"Overbought: >{self.indicators_config.rsi_overbought_threshold})"
            )
            self.logger.debug(
                f"📈 EMA Fast: ${indicators.ema_fast:.4f} | EMA Slow: ${indicators.ema_slow:.4f} | "
                f"EMA Trend: ${indicators.ema_trend:.4f} | "
                f"Trend: {'Bullish' if indicators.ema_fast > indicators.ema_slow else 'Bearish'}"
            )
            self.logger.debug(
                f"🎯 Bollinger Bands: Upper=${indicators.bollinger_upper:.4f}, "
                f"Middle=${indicators.bollinger_middle:.4f}, Lower=${indicators.bollinger_lower:.4f}"
            )
            self.logger.debug(f"📍 BB Position: {bb_position:.2f} (0=Lower, 1=Upper)")
            self.logger.debug(
                f"📊 MACD: {indicators.macd:.6f} | Signal: {indicators.macd_signal:.6f} | "
                f"Histogram: {indicators.macd_histogram:.6f}"
            )
            self.logger.debug(
                f"📦 Volume: {current_volume:.0f} | SMA: {indicators.volume_sma:.0f} | "
                f"Ratio: {volume_ratio:.2f}x"
            )
            self.logger.debug("📊 === END TECHNICAL ANALYSIS ===")
            
        except Exception as e:
            self.logger.error(f"❌ Error logging detailed indicators: {e}")
    
    def log_signal_details(self, signal: Signal, indicators: TechnicalIndicators, dca_levels: List[Dict[str, float]]) -> None:
        """Log comprehensive signal details (debug only)"""
        try:
            self.logger.debug("🎯 === SIGNAL GENERATION DETAILS ===")
            self.logger.debug(
                f"🎯 Signal: {signal.direction.value} | Strength: {signal.strength:.2f} | "
                f"Confidence: {signal.confidence:.1%}"
            )
            self.logger.debug(
                f"💰 Entry: ${signal.entry_price:.4f} | Stop Loss: ${signal.stop_loss:.4f} | "
                f"Take Profit: ${signal.take_profit:.4f}"
            )
            
            if dca_levels:
                self.logger.debug("📊 DCA LEVELS:")
                total_investment = signal.entry_price  # Base amount
                for level in dca_levels:
                    amount = signal.entry_price * level['amount_multiplier']
                    total_investment += amount
                    self.logger.debug(
                        f"  Level {level['level']}: ${level['price']:.4f} "
                        f"({level['percentage_from_entry']:.1f}% from entry) - Amount: ${amount:.2f}"
                    )
                self.logger.debug(f"💰 Total Max Investment: ${total_investment:.2f}")
            
            self.logger.debug("🎯 === END SIGNAL DETAILS ===")
            
        except Exception as e:
            self.logger.error(f"❌ Error logging signal details: {e}")
    
    def log_market_data_update(self, candles_count: int, update_success: bool) -> None:
        """Log market data update status"""
        if update_success:
            self.logger.debug(f"📈 Updated market data: {candles_count} candles available")
        else:
            self.logger.warning("❌ Failed to update market data")
    
    def log_signal_generation_start(self) -> None:
        """Log start of signal generation process"""
        self.logger.debug("🔄 Starting signal generation...")
    
    def log_signal_generation_result(self, signal: Optional[Signal]) -> None:
        """Log final result of signal generation"""
        if signal:
            self.logger.info(
                f"📶 Signal generated: {signal.direction.value.upper()} @ "
                f"${signal.entry_price:.4f} (confidence: {signal.confidence:.1%})"
            )
        else:
            self.logger.debug("❌ No signal generated from market analysis")
    
    def log_signal_validation_result(self, signal: Signal, validation_passed: bool) -> None:
        """Log signal validation results"""
        if validation_passed:
            self.logger.info(f"✅ Signal validated and ready: {signal.direction.value.upper()}")
        else:
            self.logger.warning("❌ Signal validation failed")
    
    def log_prerequisites_check(self, sufficient_data: bool, on_cooldown: bool, candle_count: Optional[int] = None) -> None:
        """Log prerequisites check results"""
        if not sufficient_data and candle_count is not None:
            self.logger.warning(f"⚠️ Insufficient candles: {candle_count} < 50 required")
        elif on_cooldown:
            self.logger.debug("⏰ Signal generation on cooldown")
        else:
            self.logger.debug("✅ Prerequisites check passed")
    
    def log_indicators_calculation(self, indicators: TechnicalIndicators) -> None:
        """Log basic indicator calculation results"""
        self.logger.debug("🧮 Calculating technical indicators...")
        self.logger.debug(
            f"📊 Indicators calculated: RSI={indicators.rsi:.1f}, "
            f"EMA Fast={indicators.ema_fast:.5f}, EMA Slow={indicators.ema_slow:.5f}, "
            f"EMA Trend={indicators.ema_trend:.5f}"
        )
    
    def log_component_initialization(self, components: Dict[str, str]) -> None:
        """Log component initialization status"""
        self.logger.debug("🔧 Initializing strategy components...")
        for component_name, component_type in components.items():
            self.logger.debug(f"  ✅ {component_name}: {component_type}")
    
    def log_market_analysis_start(self) -> None:
        """Log start of market analysis"""
        self.logger.debug("🎯 Analyzing market conditions...")
    
    def log_error(self, operation: str, error: Exception) -> None:
        """Log errors with context"""
        self.logger.error(f"❌ {operation} error: {error}", exc_info=True)
    
    def log_debug_message(self, message: str) -> None:
        """Log general debug messages"""
        self.logger.debug(message)
    
    def log_info_message(self, message: str) -> None:
        """Log general info messages"""
        self.logger.info(message)
    
    def log_warning_message(self, message: str) -> None:
        """Log general warning messages"""
        self.logger.warning(message) 