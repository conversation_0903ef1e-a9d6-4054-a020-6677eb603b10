"""Technical indicators calculation and data structures"""
import pandas as pd
import numpy as np
from dataclasses import dataclass
from typing import List
from src.core.models import Candle


@dataclass
class TechnicalIndicators:
    """Technical indicators data"""
    rsi: float
    ema_fast: float  # EMA 34
    ema_slow: float  # EMA 89
    ema_trend: float  # EMA 120 - for trend direction
    bollinger_upper: float
    bollinger_middle: float
    bollinger_lower: float
    volume_sma: float
    macd: float
    macd_signal: float
    macd_histogram: float


class TechnicalIndicatorCalculator:
    """Calculate technical indicators from candle data"""
    
    @staticmethod
    async def calculate_all_indicators_for_timeframe(
        exchange_connector, 
        symbol: str, 
        timeframe: str, 
        indicators_config,
        limit: int = 300  # Increased for better EMA accuracy
    ) -> TechnicalIndicators:
        """Calculate all technical indicators for a specific timeframe"""
        try:
            # Use ExchangeConnector's fetch_ohlcv which returns List[Candle]
            candles = await exchange_connector.fetch_ohlcv(symbol, timeframe, limit=limit)
            
            if not candles:
                raise ValueError(f"No candle data received for {symbol} on {timeframe}")
            
            # Calculate indicators using the existing method
            return TechnicalIndicatorCalculator.calculate_all_indicators(candles, indicators_config)
            
        except Exception as e:
            # Return default indicators if calculation fails
            return TechnicalIndicators(
                rsi=50.00000,
                ema_fast=0.00000,
                ema_slow=0.00000,
                ema_trend=0.00000,
                bollinger_upper=0.00000,
                bollinger_middle=0.00000,
                bollinger_lower=0.00000,
                volume_sma=0.00000,
                macd=0.00000,
                macd_signal=0.00000,
                macd_histogram=0.00000
            )
    
    @staticmethod
    def calculate_all_indicators(candles: List[Candle], indicators_config) -> TechnicalIndicators:
        """Calculate all technical indicators from candle data"""
        # Sort candles by timestamp to ensure chronological order (oldest first)
        # This is critical for accurate technical indicator calculations
        sorted_candles = sorted(candles, key=lambda c: c.timestamp)
        
        # Convert candles to pandas DataFrame for easier calculation
        df = pd.DataFrame([
            {
                'timestamp': c.timestamp,
                'open': c.open,
                'high': c.high,
                'low': c.low,
                'close': c.close,
                'volume': c.volume
            }
            for c in sorted_candles
        ])
        
        # Calculate each indicator
        rsi = TechnicalIndicatorCalculator._calculate_rsi(
            pd.Series(df['close'].astype(float)), 
            indicators_config.rsi_period
        )
        
        close_series = pd.Series(df['close'].astype(float))
        volume_series = pd.Series(df['volume'].astype(float))
        
        ema_fast, ema_slow, ema_trend = TechnicalIndicatorCalculator._calculate_emas(
            close_series, 
            indicators_config.ema_periods
        )
        
        bb_upper, bb_middle, bb_lower = TechnicalIndicatorCalculator._calculate_bollinger_bands(
            close_series, 
            indicators_config.bollinger_bands
        )
        
        volume_sma = TechnicalIndicatorCalculator._calculate_volume_sma(volume_series)
        
        macd, macd_signal, macd_histogram = TechnicalIndicatorCalculator._calculate_macd(
            close_series, 
            indicators_config.macd
        )
        
        return TechnicalIndicators(
            rsi=rsi,
            ema_fast=ema_fast,
            ema_slow=ema_slow,
            ema_trend=ema_trend,
            bollinger_upper=bb_upper,
            bollinger_middle=bb_middle,
            bollinger_lower=bb_lower,
            volume_sma=volume_sma,
            macd=macd,
            macd_signal=macd_signal,
            macd_histogram=macd_histogram
        )
    
    @staticmethod
    def _calculate_rsi(prices: pd.Series, period: int = 14) -> float:
        """Calculate RSI indicator with improved handling"""
        if len(prices) < period + 1:
            # Not enough data for RSI calculation
            return 50.00000  # Return neutral RSI
            
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        
        # Convert to numpy arrays to avoid type issues
        gain_values = np.array(gain)
        loss_values = np.array(loss)
        
        # Get last values
        gain_val = gain_values[-1]
        loss_val = loss_values[-1]
        
        if np.isnan(gain_val) or np.isnan(loss_val):
            return 50.00000  # Return neutral RSI if calculation fails
        
        if gain_val == 0 and loss_val == 0:
            return 50.00000  # No price movement, neutral RSI
        
        if loss_val == 0:
            return 100.00000  # Only gains, maximum RSI
        
        # Calculate RS and RSI
        rs = gain_val / loss_val
        rsi_value = 100 - (100 / (1 + rs))
        
        return round(float(rsi_value), 5)
    
    @staticmethod
    def _calculate_emas(close_prices: pd.Series, ema_periods: List[int]) -> tuple:
        """Calculate fast and slow EMAs - consistent with bot_dca.py"""
        ema_fast = close_prices.ewm(span=ema_periods[0], adjust=False).mean().iloc[-1]
        ema_slow = close_prices.ewm(span=ema_periods[1], adjust=False).mean().iloc[-1]
        ema_trend = close_prices.ewm(span=ema_periods[2], adjust=False).mean().iloc[-1]
        return round(float(ema_fast), 5), round(float(ema_slow), 5), round(float(ema_trend), 5)
    
    @staticmethod
    def _calculate_bollinger_bands(close_prices: pd.Series, bb_config: dict) -> tuple:
        """Calculate Bollinger Bands - optimized version"""
        bb_period = bb_config.get('period', 20)
        bb_std = bb_config.get('std', 2)
        
        if len(close_prices) < bb_period:
            # Not enough data, return current price as all bands
            current_price = round(float(close_prices.iloc[-1]), 5)
            return current_price, current_price, current_price
        
        # Calculate rolling statistics and convert to numpy arrays
        rolling_mean_values = np.array(close_prices.rolling(bb_period).mean())
        rolling_std_values = np.array(close_prices.rolling(bb_period).std())
        
        # Get the last values
        bb_middle = float(rolling_mean_values[-1])
        bb_std_val = float(rolling_std_values[-1])
        
        # Calculate upper and lower bands
        bb_upper = bb_middle + (bb_std_val * bb_std)
        bb_lower = bb_middle - (bb_std_val * bb_std)
        
        return round(bb_upper, 5), round(bb_middle, 5), round(bb_lower, 5)
    
    @staticmethod
    def _calculate_volume_sma(volume_data: pd.Series, period: int = 20) -> float:
        """Calculate volume simple moving average - optimized version"""
        if len(volume_data) < period:
            # Not enough data, return current volume
            return round(float(volume_data.iloc[-1]), 5)
        
        # Calculate SMA and convert to numpy array
        volume_sma_values = np.array(volume_data.rolling(period).mean())
        volume_sma = volume_sma_values[-1]
        
        return round(float(volume_sma), 5)
    
    @staticmethod
    def _calculate_macd(close_prices: pd.Series, macd_config: dict) -> tuple:
        """Calculate MACD indicator"""
        macd_fast = macd_config.get('fast', 12)
        macd_slow = macd_config.get('slow', 26)
        macd_signal_period = macd_config.get('signal', 9)
        
        if len(close_prices) < macd_slow:
            # Not enough data for MACD calculation
            return 0.00000, 0.00000, 0.00000
        
        ema_fast = close_prices.ewm(span=macd_fast, adjust=False).mean()
        ema_slow = close_prices.ewm(span=macd_slow, adjust=False).mean()
        macd_line = ema_fast - ema_slow
        macd_signal_line = macd_line.ewm(span=macd_signal_period, adjust=False).mean()
        macd_histogram = macd_line - macd_signal_line
        
        return (
            round(float(macd_line.iloc[-1]), 5),
            round(float(macd_signal_line.iloc[-1]), 5),
            round(float(macd_histogram.iloc[-1]), 5)
        ) 