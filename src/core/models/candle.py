"""Candle model for OHLCV data"""
from dataclasses import dataclass
from datetime import datetime


@dataclass
class Candle:
    """OHLCV candle data model"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    
    # Calculated fields
    change: float = 0.0
    change_percent: float = 0.0
    
    def __post_init__(self):
        """Calculate derived fields"""
        self.change = self.close - self.open
        if self.open > 0:
            self.change_percent = (self.change / self.open) * 100
    
    @property
    def is_bullish(self) -> bool:
        """Check if candle is bullish"""
        return self.close > self.open
    
    @property
    def is_bearish(self) -> bool:
        """Check if candle is bearish"""
        return self.close < self.open
    
    @property
    def body_size(self) -> float:
        """Calculate candle body size"""
        return abs(self.close - self.open)
    
    @property
    def upper_wick(self) -> float:
        """Calculate upper wick size"""
        body_high = max(self.open, self.close)
        return self.high - body_high
    
    @property
    def lower_wick(self) -> float:
        """Calculate lower wick size"""
        body_low = min(self.open, self.close)
        return body_low - self.low
    
    @property
    def total_range(self) -> float:
        """Calculate total price range"""
        return self.high - self.low
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume,
            'change': self.change,
            'change_percent': self.change_percent,
            'is_bullish': self.is_bullish,
            'body_size': self.body_size,
            'upper_wick': self.upper_wick,
            'lower_wick': self.lower_wick,
            'total_range': self.total_range
        } 