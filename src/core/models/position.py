"""Position model for tracking trading positions"""
from dataclasses import dataclass, field
from datetime import datetime
from typing import Dict, Optional, List
from enum import Enum


class PositionState(Enum):
    """Position states"""
    PENDING = 'pending'
    OPEN = 'open'
    CLOSED = 'closed'
    CANCELLED = 'cancelled'


@dataclass
class DCAOrder:
    """DCA order information"""
    order_id: Optional[str] = None
    is_ordered: bool = False
    price: Optional[float] = None
    amount: Optional[float] = None
    filled_at: Optional[datetime] = None


@dataclass
class Position:
    """Trading position model"""
    symbol: str
    side: str  # 'long' or 'short'
    entry_price: float
    current_price: float
    contracts: float
    pnl: float = 0.0
    pnl_percentage: float = 0.0
    state: PositionState = PositionState.PENDING
    
    # Unique identifier
    id: Optional[str] = None
    
    # Risk management
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    
    # DCA management
    dca_orders: Dict[str, DCAOrder] = field(default_factory=dict)
    
    # Order IDs
    entry_order_id: Optional[str] = None
    sl_order_id: Optional[str] = None
    tp_order_id: Optional[str] = None
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.now)
    opened_at: Optional[datetime] = None
    closed_at: Optional[datetime] = None
    
    # Statistics
    max_pnl: float = 0.0
    min_pnl: float = 0.0
    fees_paid: float = 0.0
    
    def update_pnl(self, current_price: float) -> None:
        """Update P&L based on current price"""
        self.current_price = current_price
        
        if self.side == 'long':
            self.pnl = (current_price - self.entry_price) * self.contracts
            self.pnl_percentage = ((current_price - self.entry_price) / self.entry_price) * 100
        else:
            self.pnl = (self.entry_price - current_price) * self.contracts
            self.pnl_percentage = ((self.entry_price - current_price) / self.entry_price) * 100
        
        # Track max/min PnL
        self.max_pnl = max(self.max_pnl, self.pnl)
        self.min_pnl = min(self.min_pnl, self.pnl)
    
    def to_dict(self) -> Dict:
        """Convert to dictionary for serialization"""
        return {
            'symbol': self.symbol,
            'side': self.side,
            'entry_price': self.entry_price,
            'current_price': self.current_price,
            'contracts': self.contracts,
            'pnl': self.pnl,
            'pnl_percentage': self.pnl_percentage,
            'state': self.state.value,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'created_at': self.created_at.isoformat(),
            'opened_at': self.opened_at.isoformat() if self.opened_at else None,
            'closed_at': self.closed_at.isoformat() if self.closed_at else None,
            'max_pnl': self.max_pnl,
            'min_pnl': self.min_pnl,
            'fees_paid': self.fees_paid,
            'dca_orders': {k: {'order_id': v.order_id, 'is_ordered': v.is_ordered, 
                              'price': v.price, 'amount': v.amount} 
                          for k, v in self.dca_orders.items()}
        } 