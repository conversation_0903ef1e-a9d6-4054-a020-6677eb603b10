"""Signal model for trading signals"""
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional


class SignalDirection(Enum):
    """Signal directions"""
    LONG = 'long'
    SHORT = 'short'
    NEUTRAL = 'neutral'
    CLOSE = 'close'


@dataclass
class Signal:
    """Trading signal model"""
    symbol: str
    direction: SignalDirection
    strength: float  # 0.0 to 1.0
    current_price: float
    
    # Entry/Exit points
    entry_price: float
    stop_loss: float
    take_profit: float
    
    # Signal source
    source: str  # e.g., 'ema_crossover', 'bb_squeeze', etc.
    timeframe: str
    
    # Indicators used
    indicators: Dict[str, Any] = field(default_factory=dict)
    
    # Metadata
    created_at: datetime = field(default_factory=datetime.now)
    expires_at: Optional[datetime] = None
    confidence: float = 0.5  # 0.0 to 1.0
    
    # Risk metrics
    risk_reward_ratio: float = 0.0
    position_size_percent: float = 1.0
    
    def __post_init__(self):
        """Calculate derived fields"""
        if self.direction == SignalDirection.LONG:
            risk = self.entry_price - self.stop_loss
            reward = self.take_profit - self.entry_price
        elif self.direction == SignalDirection.SHORT:
            risk = self.stop_loss - self.entry_price
            reward = self.entry_price - self.take_profit
        else:
            risk = reward = 0
        
        if risk > 0:
            self.risk_reward_ratio = reward / risk
    
    def is_valid(self) -> bool:
        """Check if signal is still valid"""
        if self.expires_at and datetime.now() > self.expires_at:
            return False
        
        # Check if price is still near entry
        price_deviation = abs(self.current_price - self.entry_price) / self.entry_price
        return price_deviation < 0.02  # 2% deviation allowed
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            'symbol': self.symbol,
            'direction': self.direction.value,
            'strength': self.strength,
            'current_price': self.current_price,
            'entry_price': self.entry_price,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'source': self.source,
            'timeframe': self.timeframe,
            'indicators': self.indicators,
            'created_at': self.created_at.isoformat(),
            'expires_at': self.expires_at.isoformat() if self.expires_at else None,
            'confidence': self.confidence,
            'risk_reward_ratio': self.risk_reward_ratio,
            'position_size_percent': self.position_size_percent,
            'is_valid': self.is_valid()
        } 