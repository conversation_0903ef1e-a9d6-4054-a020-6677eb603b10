"""Trade configuration models"""
from dataclasses import dataclass, field
from typing import Dict, List, Any, Optional


@dataclass
class IndicatorConfig:
    """Indicator configuration"""
    ema_periods: List[int] = field(default_factory=lambda: [34, 89, 120])
    timeframes: List[str] = field(default_factory=lambda: ['15m', '1h', '4h'])
    primary_timeframe: str = '15m'  # Primary timeframe for trading signals
    bollinger_bands: Dict[str, Any] = field(default_factory=lambda: {
        'period': 20,
        'std': 2
    })
    rsi_period: int = 14
    macd: Dict[str, int] = field(default_factory=lambda: {
        'fast': 12,
        'slow': 26,
        'signal': 9
    })
    
    # Signal strength thresholds
    long_signal_threshold: float = 0.6  # Minimum strength for LONG signals
    short_signal_threshold: float = 0.6  # Minimum strength for SHORT signals
    signal_validation_threshold: float = 0.6  # Minimum confidence for signal validation
    
    # Technical indicator thresholds
    rsi_oversold_threshold: float = 30.0  # RSI oversold level
    rsi_overbought_threshold: float = 70.0  # RSI overbought level
    bollinger_lower_threshold: float = 0.2  # Position threshold for lower band
    bollinger_upper_threshold: float = 0.8  # Position threshold for upper band
    volume_threshold_multiplier: float = 1.2  # Volume above average threshold


@dataclass
class DCAConfig:
    """DCA configuration"""
    enabled: bool = True
    max_orders: int = 3
    price_deviation: float = 1.5  # percentage
    volume_scale: float = 1.5
    
    # Specific DCA strategies
    strategies: Dict[str, Dict[str, Any]] = field(default_factory=lambda: {
        'BB_LOWER': {
            'timeframe': '15m',
            'amount': 50,
            'enabled': True
        },
        'EMA_89': {
            'timeframe': '1h',
            'amount': 50,
            'enabled': False
        }
    })


@dataclass
class PlannedPairConfig:
    """Planned pair strategy configuration"""
    long: Dict[str, float] = field(default_factory=lambda: {
        'take_profit_multiplier': 1.005,  # Upper band * 1.005 (+ 0.5%)
        'stop_loss_multiplier': 0.995     # Trend reference * 0.995 (- 0.5%)
    })
    short: Dict[str, float] = field(default_factory=lambda: {
        'take_profit_multiplier': 0.995,  # Lower band * 0.995 (- 0.5%)
        'stop_loss_multiplier': 1.005     # Trend reference * 1.005 (+ 0.5%)
    })


@dataclass
class RiskConfig:
    """Risk management configuration"""
    # Capital management (capital is fetched from exchange)
    min_position_size: float = 10.0  # Minimum position size in USD
    max_position_size: float = 500.0  # Maximum position size in USD
    max_position_percentage: float = 50.0  # Max percentage of capital per position
    
    # Risk limits
    leverage: float = 1.0
    daily_loss_limit: float = 10.0  # percentage
    max_concurrent_positions: int = 10
    
    # Stop loss / Take profit
    default_stop_loss: float = 2.0  # percentage
    default_take_profit: float = 3.0  # percentage
    use_trailing_stop: bool = False
    trailing_stop_distance: float = 1.0  # percentage
    
    # TP/SL strategy options
    tp_sl_strategy: str = 'volatility_based'  # 'fixed', 'volatility_based', 'atr_based', 'support_resistance'
    tp_sl_immediately: bool = True  # Create TP/SL orders immediately after position open
    adjust_tp_sl_on_dca: bool = True  # Adjust TP/SL after DCA orders fill
    partial_take_profit: bool = False  # Take partial profits at multiple levels
    
    # Volatility-based TP/SL configuration
    volatility_base_tp_percent: float = 1.0  # Base TP percentage (1%)
    volatility_base_sl_percent: float = 0.5  # Base SL percentage (0.5%)
    volatility_tp_multiplier: float = 0.4   # Volatility multiplier for TP (40% of volatility factor)
    volatility_sl_multiplier: float = 0.25  # Volatility multiplier for SL (25% of volatility factor)
    
    # Position sizing
    risk_per_trade: float = 40.0  # percentage of capital
    use_kelly_criterion: bool = False
    
    # Cooldown periods
    loss_cooldown_minutes: int = 30
    max_daily_trades: int = 10


@dataclass
class TradeConfig:
    """Main trading configuration"""
    # Basic settings
    symbol: str
    exchange: str = 'bybit'
    direction: str = 'LONG'  # LONG or SHORT
    amount: float = 50.0  # Base amount in USD
    
    # Sub-configurations
    indicators: IndicatorConfig = field(default_factory=IndicatorConfig)
    dca: DCAConfig = field(default_factory=DCAConfig)
    risk: RiskConfig = field(default_factory=RiskConfig)
    planned_pair: PlannedPairConfig = field(default_factory=PlannedPairConfig)
    
    # Execution settings
    use_test_mode: bool = True
    use_sandbox: bool = True
    order_type: str = 'limit'
    
    # Timing configuration
    signal_cooldown_minutes: float = 5.0  # Cooldown between signal generations (supports decimal)
    trading_loop_interval_seconds: int = 10  # Main trading loop interval
    
    # Logging and monitoring
    log_level: str = 'INFO'
    save_trades_to_csv: bool = True
    enable_notifications: bool = False
    
    @classmethod
    def from_dict(cls, config_dict: dict) -> 'TradeConfig':
        """Create TradeConfig from dictionary"""
        # Extract nested configs
        indicators = config_dict.get('indicators', {})
        dca = config_dict.get('dca', {})
        risk = config_dict.get('risk', {})
        planned_pair = config_dict.get('planned_pair', {})
        
        # Create sub-configs
        indicator_config = IndicatorConfig(**indicators) if indicators else IndicatorConfig()
        dca_config = DCAConfig(**dca) if dca else DCAConfig()
        risk_config = RiskConfig(**risk) if risk else RiskConfig()
        planned_pair_config = PlannedPairConfig(**planned_pair) if planned_pair else PlannedPairConfig()
        
        # Remove nested configs and deprecated fields from main dict
        main_config = {k: v for k, v in config_dict.items()
                      if k not in ['indicators', 'dca', 'risk', 'planned_pair', 'dca_amount']}
        
        return cls(
            **main_config,
            indicators=indicator_config,
            dca=dca_config,
            risk=risk_config,
            planned_pair=planned_pair_config
        )
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            'symbol': self.symbol,
            'exchange': self.exchange,
            'direction': self.direction,
            'amount': self.amount,
            'indicators': {
                'ema_periods': self.indicators.ema_periods,
                'timeframes': self.indicators.timeframes,
                'primary_timeframe': self.indicators.primary_timeframe,
                'bollinger_bands': self.indicators.bollinger_bands,
                'rsi_period': self.indicators.rsi_period,
                'macd': self.indicators.macd,
                'long_signal_threshold': self.indicators.long_signal_threshold,
                'short_signal_threshold': self.indicators.short_signal_threshold,
                'signal_validation_threshold': self.indicators.signal_validation_threshold,
                'rsi_oversold_threshold': self.indicators.rsi_oversold_threshold,
                'rsi_overbought_threshold': self.indicators.rsi_overbought_threshold,
                'bollinger_lower_threshold': self.indicators.bollinger_lower_threshold,
                'bollinger_upper_threshold': self.indicators.bollinger_upper_threshold,
                'volume_threshold_multiplier': self.indicators.volume_threshold_multiplier
            },
            'dca': {
                'enabled': self.dca.enabled,
                'max_orders': self.dca.max_orders,
                'price_deviation': self.dca.price_deviation,
                'volume_scale': self.dca.volume_scale,
                'strategies': self.dca.strategies
            },
            'planned_pair': {
                'long': self.planned_pair.long,
                'short': self.planned_pair.short
            },
            'risk': {
                'min_position_size': self.risk.min_position_size,
                'max_position_size': self.risk.max_position_size,
                'max_position_percentage': self.risk.max_position_percentage,
                'leverage': self.risk.leverage,
                'daily_loss_limit': self.risk.daily_loss_limit,
                'max_concurrent_positions': self.risk.max_concurrent_positions,
                'default_stop_loss': self.risk.default_stop_loss,
                'default_take_profit': self.risk.default_take_profit,
                'use_trailing_stop': self.risk.use_trailing_stop,
                'trailing_stop_distance': self.risk.trailing_stop_distance,
                'tp_sl_strategy': self.risk.tp_sl_strategy,
                'tp_sl_immediately': self.risk.tp_sl_immediately,
                'adjust_tp_sl_on_dca': self.risk.adjust_tp_sl_on_dca,
                'partial_take_profit': self.risk.partial_take_profit,
                'volatility_base_tp_percent': self.risk.volatility_base_tp_percent,
                'volatility_base_sl_percent': self.risk.volatility_base_sl_percent,
                'volatility_tp_multiplier': self.risk.volatility_tp_multiplier,
                'volatility_sl_multiplier': self.risk.volatility_sl_multiplier,
                'risk_per_trade': self.risk.risk_per_trade,
                'use_kelly_criterion': self.risk.use_kelly_criterion,
                'loss_cooldown_minutes': self.risk.loss_cooldown_minutes,
                'max_daily_trades': self.risk.max_daily_trades
            },
            'use_test_mode': self.use_test_mode,
            'use_sandbox': self.use_sandbox,
            'order_type': self.order_type,
            'signal_cooldown_minutes': self.signal_cooldown_minutes,
            'trading_loop_interval_seconds': self.trading_loop_interval_seconds,
            'log_level': self.log_level,
            'save_trades_to_csv': self.save_trades_to_csv,
            'enable_notifications': self.enable_notifications
        } 