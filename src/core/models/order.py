"""Order model for trading orders"""
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Optional


class OrderType(Enum):
    """Order types"""
    MARKET = 'market'
    LIMIT = 'limit'
    STOP_LOSS = 'stop_loss'
    TAKE_PROFIT = 'take_profit'
    STOP_LIMIT = 'stop_limit'


class OrderSide(Enum):
    """Order sides"""
    BUY = 'buy'
    SELL = 'sell'


class OrderStatus(Enum):
    """Order status"""
    PENDING = 'pending'
    OPEN = 'open'
    PARTIALLY_FILLED = 'partially_filled'
    FILLED = 'filled'
    CANCELLED = 'cancelled'
    REJECTED = 'rejected'
    EXPIRED = 'expired'


@dataclass
class Order:
    """Trading order model"""
    symbol: str
    side: OrderSide
    type: OrderType
    price: float
    amount: float
    status: OrderStatus = OrderStatus.PENDING
    
    # Order identifiers
    order_id: Optional[str] = None
    client_order_id: Optional[str] = None
    
    # Execution details
    filled_amount: float = 0.0
    average_price: float = 0.0
    fee: float = 0.0
    
    # Risk parameters
    stop_price: Optional[float] = None
    trigger_price: Optional[float] = None
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: Optional[datetime] = None
    filled_at: Optional[datetime] = None
    
    # Additional info
    reduce_only: bool = False
    post_only: bool = False
    time_in_force: str = 'GTC'  # Good Till Cancel
    
    def is_complete(self) -> bool:
        """Check if order is complete"""
        return self.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, 
                               OrderStatus.REJECTED, OrderStatus.EXPIRED]
    
    def is_active(self) -> bool:
        """Check if order is active"""
        return self.status in [OrderStatus.PENDING, OrderStatus.OPEN, 
                              OrderStatus.PARTIALLY_FILLED]
    
    def fill_percentage(self) -> float:
        """Calculate fill percentage"""
        if self.amount == 0:
            return 0.0
        return (self.filled_amount / self.amount) * 100
    
    def to_dict(self) -> dict:
        """Convert to dictionary"""
        return {
            'symbol': self.symbol,
            'side': self.side.value,
            'type': self.type.value,
            'price': self.price,
            'amount': self.amount,
            'status': self.status.value,
            'order_id': self.order_id,
            'client_order_id': self.client_order_id,
            'filled_amount': self.filled_amount,
            'average_price': self.average_price,
            'fee': self.fee,
            'stop_price': self.stop_price,
            'trigger_price': self.trigger_price,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat() if self.updated_at else None,
            'filled_at': self.filled_at.isoformat() if self.filled_at else None,
            'reduce_only': self.reduce_only,
            'post_only': self.post_only,
            'time_in_force': self.time_in_force,
            'fill_percentage': self.fill_percentage()
        } 