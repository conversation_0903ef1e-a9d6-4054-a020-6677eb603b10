"""Capital management for risk system"""
import logging
from datetime import datetime
from typing import Optional, Tuple


class CapitalManager:
    """Manages trading capital and balance updates"""
    
    def __init__(self):
        self.current_capital = 0.0
        self.exchange_connector = None
        self.last_balance_update: Optional[datetime] = None
        self.balance_update_interval = 300  # 5 minutes in seconds
        self.logger = logging.getLogger('CapitalManager')
    
    def set_exchange_connector(self, exchange_connector) -> None:
        """Set exchange connector for balance updates"""
        self.exchange_connector = exchange_connector
    
    async def update_current_capital(self, force_update: bool = False) -> None:
        """Update current capital by fetching account balance"""
        if not self.exchange_connector:
            return
        
        now = datetime.now()
        
        # Check if update is needed
        if not force_update and self.last_balance_update:
            time_since_update = (now - self.last_balance_update).total_seconds()
            if time_since_update < self.balance_update_interval:
                return
        
        try:
            # Fetch account balance from exchange
            balance = await self.exchange_connector.fetch_balance()
            
            if balance and 'USDT' in balance:
                # Update current capital with USDT balance
                usdt_balance = balance['USDT']
                if 'free' in usdt_balance and usdt_balance['free'] is not None:
                    try:
                        new_capital = float(usdt_balance['free'])
                        
                        if new_capital != self.current_capital:
                            # Log capital change
                            change = new_capital - self.current_capital
                            change_pct = (change / self.current_capital) * 100 if self.current_capital > 0 else 0
                            
                            self.logger.info(
                                f"💰 Capital updated: ${self.current_capital:.2f} → ${new_capital:.2f} "
                                f"({change:+.2f} / {change_pct:+.2f}%)"
                            )
                            
                            self.current_capital = new_capital
                        
                        self.last_balance_update = now
                    except (ValueError, TypeError) as e:
                        self.logger.warning(f"Failed to parse USDT balance value: {usdt_balance['free']} - {e}")
            elif balance and 'free' in balance and isinstance(balance['free'], dict):
                # Alternative balance structure
                free_balance = balance['free']
                if 'USDT' in free_balance and free_balance['USDT'] is not None:
                    try:
                        new_capital = float(free_balance['USDT'])
                        
                        if new_capital != self.current_capital:
                            # Log capital change
                            change = new_capital - self.current_capital
                            change_pct = (change / self.current_capital) * 100 if self.current_capital > 0 else 0
                            
                            self.logger.info(
                                f"💰 Capital updated: ${self.current_capital:.2f} → ${new_capital:.2f} "
                                f"({change:+.2f} / {change_pct:+.2f}%)"
                            )
                            
                            self.current_capital = new_capital
                        
                        self.last_balance_update = now
                    except (ValueError, TypeError) as e:
                        self.logger.warning(f"Failed to parse USDT balance from free: {free_balance['USDT']} - {e}")
            else:
                self.logger.debug(f"No USDT balance found in response: {balance}")
                # Keep existing capital as fallback
                    
        except Exception as e:
            self.logger.warning(f"Failed to update account balance: {e}")
    
    async def check_capital_available(self, required_margin: float, active_positions: dict) -> Tuple[bool, str]:
        """Check if sufficient capital available for position (margin requirement)"""
        try:
            # Get real-time free balance from exchange if available
            if self.exchange_connector:
                balance = await self.exchange_connector.fetch_balance()
                
                # Parse free USDT balance - Handle Bybit Unified Account
                free_usdt = 0.0
                total_usdt = 0.0
                
                # Method 1: Try Bybit Unified Account structure first
                if (isinstance(balance, dict) and 
                    'info' in balance and 
                    isinstance(balance['info'], dict) and 
                    'result' in balance['info'] and
                    isinstance(balance['info']['result'], dict) and
                    'list' in balance['info']['result'] and
                    len(balance['info']['result']['list']) > 0):
                    
                    account_info = balance['info']['result']['list'][0]
                    
                    # Find USDT coin info
                    if 'coin' in account_info:
                        for coin_info in account_info['coin']:
                            if coin_info.get('coin') == 'USDT':
                                # Bybit Unified Account structure
                                wallet_balance = float(coin_info.get('walletBalance', 0))
                                locked_balance = float(coin_info.get('locked', 0))
                                
                                total_usdt = wallet_balance
                                free_usdt = wallet_balance - locked_balance
                                
                                self.logger.debug(f"💰 Bybit Unified: wallet=${wallet_balance:.2f}, locked=${locked_balance:.2f}, free=${free_usdt:.2f}")
                                break
                
                # Method 2: Standard structure with null handling
                elif (isinstance(balance, dict) and 
                      'USDT' in balance and 
                      isinstance(balance['USDT'], dict)):
                    
                    usdt_info = balance['USDT']
                    free_val = usdt_info.get('free')
                    total_val = usdt_info.get('total', 0)
                    
                    if free_val is not None:
                        free_usdt = float(free_val)
                    elif total_val is not None:
                        # Bybit case: free is null, use total as free
                        total_usdt = float(total_val)
                        free_usdt = total_usdt  # Assume all is free if not locked
                    
                    if total_val is not None:
                        total_usdt = float(total_val)
                
                # Method 3: Free/Used/Total structure
                elif (isinstance(balance, dict) and 
                      'free' in balance and 
                      isinstance(balance['free'], dict)):
                    
                    free_balance = balance['free']
                    total_balance = balance.get('total', {})
                    
                    free_val = free_balance.get('USDT')
                    total_val = total_balance.get('USDT') if isinstance(total_balance, dict) else None
                    
                    if free_val is not None:
                        free_usdt = float(free_val)
                    elif total_val is not None:
                        # Assume all is free if free is null
                        free_usdt = float(total_val)
                    
                    if total_val is not None:
                        total_usdt = float(total_val)
                
                # Enhanced error message based on balance situation
                if free_usdt < required_margin:
                    if free_usdt == 0 and total_usdt > 0:
                        return False, (
                            f"🚨 Critical: $0 free USDT (${total_usdt:.2f} total locked) "
                            f"- need ${required_margin:.2f} margin. "
                            f"Check staking/lending/isolated accounts!"
                        )
                    elif free_usdt == 0 and total_usdt == 0:
                        return False, (
                            f"❌ No USDT available - need ${required_margin:.2f} margin. "
                            f"Please deposit funds to your account."
                        )
                    else:
                        return False, (
                            f"💰 Insufficient free USDT: ${free_usdt:.2f} available, "
                            f"${required_margin:.2f} margin required. "
                            f"(Total: ${total_usdt:.2f})"
                        )
                
                return True, ""
                
        except Exception as e:
            self.logger.warning(f"Failed to get free balance, using fallback calculation: {e}")
        
        # Fallback to original calculation if exchange fetch fails
        # Reserved capital for active positions
        reserved = sum(p.contracts * p.current_price for p in active_positions.values())
        available = self.current_capital - reserved
        
        if required_margin > available:
            return False, f"Insufficient capital: ${available:.2f} available, ${required_margin:.2f} margin required"
        
        return True, ""
    
    def adjust_capital_after_trade(self, pnl: float, fees: float) -> None:
        """Adjust capital after trade completion"""
        self.current_capital += pnl - fees
        
        # Log significant changes
        total_change = pnl - fees
        if abs(total_change) > 0.01:  # Only log changes > 1 cent
            self.logger.info(f"💰 Capital adjusted: PnL ${pnl:.2f}, Fees ${fees:.2f}, Net: ${total_change:+.2f}")
    
    def get_capital_info(self) -> dict:
        """Get current capital information"""
        return {
            'current_capital': self.current_capital,
            'last_update': self.last_balance_update,
            'update_interval': self.balance_update_interval
        } 