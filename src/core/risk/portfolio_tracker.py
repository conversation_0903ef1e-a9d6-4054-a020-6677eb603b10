"""Portfolio tracking and performance metrics"""
import logging
from datetime import datetime
from typing import Dict, List, Any
from src.core.models import Position


class PortfolioTracker:
    """Tracks portfolio performance and metrics"""
    
    def __init__(self):
        self.active_positions: Dict[str, Position] = {}
        self.position_history: List[Position] = []
        self.logger = logging.getLogger('PortfolioTracker')
    
    def add_active_position(self, position: Position) -> None:
        """Add position to active tracking"""
        self.active_positions[position.symbol] = position
        self.logger.debug(f"📊 Position added to tracking: {position.symbol}")
    
    def update_position(self, position: Position) -> None:
        """Update active position"""
        if position.symbol in self.active_positions:
            self.active_positions[position.symbol] = position
    
    def complete_position(self, position: Position) -> None:
        """Move position from active to history"""
        # Add to history
        self.position_history.append(position)
        
        # Remove from active
        if position.symbol in self.active_positions:
            del self.active_positions[position.symbol]
        
        self.logger.debug(f"📊 Position completed: {position.symbol}, PnL: ${position.pnl:.2f}")
    
    def calculate_total_exposure(self) -> float:
        """Calculate total market exposure"""
        return sum(
            position.contracts * position.current_price 
            for position in self.active_positions.values()
        )
    
    def calculate_drawdown(self, current_capital: float) -> float:
        """Calculate current drawdown from peak"""
        if not self.position_history or current_capital <= 0:
            return 0.0
        
        # Calculate peak capital starting from current capital
        # Work backwards from current capital to find historical peak
        peak_capital = current_capital
        running_capital = current_capital
        
        # Add back all historical PnL to reconstruct peak
        for position in reversed(self.position_history):
            running_capital -= (position.pnl - position.fees_paid)
            peak_capital = max(peak_capital, running_capital)
        
        # Current drawdown
        if peak_capital <= 0:
            return 0.0
        
        drawdown = (peak_capital - current_capital) / peak_capital * 100
        return max(0, drawdown)
    
    def calculate_win_rate(self) -> float:
        """Calculate historical win rate"""
        if not self.position_history:
            return 0.0
        
        winning_trades = sum(1 for p in self.position_history if p.pnl > 0)
        total_trades = len(self.position_history)
        
        return (winning_trades / total_trades) * 100 if total_trades > 0 else 0.0
    
    def calculate_avg_win_loss_ratio(self) -> float:
        """Calculate average win/loss ratio"""
        if not self.position_history:
            return 0.0
        
        wins = [p.pnl for p in self.position_history if p.pnl > 0]
        losses = [abs(p.pnl) for p in self.position_history if p.pnl < 0]
        
        if not wins or not losses:
            return 0.0
        
        avg_win = sum(wins) / len(wins)
        avg_loss = sum(losses) / len(losses)
        
        return avg_win / avg_loss if avg_loss > 0 else 0.0
    
    def calculate_profit_factor(self) -> float:
        """Calculate profit factor (total wins / total losses)"""
        if not self.position_history:
            return 0.0
        
        total_wins = sum(p.pnl for p in self.position_history if p.pnl > 0)
        total_losses = abs(sum(p.pnl for p in self.position_history if p.pnl < 0))
        
        return total_wins / total_losses if total_losses > 0 else 0.0
    
    def calculate_sharpe_ratio(self, current_capital: float, risk_free_rate: float = 0.02) -> float:
        """Calculate Sharpe ratio (simplified version)"""
        if not self.position_history or current_capital <= 0:
            return 0.0
        
        # Calculate returns
        returns = [p.pnl / current_capital for p in self.position_history]
        
        if not returns:
            return 0.0
        
        # Calculate excess return
        avg_return = sum(returns) / len(returns)
        excess_return = avg_return - (risk_free_rate / 252)  # Daily risk-free rate
        
        # Calculate standard deviation
        if len(returns) < 2:
            return 0.0
        
        variance = sum((r - avg_return) ** 2 for r in returns) / (len(returns) - 1)
        std_dev = variance ** 0.5
        
        return excess_return / std_dev if std_dev > 0 else 0.0
    
    def get_performance_summary(self, current_capital: float) -> Dict[str, Any]:
        """Get comprehensive performance summary"""
        total_trades = len(self.position_history)
        winning_trades = sum(1 for p in self.position_history if p.pnl > 0)
        losing_trades = sum(1 for p in self.position_history if p.pnl < 0)
        
        total_pnl = sum(p.pnl for p in self.position_history)
        total_fees = sum(p.fees_paid for p in self.position_history)
        net_pnl = total_pnl - total_fees
        
        # Calculate max consecutive wins/losses
        max_consecutive_wins = self._calculate_max_consecutive(True)
        max_consecutive_losses = self._calculate_max_consecutive(False)
        
        # Calculate average trade duration
        avg_duration_hours = self._calculate_avg_trade_duration()
        
        return {
            # Basic stats
            'total_trades': total_trades,
            'winning_trades': winning_trades,
            'losing_trades': losing_trades,
            'breakeven_trades': total_trades - winning_trades - losing_trades,
            
            # Performance metrics
            'win_rate_percentage': self.calculate_win_rate(),
            'avg_win_loss_ratio': self.calculate_avg_win_loss_ratio(),
            'profit_factor': self.calculate_profit_factor(),
            'sharpe_ratio': self.calculate_sharpe_ratio(current_capital),
            
            # PnL stats
            'total_pnl': total_pnl,
            'total_fees': total_fees,
            'net_pnl': net_pnl,
            'total_pnl_percentage': (total_pnl / current_capital) * 100 if current_capital > 0 else 0,
            
            # Risk metrics
            'current_drawdown': self.calculate_drawdown(current_capital),
            'active_positions': len(self.active_positions),
            'total_exposure': self.calculate_total_exposure(),
            
            # Trade patterns
            'max_consecutive_wins': max_consecutive_wins,
            'max_consecutive_losses': max_consecutive_losses,
            'avg_trade_duration_hours': avg_duration_hours,
            
            # Best/worst trades
            'best_trade': max((p.pnl for p in self.position_history), default=0),
            'worst_trade': min((p.pnl for p in self.position_history), default=0),
            'avg_trade_pnl': total_pnl / total_trades if total_trades > 0 else 0
        }
    
    def _calculate_max_consecutive(self, wins: bool) -> int:
        """Calculate maximum consecutive wins or losses"""
        if not self.position_history:
            return 0
        
        max_consecutive = 0
        current_consecutive = 0
        
        for position in self.position_history:
            if (wins and position.pnl > 0) or (not wins and position.pnl < 0):
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def _calculate_avg_trade_duration(self) -> float:
        """Calculate average trade duration in hours"""
        if not self.position_history:
            return 0.0
        
        durations = []
        for position in self.position_history:
            if position.opened_at and position.closed_at:
                duration = (position.closed_at - position.opened_at).total_seconds() / 3600
                durations.append(duration)
        
        return sum(durations) / len(durations) if durations else 0.0
    
    def get_active_positions_summary(self) -> List[Dict[str, Any]]:
        """Get summary of all active positions"""
        return [
            {
                'symbol': pos.symbol,
                'side': pos.side,
                'entry_price': pos.entry_price,
                'current_price': pos.current_price,
                'contracts': pos.contracts,
                'pnl': pos.pnl,
                'pnl_percentage': pos.pnl_percentage,
                'unrealized_value': pos.contracts * pos.current_price,
                'duration_hours': (datetime.now() - pos.opened_at).total_seconds() / 3600 if pos.opened_at else 0
            }
            for pos in self.active_positions.values()
        ]
    
    def clear_history(self) -> None:
        """Clear position history (for testing or reset)"""
        self.position_history = []
        self.logger.info("📊 Portfolio history cleared")
    
    def export_trade_history(self) -> List[Dict[str, Any]]:
        """Export complete trade history"""
        return [pos.to_dict() for pos in self.position_history] 