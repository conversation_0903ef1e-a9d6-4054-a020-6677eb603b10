"""Risk metrics data structures"""
from dataclasses import dataclass


@dataclass
class RiskMetrics:
    """Risk metrics for trading decisions"""
    position_size: float
    risk_amount: float
    risk_percentage: float
    can_trade: bool
    reason: str = ""
    
    # Portfolio metrics
    total_exposure: float = 0.0
    daily_loss: float = 0.0
    daily_trades: int = 0
    current_drawdown: float = 0.0 