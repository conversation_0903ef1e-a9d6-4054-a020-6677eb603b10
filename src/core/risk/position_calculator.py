"""Position size calculation logic - refactored to use unified AmountCalculator"""
import logging
from typing import List, Dict
from src.core.models import Signal, TradeConfig, Position
from .amount_calculator import AmountCalculator, AmountStrategy


class PositionCalculator:
    """Calculates position sizes using unified amount calculation logic"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        self.logger = logging.getLogger('PositionCalculator')
        # Use unified amount calculator
        self.amount_calculator = AmountCalculator(config)
    
    def calculate_position_size(self, signal: Signal, current_capital: float, 
                              position_history: List[Position]) -> float:
        """
        Calculate position size using unified amount calculation
        Returns the amount in USD, not contracts
        """
        # Safety check for zero capital
        if current_capital <= 0:
            self.logger.error(f"❌ Cannot calculate position size: current_capital={current_capital}")
            return 0.0
        
        # Calculate amount using unified calculator
        amount_result = self.amount_calculator.calculate_position_amount(
            signal, current_capital, position_history
        )
        
        if not amount_result.get('valid', False):
            self.logger.error(f"❌ Invalid amount calculation result")
            return 0.0
        
        final_amount = amount_result['amount']
        
        # Log calculation details
        self.logger.debug(
            f"📊 Position sizing: {amount_result['method']} = ${final_amount:.2f}"
        )
        
        # Add additional debug info if available
        if 'was_constrained' in amount_result and amount_result['was_constrained']:
            constraint = amount_result.get('constraint_applied', 'unknown')
            original = amount_result.get('original_amount', final_amount)
            self.logger.debug(
                f"🔒 Amount constrained ({constraint}): ${original:.2f} -> ${final_amount:.2f}"
            )
        
        return final_amount
    
    def calculate_position_contracts(self, signal: Signal, current_capital: float, 
                                   position_history: List[Position], 
                                   current_price: float) -> float:
        """
        Calculate position size in contracts (for order execution)
        """
        if current_price <= 0:
            self.logger.error(f"❌ Cannot calculate contracts: invalid price {current_price}")
            return 0.0
        
        # Get amount in USD
        amount_usd = self.calculate_position_size(signal, current_capital, position_history)
        
        if amount_usd <= 0:
            return 0.0
        
        # Convert to contracts
        contracts = amount_usd / current_price
        
        self.logger.debug(
            f"📊 Position contracts: ${amount_usd:.2f} / ${current_price:.6f} = {contracts:.6f}"
        )
        
        return contracts
    
    def calculate_dca_position_size(self, dca_strategy: str, dca_config: Dict, 
                                  current_price: float, level: int = 0) -> Dict[str, float]:
        """Calculate DCA position size using unified calculator"""
        return self.amount_calculator.calculate_dca_amount(
            dca_strategy, dca_config, current_price, level
        )
    
    def calculate_total_dca_exposure(self, entry_price: float) -> Dict[str, float]:
        """Calculate total DCA exposure using unified calculator"""
        return self.amount_calculator.calculate_total_dca_exposure(entry_price)
    
    def validate_position_size(self, position_size: float, current_capital: float) -> bool:
        """Validate calculated position size against constraints"""
        is_valid, reason = self.amount_calculator.validate_amount(position_size, current_capital)
        
        if not is_valid:
            self.logger.warning(f"⚠️ Position validation failed: {reason}")
        
        return is_valid
    
    def get_sizing_info(self, current_capital: float, position_history: List[Position]) -> Dict[str, any]:
        """Get comprehensive position sizing information"""
        # Get amount calculator info
        amount_info = self.amount_calculator.get_amount_info(current_capital, position_history)
        
        # Add position calculator specific info
        sizing_info = {
            **amount_info,
            'calculator_type': 'Unified Position Calculator',
            'supports_config_amount': True,
            'supports_risk_based': True,
            'supports_hybrid': True,
            'supports_kelly': True,
            'supports_dca_integration': True
        }
        
        return sizing_info
    
    def get_amount_strategy_recommendation(self, current_capital: float) -> AmountStrategy:
        """Get recommended amount strategy for current conditions"""
        return self.amount_calculator._detect_optimal_strategy(current_capital)
    
    def compare_strategies(self, signal: Signal, current_capital: float, 
                         position_history: List[Position]) -> Dict[str, Dict]:
        """Compare all available strategies for debugging/analysis"""
        strategies = [
            AmountStrategy.CONFIG_AMOUNT,
            AmountStrategy.RISK_BASED,
            AmountStrategy.HYBRID,
            AmountStrategy.KELLY_CRITERION
        ]
        
        comparison = {}
        
        for strategy in strategies:
            try:
                result = self.amount_calculator.calculate_position_amount(
                    signal, current_capital, position_history, strategy
                )
                comparison[strategy.value] = result
            except Exception as e:
                comparison[strategy.value] = {
                    'error': str(e),
                    'valid': False
                }
        
        return comparison
    
    # Backward compatibility methods (deprecated)
    def _kelly_criterion_size(self, signal: Signal, current_capital: float, 
                            position_history: List[Position]) -> float:
        """
        DEPRECATED: Use calculate_position_size with AmountStrategy.KELLY_CRITERION
        Kept for backward compatibility
        """
        self.logger.warning("⚠️ _kelly_criterion_size is deprecated, use calculate_position_size instead")
        
        result = self.amount_calculator.calculate_position_amount(
            signal, current_capital, position_history, AmountStrategy.KELLY_CRITERION
        )
        
        return result.get('amount', 0.0)
    
    def _calculate_win_rate(self, position_history: List[Position]) -> float:
        """DEPRECATED: Use amount_calculator._calculate_win_rate"""
        self.logger.warning("⚠️ _calculate_win_rate is deprecated")
        return self.amount_calculator._calculate_win_rate(position_history)
    
    def _calculate_avg_win_loss_ratio(self, position_history: List[Position]) -> float:
        """DEPRECATED: Use amount_calculator._calculate_avg_win_loss_ratio"""
        self.logger.warning("⚠️ _calculate_avg_win_loss_ratio is deprecated")
        return self.amount_calculator._calculate_avg_win_loss_ratio(position_history) 