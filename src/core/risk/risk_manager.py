"""Refactored Risk Manager - Main orchestrator using modular components"""
import logging
from typing import Any, Dict
from src.core.models import Position, Signal, TradeConfig

# Import modular components
from .risk_metrics import RiskMetrics
from .capital_manager import CapitalManager
from .position_calculator import PositionCalculator
from .risk_validator import RiskValidator
from .portfolio_tracker import PortfolioTracker


class RiskManager:
    """Main risk management orchestrator using modular components"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        self.logger = logging.getLogger('RiskManager')
        
        # Initialize modular components
        self.capital_manager = CapitalManager()
        self.position_calculator = PositionCalculator(config)
        self.risk_validator = RiskValidator(config)
        self.portfolio_tracker = PortfolioTracker()
        
        self.logger.info("🛡️ Risk Manager initialized with modular components")
    
    def set_exchange_connector(self, exchange_connector) -> None:
        """Set exchange connector for capital management"""
        self.capital_manager.set_exchange_connector(exchange_connector)
    
    async def check_trade_allowed(self, signal: Signal) -> RiskMetrics:
        """Main entry point - check if trade is allowed based on all risk rules"""
        # Update current capital from exchange
        await self.capital_manager.update_current_capital()
        
        # Calculate position size (now returns amount in USD, not contracts)
        position_amount = self.position_calculator.calculate_position_size(
            signal, 
            self.capital_manager.current_capital, 
            self.portfolio_tracker.position_history
        )
        
        # Calculate risk metrics based on actual position amount
        risk_amount = position_amount  # Position amount IS the risk amount
        risk_percentage = (risk_amount / self.capital_manager.current_capital) * 100 if self.capital_manager.current_capital > 0 else 0
        
        # Initialize metrics
        metrics = RiskMetrics(
            position_size=position_amount,
            risk_amount=risk_amount,
            risk_percentage=risk_percentage,
            can_trade=True
        )
        
        # Run all risk validation checks
        risk_check_passed, risk_reason = self.risk_validator.check_all_risk_conditions(
            metrics, 
            self.capital_manager.current_capital,
            self.portfolio_tracker.active_positions
        )
        
        if not risk_check_passed:
            metrics.can_trade = False
            metrics.reason = risk_reason
        
        # If risk checks passed, verify capital availability
        if metrics.can_trade:
            # Calculate required margin for leveraged trading
            required_margin = position_amount / self.config.risk.leverage
            capital_check_passed, capital_reason = await self.capital_manager.check_capital_available(
                required_margin, 
                self.portfolio_tracker.active_positions
            )
            if not capital_check_passed:
                metrics.can_trade = False
                metrics.reason = capital_reason
        
        # Calculate portfolio metrics for the response
        metrics.total_exposure = self.portfolio_tracker.calculate_total_exposure()
        metrics.daily_trades = self.risk_validator.daily_trades
        metrics.current_drawdown = self.portfolio_tracker.calculate_drawdown(self.capital_manager.current_capital)
        
        # Log risk decision
        if metrics.can_trade:
            self.logger.info(f"✅ Trade approved: ${position_amount:.2f} position, risk: {risk_percentage:.2f}%")
        else:
            self.logger.warning(f"🚫 Trade rejected: {metrics.reason}")
        
        return metrics
    
    def add_active_position(self, position: Position) -> None:
        """Add position to tracking"""
        self.portfolio_tracker.add_active_position(position)
        self.logger.debug(f"📊 Position added to risk tracking: {position.symbol}")
    
    def update_position(self, position: Position) -> None:
        """Update active position"""
        self.portfolio_tracker.update_position(position)
    
    def record_trade_result(self, position: Position) -> None:
        """Record completed trade result"""
        # Update capital
        self.capital_manager.adjust_capital_after_trade(position.pnl, position.fees_paid)
        
        # Record in validator for risk tracking
        self.risk_validator.record_trade_result(position.pnl)
        
        # Move to portfolio history
        self.portfolio_tracker.complete_position(position)
        
        # Log trade result
        self.logger.info(
            f"📊 Trade recorded: {position.symbol} PnL: ${position.pnl:.2f}, "
            f"New capital: ${self.capital_manager.current_capital:.2f}"
        )
    
    async def update_current_capital(self, force_update: bool = False) -> None:
        """Update current capital (delegated to capital manager)"""
        await self.capital_manager.update_current_capital(force_update)
    
    @property
    def current_capital(self) -> float:
        """Get current capital"""
        return self.capital_manager.current_capital
    
    @current_capital.setter
    def current_capital(self, value: float) -> None:
        """Set current capital"""
        self.capital_manager.current_capital = value
    
    @property
    def active_positions(self) -> Dict[str, Position]:
        """Get active positions"""
        return self.portfolio_tracker.active_positions
    
    @property
    def position_history(self) -> list:
        """Get position history"""
        return self.portfolio_tracker.position_history
    
    def get_risk_summary(self) -> Dict[str, Any]:
        """Get comprehensive risk metrics summary"""
        # Get component summaries
        capital_info = self.capital_manager.get_capital_info()
        risk_status = self.risk_validator.get_risk_status(
            self.capital_manager.current_capital, 
            self.portfolio_tracker.active_positions
        )
        performance_summary = self.portfolio_tracker.get_performance_summary(
            self.capital_manager.current_capital
        )
        sizing_info = self.position_calculator.get_sizing_info(
            self.capital_manager.current_capital,
            self.portfolio_tracker.position_history
        )
        
        # Combine all metrics
        return {
            # Capital metrics
            'capital': capital_info,
            
            # Risk validation status
            'risk_status': risk_status,
            
            # Portfolio performance
            'performance': performance_summary,
            
            # Position sizing info
            'sizing': sizing_info,
            
            # Quick summary metrics
            'summary': {
                'current_capital': self.capital_manager.current_capital,
                'active_positions': len(self.portfolio_tracker.active_positions),
                'total_exposure': self.portfolio_tracker.calculate_total_exposure(),
                'current_drawdown': self.portfolio_tracker.calculate_drawdown(self.capital_manager.current_capital),
                'win_rate': self.portfolio_tracker.calculate_win_rate(),
                'profit_factor': self.portfolio_tracker.calculate_profit_factor(),
                'daily_trades': self.risk_validator.daily_trades,
                'in_cooldown': risk_status.get('in_cooldown', False)
            }
        }
    
    def get_active_positions_summary(self):
        """Get summary of active positions"""
        return self.portfolio_tracker.get_active_positions_summary()
    
    def force_reset_daily_counters(self) -> None:
        """Force reset daily risk counters"""
        self.risk_validator.force_reset_daily_counters()
        self.logger.info("🔄 Daily risk counters manually reset")
    
    def clear_cooldown(self) -> None:
        """Clear cooldown period"""
        self.risk_validator.clear_cooldown()
        self.logger.info("🔄 Cooldown period manually cleared")
    
    def export_portfolio_data(self) -> Dict[str, Any]:
        """Export complete portfolio data"""
        return {
            'active_positions': self.portfolio_tracker.get_active_positions_summary(),
            'trade_history': self.portfolio_tracker.export_trade_history(),
            'risk_summary': self.get_risk_summary()
        } 