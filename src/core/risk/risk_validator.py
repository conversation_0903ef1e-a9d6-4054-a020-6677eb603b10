"""Risk validation checks"""
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from src.core.models import TradeConfig, Position
from .risk_metrics import RiskMetrics


class RiskValidator:
    """Performs various risk validation checks"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        self.logger = logging.getLogger('RiskValidator')
        
        # Track daily metrics
        self.daily_losses: List[float] = []
        self.daily_trades: int = 0
        self.last_reset: datetime = datetime.now()
        
        # Cooldown tracking
        self.last_loss_time: Optional[datetime] = None
        self.consecutive_losses: int = 0
    
    def check_all_risk_conditions(self, metrics: RiskMetrics, current_capital: float,
                                active_positions: Dict[str, Position]) -> Tuple[bool, str]:
        """Run all risk validation checks"""
        # Reset daily counters if needed
        self._check_daily_reset()
        
        # Check various risk conditions
        checks = [
            # self._check_daily_loss_limit(metrics, current_capital),
            self._check_max_positions(metrics, active_positions),
            self._check_position_size_limit(metrics),
            self._check_daily_trade_limit(metrics),
            self._check_cooldown_period(metrics),
            self._check_leverage_limit(metrics, current_capital, active_positions)
        ]
        
        # If any check fails, return the first failure
        for check_passed, reason in checks:
            if not check_passed:
                return False, reason
        
        return True, ""
    
    def _check_daily_reset(self) -> None:
        """Reset daily counters if new day"""
        now = datetime.now()
        if now.date() > self.last_reset.date():
            self.daily_losses = []
            self.daily_trades = 0
            self.last_reset = now
            self.logger.debug("📅 Daily risk counters reset")
    
    def _check_daily_loss_limit(self, metrics: RiskMetrics, current_capital: float) -> Tuple[bool, str]:
        """Check if daily loss limit exceeded"""
        # Use current_capital with safety check
        if current_capital <= 0:
            return False, "Cannot calculate daily loss limit: current capital is zero"
        
        daily_loss_percentage = abs(sum(self.daily_losses)) / current_capital * 100
        
        if daily_loss_percentage >= self.config.risk.daily_loss_limit:
            return False, f"Daily loss limit reached: {daily_loss_percentage:.2f}%"
        
        # Check if this trade would exceed limit
        potential_loss = metrics.risk_amount
        potential_total_loss = abs(sum(self.daily_losses)) + potential_loss
        potential_loss_percentage = potential_total_loss / current_capital * 100
        
        if potential_loss_percentage > self.config.risk.daily_loss_limit:
            return False, f"Trade would exceed daily loss limit: {potential_loss_percentage:.2f}%"
        
        return True, ""
    
    def _check_max_positions(self, metrics: RiskMetrics, active_positions: Dict[str, Position]) -> Tuple[bool, str]:
        """Check if max concurrent positions reached"""
        active_count = len(active_positions)
        
        if active_count >= self.config.risk.max_concurrent_positions:
            return False, f"Max concurrent positions reached: {active_count}"
        
        return True, ""
    
    def _check_position_size_limit(self, metrics: RiskMetrics) -> Tuple[bool, str]:
        """Check if position size within limits"""
        if metrics.position_size > self.config.risk.max_position_size:
            return False, f"Position size ${metrics.position_size:.2f} exceeds max ${self.config.risk.max_position_size}"
        
        if metrics.position_size < self.config.risk.min_position_size:
            return False, f"Position size ${metrics.position_size:.2f} below minimum ${self.config.risk.min_position_size}"
        
        return True, ""
    
    def _check_daily_trade_limit(self, metrics: RiskMetrics) -> Tuple[bool, str]:
        """Check if daily trade limit reached"""
        if self.daily_trades >= self.config.risk.max_daily_trades:
            return False, f"Daily trade limit reached: {self.daily_trades}"
        
        return True, ""
    
    def _check_cooldown_period(self, metrics: RiskMetrics) -> Tuple[bool, str]:
        """Check if in cooldown period after loss"""
        if self.last_loss_time:
            cooldown_end = self.last_loss_time + timedelta(
                minutes=self.config.risk.loss_cooldown_minutes
            )
            
            if datetime.now() < cooldown_end:
                remaining = (cooldown_end - datetime.now()).total_seconds() / 60
                return False, f"In cooldown period, {remaining:.1f} minutes remaining"
        
        return True, ""
    
    def _check_leverage_limit(self, metrics: RiskMetrics, current_capital: float,
                            active_positions: Dict[str, Position]) -> Tuple[bool, str]:
        """Check if leverage within limits"""
        # Safety check for division by zero
        if current_capital <= 0:
            return False, "Cannot calculate leverage: current capital is zero"
            
        # Calculate effective leverage
        total_exposure = self._calculate_total_exposure(active_positions) + (metrics.position_size)
        effective_leverage = total_exposure / current_capital
        
        if effective_leverage > self.config.risk.leverage:
            return False, f"Would exceed max leverage: {effective_leverage:.2f}x"
        
        return True, ""
    
    def _calculate_total_exposure(self, active_positions: Dict[str, Position]) -> float:
        """Calculate total market exposure"""
        return sum(
            position.contracts * position.current_price 
            for position in active_positions.values()
        )
    
    def record_trade_result(self, pnl: float) -> None:
        """Record trade result for risk tracking"""
        # Track daily losses
        if pnl < 0:
            self.daily_losses.append(pnl)
            self.last_loss_time = datetime.now()
            self.consecutive_losses += 1
            self.logger.debug(f"📉 Loss recorded: ${pnl:.2f}, consecutive: {self.consecutive_losses}")
        else:
            self.consecutive_losses = 0
            self.logger.debug(f"📈 Profit recorded: ${pnl:.2f}, consecutive losses reset")
        
        # Update counters
        self.daily_trades += 1
    
    def get_risk_status(self, current_capital: float, active_positions: Dict[str, Position]) -> dict:
        """Get current risk validation status"""
        self._check_daily_reset()
        
        # Calculate daily loss percentage with safety check
        daily_loss_pct = 0.0
        if current_capital > 0:
            daily_loss_pct = abs(sum(self.daily_losses)) / current_capital * 100
        
        # Calculate leverage
        total_exposure = self._calculate_total_exposure(active_positions)
        leverage = total_exposure / current_capital if current_capital > 0 else 0
        
        # Check cooldown status
        in_cooldown = False
        cooldown_remaining = 0
        if self.last_loss_time:
            cooldown_end = self.last_loss_time + timedelta(minutes=self.config.risk.loss_cooldown_minutes)
            in_cooldown = datetime.now() < cooldown_end
            if in_cooldown:
                cooldown_remaining = (cooldown_end - datetime.now()).total_seconds() / 60
        
        return {
            'daily_trades': self.daily_trades,
            'daily_trades_limit': self.config.risk.max_daily_trades,
            'daily_loss': sum(self.daily_losses),
            'daily_loss_percentage': daily_loss_pct,
            'daily_loss_limit': self.config.risk.daily_loss_limit,
            'active_positions': len(active_positions),
            'max_positions': self.config.risk.max_concurrent_positions,
            'total_exposure': total_exposure,
            'current_leverage': leverage,
            'leverage': self.config.risk.leverage,
            'consecutive_losses': self.consecutive_losses,
            'in_cooldown': in_cooldown,
            'cooldown_remaining_minutes': cooldown_remaining,
            'last_reset': self.last_reset
        }
    
    def force_reset_daily_counters(self) -> None:
        """Force reset daily counters (for testing or manual reset)"""
        self.daily_losses = []
        self.daily_trades = 0
        self.last_reset = datetime.now()
        self.logger.info("🔄 Daily risk counters manually reset")
    
    def clear_cooldown(self) -> None:
        """Clear cooldown period (for manual intervention)"""
        self.last_loss_time = None
        self.consecutive_losses = 0
        self.logger.info("🔄 Cooldown period manually cleared") 