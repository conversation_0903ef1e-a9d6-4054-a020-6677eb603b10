"""Unified amount calculation logic"""
import logging
from typing import Dict, List, Optional, Tuple
from enum import Enum
from src.core.models import Signal, TradeConfig, Position


class AmountStrategy(Enum):
    """Amount calculation strategies"""
    CONFIG_AMOUNT = 'config_amount'  # Use config.amount
    RISK_BASED = 'risk_based'  # Use risk_per_trade %
    HYBRID = 'hybrid'  # Combine both with validation
    KELLY_CRITERION = 'kelly_criterion'  # Use Kelly formula


class AmountCalculator:
    """Unified calculator for all amount-related calculations"""
    
    def __init__(self, config: TradeConfig):
        self.config = config
        self.logger = logging.getLogger('AmountCalculator')
    
    def calculate_position_amount(self, signal: Signal, current_capital: float, 
                                 position_history: List[Position], 
                                 strategy: AmountStrategy = AmountStrategy.CONFIG_AMOUNT) -> Dict[str, float]:
        """
        Calculate position amount using specified strategy
        Returns dict with amount, method, and validation info
        """
        # Auto-detect strategy if not specified
        if strategy is None:
            strategy = self._detect_optimal_strategy(current_capital)
        
        # Safety check for zero capital
        if current_capital <= 0:
            self.logger.error(f"❌ Cannot calculate amount: current_capital={current_capital}")
            return self._create_error_result()
        
        # Calculate base amount based on strategy
        if strategy == AmountStrategy.CONFIG_AMOUNT:
            result = self._calculate_config_amount()
        elif strategy == AmountStrategy.RISK_BASED:
            result = self._calculate_risk_based_amount(signal, current_capital, position_history)
        elif strategy == AmountStrategy.HYBRID:
            result = self._calculate_hybrid_amount(signal, current_capital, position_history)
        elif strategy == AmountStrategy.KELLY_CRITERION:
            result = self._calculate_kelly_amount(signal, current_capital, position_history)
        else:
            self.logger.error(f"❌ Unknown amount strategy: {strategy}")
            return self._create_error_result()
        
        # Apply signal strength adjustment if applicable
        if signal and signal.strength > 0:
            result['amount'] *= signal.strength
            result['adjusted_for_signal_strength'] = signal.strength
        
        # Apply risk constraints
        result = self._apply_risk_constraints(result, current_capital)
        
        self.logger.debug(
            f"📊 Amount calculation: {result['method']} = ${result['amount']:.2f} "
            f"(base: ${result.get('base_amount', 0):.2f}, "
            f"strength: {result.get('adjusted_for_signal_strength', 1.0):.2f})"
        )
        
        return result
    
    def calculate_dca_amount(self, dca_strategy: str, dca_config: Dict, 
                           current_price: float, level: int = 0) -> Dict[str, float]:
        """Calculate DCA amount for specific strategy and level"""
        try:
            # Get base DCA amount (from config or fallback)
            base_dca_amount = dca_config.get('amount', self.config.amount)
            
            # Apply DCA volume scaling for level
            if level > 0:
                volume_multiplier = self.config.dca.volume_scale ** level
                dca_amount = base_dca_amount * volume_multiplier
            else:
                dca_amount = base_dca_amount
            
            # Convert to position size (contracts)
            if current_price > 0:
                position_size = dca_amount / current_price
            else:
                self.logger.error(f"❌ Invalid price for DCA calculation: {current_price}")
                return self._create_error_result()
            
            return {
                'amount': dca_amount,
                'position_size': position_size,
                'level': level,
                'volume_multiplier': volume_multiplier if level > 0 else 1.0,
                'strategy': dca_strategy,
                'method': f'DCA_{dca_strategy}_L{level}',
                'valid': True
            }
            
        except Exception as e:
            self.logger.error(f"❌ DCA amount calculation error for {dca_strategy}: {e}")
            return self._create_error_result()
    
    def calculate_total_dca_exposure(self, entry_price: float) -> Dict[str, float]:
        """Calculate total potential DCA exposure across all levels"""
        try:
            total_exposure = self.config.amount  # Initial position
            total_levels = 1
            
            # Add all DCA levels
            for level in range(1, self.config.dca.max_orders + 1):
                level_amount = self.config.amount * (self.config.dca.volume_scale ** level)
                total_exposure += level_amount
                total_levels += 1
            
            return {
                'total_exposure': total_exposure,
                'initial_amount': self.config.amount,
                'total_levels': total_levels,
                'max_drawdown_exposure': total_exposure,
                'average_amount_per_level': total_exposure / total_levels,
                'valid': True
            }
            
        except Exception as e:
            self.logger.error(f"❌ Total DCA exposure calculation error: {e}")
            return self._create_error_result()
    
    def _detect_optimal_strategy(self, current_capital: float) -> AmountStrategy:
        """Auto-detect optimal amount calculation strategy"""
        # Use Kelly if explicitly enabled
        if self.config.risk.use_kelly_criterion:
            return AmountStrategy.KELLY_CRITERION
        
        # Use hybrid approach if we have both config amount and sufficient capital
        if (self.config.amount > 0 and 
            current_capital > 0 and 
            self.config.amount <= current_capital * 0.5):  # Config amount is reasonable
            return AmountStrategy.HYBRID
        
        # Fall back to risk-based for safety
        return AmountStrategy.RISK_BASED
    
    def _calculate_config_amount(self) -> Dict[str, float]:
        """Calculate using config.amount"""
        self.logger.debug(f"🛡️ Calculating config amount: {self.config.amount}")
        return {
            'amount': self.config.amount,
            'base_amount': self.config.amount,
            'method': 'Config Amount',
            'strategy': AmountStrategy.CONFIG_AMOUNT,
            'valid': self.config.amount > 0
        }
    
    def _calculate_risk_based_amount(self, signal: Signal, current_capital: float, 
                                   position_history: List[Position]) -> Dict[str, float]:
        """Calculate using risk_per_trade percentage"""
        base_amount = current_capital * (self.config.risk.risk_per_trade / 100)
        
        return {
            'amount': base_amount,
            'base_amount': base_amount,
            'method': f'Risk Based ({self.config.risk.risk_per_trade}%)',
            'strategy': AmountStrategy.RISK_BASED,
            'risk_percentage': self.config.risk.risk_per_trade,
            'capital_used': current_capital,
            'valid': base_amount > 0
        }
    
    def _calculate_hybrid_amount(self, signal: Signal, current_capital: float, 
                               position_history: List[Position]) -> Dict[str, float]:
        """Calculate using hybrid approach - best of both worlds"""
        # Get both calculations
        config_amount = self.config.amount
        risk_amount = current_capital * (self.config.risk.risk_per_trade / 100)
        
        # Use the smaller of the two for safety
        final_amount = min(config_amount, risk_amount)
        
        # Determine which method was used
        method_used = 'Config Amount' if final_amount == config_amount else 'Risk Based'
        
        return {
            'amount': final_amount,
            'base_amount': final_amount,
            'method': f'Hybrid ({method_used})',
            'strategy': AmountStrategy.HYBRID,
            'config_amount': config_amount,
            'risk_amount': risk_amount,
            'chosen_method': method_used,
            'valid': final_amount > 0
        }
    
    def _calculate_kelly_amount(self, signal: Signal, current_capital: float, 
                              position_history: List[Position]) -> Dict[str, float]:
        """Calculate using Kelly Criterion"""
        # Get historical metrics
        win_rate = self._calculate_win_rate(position_history)
        avg_win_loss_ratio = self._calculate_avg_win_loss_ratio(position_history)
        
        if win_rate == 0 or avg_win_loss_ratio == 0:
            # Fallback to risk-based
            fallback = self._calculate_risk_based_amount(signal, current_capital, position_history)
            fallback['method'] = 'Kelly Fallback (Risk Based)'
            fallback['fallback_reason'] = 'Insufficient history'
            return fallback
        
        # Kelly formula: f = (p * b - q) / b
        q = 1 - win_rate
        kelly_percentage = (win_rate * avg_win_loss_ratio - q) / avg_win_loss_ratio
        
        # Apply Kelly fraction (conservative)
        kelly_fraction = 0.25
        position_percentage = max(0, kelly_percentage * kelly_fraction)
        
        # Cap at risk_per_trade
        position_percentage = min(position_percentage, self.config.risk.risk_per_trade * self.config.risk.leverage / 100)
        
        kelly_amount = current_capital * position_percentage
        
        return {
            'amount': kelly_amount,
            'base_amount': kelly_amount,
            'method': f'Kelly Criterion ({position_percentage:.1%})',
            'strategy': AmountStrategy.KELLY_CRITERION,
            'win_rate': win_rate,
            'win_loss_ratio': avg_win_loss_ratio,
            'kelly_percentage': kelly_percentage,
            'kelly_fraction': kelly_fraction,
            'final_percentage': position_percentage,
            'valid': kelly_amount > 0
        }
    
    def _apply_risk_constraints(self, result: Dict[str, float], current_capital: float) -> Dict[str, float]:
        """Apply min/max constraints from risk config"""
        original_amount = result['amount']
        
        # Apply min/max constraints
        min_amount = self.config.risk.min_position_size
        max_amount_abs = self.config.risk.max_position_size
        max_amount_pct = current_capital * (self.config.risk.max_position_percentage * self.config.risk.leverage / 100)
        max_amount = min(max_amount_abs, max_amount_pct)

        self.logger.debug(f"🛡️ Applying risk constraints: min_amount={min_amount}, max_amount_abs={max_amount_abs}, max_amount_pct={max_amount_pct}")
        
        # Apply constraints
        constrained_amount = max(min_amount, min(original_amount, max_amount))
        
        # Update result
        result['amount'] = constrained_amount
        result['original_amount'] = original_amount
        result['min_constraint'] = min_amount
        result['max_constraint'] = max_amount
        result['was_constrained'] = constrained_amount != original_amount
        
        if result['was_constrained']:
            constraint_type = 'minimum' if constrained_amount == min_amount else 'maximum'
            result['constraint_applied'] = constraint_type
            self.logger.debug(
                f"📊 Amount constrained: ${original_amount:.2f} -> ${constrained_amount:.2f} ({constraint_type})"
            )
        
        return result
    
    def _calculate_win_rate(self, position_history: List[Position]) -> float:
        """Calculate historical win rate"""
        if not position_history:
            return 0.5  # Default 50%
        
        winning_trades = sum(1 for p in position_history if p.pnl > 0)
        total_trades = len(position_history)
        
        return winning_trades / total_trades if total_trades > 0 else 0.5
    
    def _calculate_avg_win_loss_ratio(self, position_history: List[Position]) -> float:
        """Calculate average win/loss ratio"""
        if not position_history:
            return 1.5  # Default 1.5:1
        
        wins = [p.pnl for p in position_history if p.pnl > 0]
        losses = [abs(p.pnl) for p in position_history if p.pnl < 0]
        
        if not wins or not losses:
            return 1.5
        
        avg_win = sum(wins) / len(wins)
        avg_loss = sum(losses) / len(losses)
        
        return avg_win / avg_loss if avg_loss > 0 else 1.5
    
    def _create_error_result(self) -> Dict[str, float]:
        """Create error result structure"""
        return {
            'amount': 0.0,
            'base_amount': 0.0,
            'method': 'Error',
            'valid': False,
            'error': True
        }
    
    def validate_amount(self, amount: float, current_capital: float) -> Tuple[bool, str]:
        """Validate calculated amount against all constraints"""
        # Check minimum
        if amount < self.config.risk.min_position_size:
            return False, f"Amount ${amount:.2f} below minimum ${self.config.risk.min_position_size}"
        
        # Check maximum absolute
        if amount > self.config.risk.max_position_size:
            return False, f"Amount ${amount:.2f} above maximum ${self.config.risk.max_position_size}"
        
        # Check percentage of capital
        if current_capital > 0:
            percentage = (amount / current_capital) * 100
            if percentage > self.config.risk.max_position_percentage * self.config.risk.leverage:
                return False, f"Amount {percentage:.1f}% exceeds max {self.config.risk.max_position_percentage * self.config.risk.leverage}%"
        
        return True, "Valid"
    
    def get_amount_info(self, current_capital: float, position_history: List[Position]) -> Dict[str, any]:
        """Get comprehensive amount calculation info"""
        return {
            'config_amount': self.config.amount,
            'risk_per_trade': self.config.risk.risk_per_trade,
            'min_position_size': self.config.risk.min_position_size,
            'max_position_size': self.config.risk.max_position_size,
            'max_position_percentage': self.config.risk.max_position_percentage,
            'current_capital': current_capital,
            'optimal_strategy': self._detect_optimal_strategy(current_capital).value,
            'kelly_enabled': self.config.risk.use_kelly_criterion,
            'dca_enabled': self.config.dca.enabled,
            'historical_trades': len(position_history),
            'win_rate': self._calculate_win_rate(position_history),
            'win_loss_ratio': self._calculate_avg_win_loss_ratio(position_history)
        } 