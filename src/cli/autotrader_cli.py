#!/usr/bin/env python3
"""
AutoTrader CLI - Python-based command line interface
Handles complex operations that were previously in bot.sh
"""

import sys
import os
import argparse
import json
import subprocess
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.container_helper import container_helper


class AutoTraderCLI:
    """Main CLI class for AutoTrader operations"""
    
    def __init__(self):
        self.container_helper = container_helper
    
    def list_containers(self, format_type: str = "table") -> int:
        """List trading containers"""
        try:
            containers = self.container_helper.list_trading_containers()
            
            if not containers:
                print("📭 No trading bot containers found")
                print("")
                print("💡 Use './bot.sh start <symbol>' to create a new trading bot")
                return 0
            
            if format_type == "json":
                print(json.dumps(containers, indent=2))
                return 0
            
            # Table format
            print("📊 Trading Bot Containers")
            print("=" * 50)
            print(f"{'NAME':<15} {'STATUS':<12} {'CREATED':<20}")
            print("-" * 50)
            
            running_count = 0
            for container in containers:
                status_icon = "🟢" if container['running'] else "🔴"
                status_text = "Running" if container['running'] else "Stopped"
                
                print(f"{container['name']:<15} {status_icon} {status_text:<10} {container['created'][:19]}")
                
                if container['running']:
                    running_count += 1
            
            print("-" * 50)
            print(f"📈 Summary: {len(containers)} total, {running_count} running, {len(containers) - running_count} stopped")
            
            return 0
            
        except Exception as e:
            print(f"❌ Error listing containers: {e}")
            return 1
    
    def get_container_status(self, symbol: str, detailed: bool = False) -> int:
        """Get status of a specific container"""
        try:
            container_name = self.container_helper.find_container_by_symbol(symbol)
            
            if not container_name:
                print(f"❌ No container found for symbol: {symbol}")
                print("")
                print("💡 Available containers:")
                containers = self.container_helper.list_trading_containers()
                for container in containers[:5]:
                    status = "🟢" if container['running'] else "🔴"
                    print(f"   {status} {container['name']}")
                return 1
            
            status_info = self.container_helper.get_container_status(container_name)
            
            if not status_info:
                print(f"❌ Could not get status for container: {container_name}")
                return 1
            
            # Basic status
            status_emoji = "🟢" if status_info['running'] else "🔴"
            status_text = "Running" if status_info['running'] else "Stopped"
            
            print(f"📊 Container Status: {symbol}")
            print("=" * 40)
            print(f"Name: {container_name}")
            print(f"Status: {status_emoji} {status_text}")
            print(f"Image: {status_info['image']}")
            print(f"Created: {status_info['created']}")
            
            # Detailed status for running containers
            if detailed and status_info['running'] and 'cpu' in status_info:
                print("")
                print("📊 Performance Metrics:")
                print(f"CPU: {status_info['cpu']}")
                print(f"Memory: {status_info['memory']}")
                print(f"Network: {status_info['network']}")
            
            return 0
            
        except Exception as e:
            print(f"❌ Error getting container status: {e}")
            return 1
    
    def get_container_logs(self, symbol: str, lines: int = 50) -> int:
        """Get logs from a container"""
        try:
            container_name = self.container_helper.find_container_by_symbol(symbol)
            
            if not container_name:
                print(f"❌ No container found for symbol: {symbol}")
                return 1
            
            logs = self.container_helper.get_container_logs(container_name, lines)
            
            if not logs:
                print(f"❌ No logs found for container: {container_name}")
                return 1
            
            print(f"📋 Logs for {symbol} (container: {container_name}, last {lines} lines):")
            print("=" * 60)
            print(logs)
            
            return 0
            
        except Exception as e:
            print(f"❌ Error getting container logs: {e}")
            return 1
    
    def stop_container(self, symbol: str, force: bool = False) -> int:
        """Stop a container"""
        try:
            container_name = self.container_helper.find_container_by_symbol(symbol)
            
            if not container_name:
                print(f"❌ No container found for symbol: {symbol}")
                return 1
            
            if not force:
                response = input(f"⚠️ Stop container '{container_name}'? [y/N]: ")
                if response.lower() not in ['y', 'yes']:
                    print("❌ Operation cancelled")
                    return 1
            
            success, message = self.container_helper.stop_container(container_name)
            
            if success:
                print(f"✅ {message}")
                return 0
            else:
                print(f"❌ {message}")
                return 1
                
        except Exception as e:
            print(f"❌ Error stopping container: {e}")
            return 1
    
    def restart_container(self, symbol: str, force: bool = False) -> int:
        """Restart a container"""
        try:
            container_name = self.container_helper.find_container_by_symbol(symbol)
            
            if not container_name:
                print(f"❌ No container found for symbol: {symbol}")
                return 1
            
            if not force:
                response = input(f"⚠️ Restart container '{container_name}'? [y/N]: ")
                if response.lower() not in ['y', 'yes']:
                    print("❌ Operation cancelled")
                    return 1
            
            success, message = self.container_helper.restart_container(container_name)
            
            if success:
                print(f"✅ {message}")
                return 0
            else:
                print(f"❌ {message}")
                return 1
                
        except Exception as e:
            print(f"❌ Error restarting container: {e}")
            return 1
    
    def remove_container(self, symbol: str, force: bool = False) -> int:
        """Remove a container"""
        try:
            container_name = self.container_helper.find_container_by_symbol(symbol)
            
            if not container_name:
                print(f"❌ No container found for symbol: {symbol}")
                return 1
            
            if not force:
                print(f"⚠️ This will permanently delete container '{container_name}' and all its data!")
                response = input("Are you sure? Type 'DELETE' to confirm: ")
                if response != 'DELETE':
                    print("❌ Operation cancelled")
                    return 1
            
            success, message = self.container_helper.remove_container(container_name)
            
            if success:
                print(f"✅ {message}")
                return 0
            else:
                print(f"❌ {message}")
                return 1
                
        except Exception as e:
            print(f"❌ Error removing container: {e}")
            return 1


def main():
    """Main CLI entry point"""
    parser = argparse.ArgumentParser(
        description="AutoTrader CLI - Python-based operations",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # List command
    list_parser = subparsers.add_parser('list', help='List trading containers')
    list_parser.add_argument('--format', choices=['table', 'json'], default='table',
                           help='Output format')
    
    # Status command
    status_parser = subparsers.add_parser('status', help='Get container status')
    status_parser.add_argument('symbol', help='Symbol or container name')
    status_parser.add_argument('--detailed', action='store_true',
                             help='Show detailed performance metrics')
    
    # Logs command
    logs_parser = subparsers.add_parser('logs', help='Get container logs')
    logs_parser.add_argument('symbol', help='Symbol or container name')
    logs_parser.add_argument('--lines', type=int, default=50,
                           help='Number of lines to show (default: 50)')
    
    # Stop command
    stop_parser = subparsers.add_parser('stop', help='Stop container')
    stop_parser.add_argument('symbol', help='Symbol or container name')
    stop_parser.add_argument('--force', action='store_true',
                           help='Skip confirmation prompt')
    
    # Restart command
    restart_parser = subparsers.add_parser('restart', help='Restart container')
    restart_parser.add_argument('symbol', help='Symbol or container name')
    restart_parser.add_argument('--force', action='store_true',
                               help='Skip confirmation prompt')
    
    # Remove command
    remove_parser = subparsers.add_parser('remove', help='Remove container')
    remove_parser.add_argument('symbol', help='Symbol or container name')
    remove_parser.add_argument('--force', action='store_true',
                             help='Skip confirmation prompt')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return 1
    
    cli = AutoTraderCLI()
    
    # Route commands
    if args.command == 'list':
        return cli.list_containers(args.format)
    elif args.command == 'status':
        return cli.get_container_status(args.symbol, args.detailed)
    elif args.command == 'logs':
        return cli.get_container_logs(args.symbol, args.lines)
    elif args.command == 'stop':
        return cli.stop_container(args.symbol, args.force)
    elif args.command == 'restart':
        return cli.restart_container(args.symbol, args.force)
    elif args.command == 'remove':
        return cli.remove_container(args.symbol, args.force)
    else:
        print(f"❌ Unknown command: {args.command}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
