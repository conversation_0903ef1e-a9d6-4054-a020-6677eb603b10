#!/usr/bin/env python3
"""Demo script to test Bybit integration"""

import asyncio
import os
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent))

from src.infrastructure.config.config_manager import ConfigManager
from src.infrastructure.exchange.exchange_connector import ExchangeConnector


async def test_bybit_connection():
    """Test Bybit connection and basic operations"""
    print("🚀 Testing Bybit Integration")
    print("=" * 50)
    
    # Check environment variables
    api_key = os.getenv('BYBIT_API_KEY')
    api_secret = os.getenv('BYBIT_API_SECRET')
    
    if not api_key or not api_secret:
        print("⚠️  Missing Bybit API credentials!")
        print("Please set environment variables:")
        print("   export BYBIT_API_KEY='your_api_key'")
        print("   export BYBIT_API_SECRET='your_api_secret'")
        print("\n🧪 Running in demo mode (no real API calls)")
        return False
    
    try:
        # Load configuration
        print("📋 Loading configuration...")
        config_manager = ConfigManager('config.json')
        config = config_manager.load_config()
        
        print(f"✅ Config loaded: {config.symbol} on {config.exchange}")
        
        # Test credentials
        print("\n🔑 Testing API credentials...")
        credentials = config_manager.get_api_credentials()
        
        if credentials['api_key'] and credentials['api_secret']:
            print("✅ API credentials found")
        else:
            print("❌ API credentials missing")
            return False
        
        # Initialize exchange connector
        print("\n🔌 Connecting to Bybit...")
        exchange = ExchangeConnector(config, credentials)
        
        # Test connection
        connected = await exchange.connect()
        
        if connected:
            print("✅ Successfully connected to Bybit!")
            
            # Test basic operations
            print("\n📊 Testing basic operations...")
            
            # Fetch ticker
            ticker = await exchange.fetch_ticker(config.symbol)
            print(f"✅ Current price for {config.symbol}: ${ticker['last']:.4f}")
            
            # Fetch balance
            balance = await exchange.fetch_balance()
            print(f"✅ Account balance fetched")
            
            # Fetch market info
            market_info = await exchange.get_market_info(config.symbol)
            print(f"✅ Market info: {market_info.get('id', 'N/A')}")
            
            # Get trading fees
            fees = await exchange.get_trading_fees(config.symbol)
            print(f"✅ Trading fees: Maker {fees['maker']}, Taker {fees['taker']}")
            
            # Validate order parameters
            is_valid, message = exchange.validate_order_params(
                config.symbol, 0.001, ticker['last']
            )
            print(f"✅ Order validation: {message}")
            
            print("\n🎉 All Bybit tests passed!")
            
            # Close connection
            await exchange.close()
            print("✅ Connection closed")
            
            return True
            
        else:
            print("❌ Failed to connect to Bybit")
            return False
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False


async def demo_trading_flow():
    """Demo trading flow with Bybit"""
    print("\n" + "=" * 50)
    print("🎯 Demo Trading Flow")
    print("=" * 50)
    
    try:
        # Load config
        config_manager = ConfigManager('config.json')
        config = config_manager.load_config()
        credentials = config_manager.get_api_credentials()
        
        if not credentials['api_key']:
            print("⚠️  Skipping trading demo - no API credentials")
            return
        
        # Connect to exchange
        exchange = ExchangeConnector(config, credentials)
        await exchange.connect()
        
        # Get current price
        ticker = await exchange.fetch_ticker(config.symbol)
        current_price = ticker['last']
        
        print(f"📊 Current {config.symbol} price: ${current_price:.4f}")
        
        # Calculate position size
        position_size = config.amount / current_price
        print(f"💰 Position size for ${config.amount}: {position_size:.6f} {config.symbol.split('/')[0]}")
        
        # Simulate stop loss and take profit
        stop_loss = current_price * 0.98  # 2% below
        take_profit = current_price * 1.04  # 4% above
        
        print(f"🛡️  Stop Loss: ${stop_loss:.4f}")
        print(f"🎯 Take Profit: ${take_profit:.4f}")
        
        # Check if in test mode
        if config.use_test_mode:
            print("\n🧪 Running in TEST MODE - no real orders placed")
        else:
            print("\n⚠️  LIVE MODE - real orders would be placed!")
            print("    Set 'use_test_mode': true in config.json for testing")
        
        await exchange.close()
        print("✅ Demo completed")
        
    except Exception as e:
        print(f"❌ Demo error: {e}")


async def main():
    """Main demo function"""
    print("🎮 Bybit Trading Bot Demo")
    print("Testing connection and basic functionality")
    print("\n💡 Make sure to set your API credentials:")
    print("   export BYBIT_API_KEY='your_key'")
    print("   export BYBIT_API_SECRET='your_secret'")
    print("\n⚠️  Important: Enable 'use_test_mode' in config.json for safety!")
    
    # Test connection
    success = await test_bybit_connection()
    
    if success:
        # Demo trading flow
        await demo_trading_flow()
    
    print("\n" + "=" * 50)
    print("📖 Next Steps:")
    print("1. Configure your trading parameters in config.json")
    print("2. Set API credentials in environment variables")
    print("3. Enable test mode for safe testing")
    print("4. Run: python main.py")
    print("5. Or use CLI: python -m src.presentation.cli.cli_interface dashboard")
    print("\n🚀 Happy Trading with Bybit!")


if __name__ == '__main__':
    asyncio.run(main()) 