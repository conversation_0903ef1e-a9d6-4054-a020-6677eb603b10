name: <PERSON><PERSON> and Push AutoTrader Images

on:
  push:
    branches: [main, feat/tele-bot]
  workflow_dispatch:

env:
  REGISTRY: ghcr.io
  IMAGE_BASE: ${{ github.repository }}

jobs:
  build-telegram:
    name: Build Telegram Bot Image
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Extract metadata for Telegram Bot
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_BASE }}-telegram
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=raw,value=latest,enable={{is_default_branch}}
            type=sha,prefix={{branch}}-

      - name: Build and push Telegram Bot image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.telegram
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha,scope=telegram
          cache-to: type=gha,mode=max,scope=telegram

  build-trader:
    name: Build Trader Bot Image
    runs-on: ubuntu-latest
    permissions:
      packages: write
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Log in to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Extract metadata for Trader Bot
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_BASE }}-trader
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=raw,value=latest,enable={{is_default_branch}}
            type=sha,prefix={{branch}}-

      - name: Build and push Trader Bot image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.trader
          platforms: linux/amd64,linux/arm64
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha,scope=trader
          cache-to: type=gha,mode=max,scope=trader

  summary:
    name: Build Summary
    runs-on: ubuntu-latest
    needs: [build-telegram, build-trader]
    if: always()
    steps:
      - name: Report Build Status
        run: |
          echo "🎉 AutoTrader Build Complete!"
          echo "================================"
          echo "📱 Telegram Bot: ${{ needs.build-telegram.result }}"
          echo "🤖 Trader Bot: ${{ needs.build-trader.result }}"
          echo ""
          echo "📦 Images built:"
          echo "  • ghcr.io/${{ github.repository }}-telegram:latest"
          echo "  • ghcr.io/${{ github.repository }}-trader:latest"
          echo ""
          echo "🚀 Ready for deployment!"
