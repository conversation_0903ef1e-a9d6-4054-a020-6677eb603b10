name: Sync bot.sh to GitHub Gist

on:
  push:
    branches: [main, master]
    paths:
      - "bot.sh"
  workflow_dispatch:

jobs:
  sync-gist:
    name: Sync bot.sh to Gist
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Check if bot.sh exists
        run: |
          if [ ! -f "bot.sh" ]; then
            echo "❌ bot.sh file not found!"
            exit 1
          fi
          echo "✅ bot.sh file found"

      - name: Get file info
        run: |
          echo "📄 File: bot.sh"
          echo "📏 Size: $(wc -c < bot.sh) bytes"
          echo "📝 Lines: $(wc -l < bot.sh) lines"
          echo "🕒 Last modified: $(stat -c %y bot.sh 2>/dev/null || stat -f %Sm bot.sh)"

      - name: Update Gist
        env:
          GIST_TOKEN: ${{ secrets.GIST_TOKEN }}
          GIST_ID: "73593690940ff91015063f2b6f9366a3"
        run: |
          echo "🔄 Updating Gist..."

          # Check if GIST_TOKEN is set
          if [ -z "$GIST_TOKEN" ]; then
            echo "❌ GIST_TOKEN secret not found!"
            echo "📝 Please add GIST_TOKEN to your repository secrets"
            echo "   1. Go to Settings > Secrets and variables > Actions"
            echo "   2. Add new secret: GIST_TOKEN"
            echo "   3. Value: Your GitHub personal access token with 'gist' scope"
            exit 1
          fi

          # Read bot.sh content and escape for JSON
          BOT_CONTENT=$(cat bot.sh | jq -Rs .)

          # Create JSON payload
          JSON_PAYLOAD=$(cat <<EOF
          {
            "description": "AutoTrader Bot Script - Updated $(date -u +%Y-%m-%d\ %H:%M:%S\ UTC)",
            "files": {
              "autotrader.sh": {
                "content": $BOT_CONTENT
              }
            }
          }
          EOF
          )

          # Update the gist
          echo "📤 Uploading to gist..."
          RESPONSE=$(curl -s -X PATCH \
            -H "Authorization: token $GIST_TOKEN" \
            -H "Accept: application/vnd.github.v3+json" \
            -d "$JSON_PAYLOAD" \
            "https://api.github.com/gists/$GIST_ID")

          # Check if update was successful
          if echo "$RESPONSE" | jq -e '.id' > /dev/null 2>&1; then
            echo "✅ Gist updated successfully!"
            echo "🌐 Gist URL: $(echo "$RESPONSE" | jq -r '.html_url')"
            echo "📄 File URL: $(echo "$RESPONSE" | jq -r '.files.["autotrader.sh"].raw_url')"
            
            # Get commit info for better tracking
            echo ""
            echo "📋 Sync Details:"
            echo "   Commit: ${{ github.sha }}"
            echo "   Branch: ${{ github.ref_name }}"
            echo "   Author: ${{ github.actor }}"
            echo "   Message: ${{ github.event.head_commit.message }}"
          else
            echo "❌ Failed to update gist!"
            echo "📋 Response:"
            echo "$RESPONSE" | jq . || echo "$RESPONSE"
            exit 1
          fi

      - name: Verify Gist Content
        env:
          GIST_ID: "73593690940ff91015063f2b6f9366a3"
        run: |
          echo "🔍 Verifying gist content..."

          # Download the raw content from gist
          RAW_URL="https://gist.githubusercontent.com/hoangtrung99/$GIST_ID/raw/autotrader.sh"

          # Wait a moment for GitHub to process the update
          sleep 2

          if curl -fsSL "$RAW_URL" > /tmp/gist_content.sh; then
            echo "✅ Gist content downloaded"
            
            # Compare file sizes
            LOCAL_SIZE=$(wc -c < bot.sh)
            GIST_SIZE=$(wc -c < /tmp/gist_content.sh)
            
            echo "📏 Local file: $LOCAL_SIZE bytes"
            echo "📏 Gist file: $GIST_SIZE bytes"
            
            if [ "$LOCAL_SIZE" -eq "$GIST_SIZE" ]; then
              echo "✅ File sizes match - sync successful!"
            else
              echo "⚠️  File sizes differ - sync may need time to propagate"
            fi
          else
            echo "⚠️  Could not verify gist content (may take time to propagate)"
          fi

      - name: Create Summary
        run: |
          echo "## 🔄 Gist Sync Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "✅ **bot.sh** has been successfully synced to GitHub Gist" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 📋 Details" >> $GITHUB_STEP_SUMMARY
          echo "- **Gist ID:** \`73593690940ff91015063f2b6f9366a3\`" >> $GITHUB_STEP_SUMMARY
          echo "- **File Size:** $(wc -c < bot.sh) bytes" >> $GITHUB_STEP_SUMMARY
          echo "- **Lines:** $(wc -l < bot.sh) lines" >> $GITHUB_STEP_SUMMARY
          echo "- **Commit:** \`${{ github.sha }}\`" >> $GITHUB_STEP_SUMMARY
          echo "- **Updated:** $(date -u +%Y-%m-%d\ %H:%M:%S\ UTC)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 🔗 Links" >> $GITHUB_STEP_SUMMARY
          echo "- [📄 View Gist](https://gist.github.com/hoangtrung99/73593690940ff91015063f2b6f9366a3)" >> $GITHUB_STEP_SUMMARY
          echo "- [⬇️ Raw Download](https://gist.githubusercontent.com/hoangtrung99/73593690940ff91015063f2b6f9366a3/raw/autotrader.sh)" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "### 💡 Usage" >> $GITHUB_STEP_SUMMARY
          echo "Users can now update their local bot.sh with:" >> $GITHUB_STEP_SUMMARY
          echo "\`\`\`bash" >> $GITHUB_STEP_SUMMARY
          echo "./bot.sh upgrade" >> $GITHUB_STEP_SUMMARY
          echo "\`\`\`" >> $GITHUB_STEP_SUMMARY
