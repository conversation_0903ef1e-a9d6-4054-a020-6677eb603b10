#!/bin/bash

# 🚀 Trading Bot Quick Launcher
# Usage: ./run.sh [option] [flags]

print_header() {
    echo "🚀 Crypto Trading Bot - Quick Launcher"
    echo "======================================"
    echo ""
}

print_help() {
    print_header
    echo "📋 Usage: ./run.sh [command] [flags]"
    echo ""
    echo "🎯 Available Commands:"
    echo "  dashboard, d    📊 Launch interactive CLI dashboard"
    echo "  start, s        🤖 Start trading bot (default if no command)"
    echo "  test, t         🧪 Start in test mode (no real trading)"
    echo "  config, c       📋 Show configuration details (pretty formatted)"
    echo "  clear-logs, cl  🧹 Clear all log files (keep structure)"
    echo "  help, h         ❓ Show this help message"
    echo ""
    echo "🔧 Available Flags:"
    echo "  --debug         🐛 Enable debug logging"
    echo "  --config FILE   📄 Use custom config file (default: configs/config.json)"
    echo ""
    echo "💡 Examples:"
    echo "  ./run.sh d                                    # Quick CLI dashboard"
    echo "  ./run.sh d --debug                            # CLI dashboard with debug logs"
    echo "  ./run.sh d --config configs/config_test.json  # CLI dashboard with custom config"
    echo "  ./run.sh start --debug --config configs/config_test.json # Start with debug & custom config"
    echo "  ./run.sh test --config configs/config_test.json          # Safe test mode with custom config"
    echo "  ./run.sh config --config configs/config_test.json        # Show custom config details"
    echo ""
    echo "⚠️  Important:"
    echo "  - Always test with 'test' mode first!"
    echo "  - Check your config file before live trading"
    echo "  - Use Ctrl+C to stop the bot safely"
    echo ""
}

# Parse arguments
COMMAND=""
DEBUG_FLAG=""
CONFIG_FLAG=""
CONFIG_FILE=""

# Parse all arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        dashboard|d|start|s|test|t|config|c|clear-logs|cl|help|h|--help)
            if [ -z "$COMMAND" ]; then
                COMMAND="$1"
            else
                echo "❌ Multiple commands specified. Use only one."
                exit 1
            fi
            shift
            ;;
        --debug)
            DEBUG_FLAG="--debug"
            shift
            ;;
        --config)
            if [ -n "$2" ] && [[ $2 != --* ]]; then
                CONFIG_FLAG="--config"
                CONFIG_FILE="$2"
                shift 2
            else
                echo "❌ --config requires a file path"
                exit 1
            fi
            ;;
        *)
            echo "❌ Unknown option: $1"
            echo "💡 Run './run.sh help' to see available commands"
            exit 1
            ;;
    esac
done

# Show help if no command or help requested
if [ -z "$COMMAND" ] || [ "$COMMAND" = "help" ] || [ "$COMMAND" = "h" ] || [ "$COMMAND" = "--help" ]; then
    print_help
    exit 0
fi

print_header

# Activate virtual environment if it exists
if [ -d ".venv" ]; then
    echo "🔧 Activating virtual environment..."
    source .venv/bin/activate
elif [ -d "venv" ]; then
    echo "🔧 Activating virtual environment..."
    source venv/bin/activate
elif [ -d "path/to/venv" ]; then
    echo "🔧 Activating virtual environment..."
    source path/to/venv/bin/activate
else
    echo "⚠️  No virtual environment found, using system Python"
fi

# Show debug mode info
if [ -n "$DEBUG_FLAG" ]; then
    echo "🐛 Debug mode enabled - detailed logs will be shown"
fi

# Show config file info
if [ -n "$CONFIG_FILE" ]; then
    echo "📄 Using custom config file: $CONFIG_FILE"
fi

# Build command arguments
PYTHON_ARGS=""
if [ -n "$DEBUG_FLAG" ]; then
    PYTHON_ARGS="$PYTHON_ARGS $DEBUG_FLAG"
fi
if [ -n "$CONFIG_FLAG" ] && [ -n "$CONFIG_FILE" ]; then
    PYTHON_ARGS="$PYTHON_ARGS $CONFIG_FLAG $CONFIG_FILE"
fi

case "$COMMAND" in
    "dashboard"|"d")
        echo "📊 Starting CLI Dashboard..."
        python3 main.py --dashboard $PYTHON_ARGS
        ;;
    "start"|"s")
        echo "🤖 Starting Trading Bot..."
        echo "   → Live trading mode enabled!"
        python3 main.py --start $PYTHON_ARGS
        ;;
    "test"|"t")
        echo "🧪 Starting in Test Mode..."
        echo "   → Safe mode: No real trades will be executed"
        python3 main.py --start --test $PYTHON_ARGS
        ;;
    "config"|"c")
        echo "📋 Showing Configuration Details..."
        echo "   → Pretty formatted config display"
        python3 main.py --show-config $PYTHON_ARGS
        ;;
    "clear-logs"|"cl")
        echo "🧹 Clearing Log Files..."
        echo "   → Clearing all log files but keeping structure"

        # Clear log files
        echo "# Trading orchestrator log cleared on $(date '+%Y-%m-%d %H:%M:%S') - Ready for new logs" > logs/tradingorchestrator.log
        echo "# DCA strategy log cleared on $(date '+%Y-%m-%d %H:%M:%S') - Ready for new logs" > logs/dcastrategy.log
        echo "# Error log cleared on $(date '+%Y-%m-%d %H:%M:%S') - Ready for new logs" > logs/errors.log
        echo "" > logs/trades.jsonl

        # Remove old rotated logs
        rm -f logs/*.log.* logs/*.log.2025-* 2>/dev/null || true

        echo "✅ Log files cleared successfully!"
        echo "📊 Current logs directory size: $(du -sh logs/ | cut -f1)"
        ;;
    *)
        echo "❌ Unknown command: $COMMAND"
        echo ""
        echo "💡 Run './run.sh help' to see available commands"
        exit 1
        ;;
esac 