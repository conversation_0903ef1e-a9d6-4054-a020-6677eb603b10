#!/usr/bin/env python3
"""Simple test bot to debug Telegram integration"""

import sys
import os
import asyncio
import logging

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from telegram import Update, ForceReply
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
from telegram.constants import ParseMode

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

class SimpleTestBot:
    def __init__(self, token, chat_id):
        self.token = token
        self.chat_id = int(chat_id)
        self.application = None
        self.wizard_state = {}
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command"""
        print(f"🔍 DEBUG: /start command from user {update.effective_user.id}")
        await update.message.reply_text("🎉 Simple test bot started!")
    
    async def createbot_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /createbot command"""
        user_id = update.effective_user.id
        print(f"🔍 DEBUG: /createbot command from user {user_id}")

        # Set wizard state
        self.wizard_state[user_id] = 'createbot_symbol'
        print(f"🔍 DEBUG: Set wizard state for user {user_id}: createbot_symbol")
        print(f"🔍 DEBUG: Current wizard_state dict: {self.wizard_state}")

        await update.message.reply_text(
            "🔑 **Sử dụng profile:** main\n\n"
            "Nhập symbol để trade (ví dụ: BTC, ETH, HYPER):",
            reply_markup=ForceReply(selective=True),
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages"""
        user_id = update.effective_user.id
        text = update.message.text

        print(f"🔍 DEBUG: Message from user {user_id}: '{text}'")
        print(f"🔍 DEBUG: Current wizard_state dict: {self.wizard_state}")
        print(f"🔍 DEBUG: User {user_id} wizard state: {self.wizard_state.get(user_id)}")
        print(f"🔍 DEBUG: User {user_id} in wizard_state: {user_id in self.wizard_state}")

        if user_id in self.wizard_state and self.wizard_state[user_id] == 'createbot_symbol':
            print(f"🔍 DEBUG: Processing symbol: {text}")
            await update.message.reply_text(
                f"✅ Symbol: `{text}`\n\n"
                "Nhập số tiền để trade (USD):",
                reply_markup=ForceReply(selective=True),
                parse_mode=ParseMode.MARKDOWN
            )
            self.wizard_state[user_id] = 'createbot_amount'
            print(f"🔍 DEBUG: Updated wizard state to: createbot_amount")
        elif user_id in self.wizard_state and self.wizard_state[user_id] == 'createbot_amount':
            print(f"🔍 DEBUG: Processing amount: {text}")
            await update.message.reply_text(
                f"✅ Amount: `${text}`\n\n"
                "Bot creation completed!",
                parse_mode=ParseMode.MARKDOWN
            )
            del self.wizard_state[user_id]
            print(f"🔍 DEBUG: Cleared wizard state")
        else:
            print(f"🔍 DEBUG: No wizard state found, sending help message")
            await update.message.reply_text("Sử dụng `/help` để xem danh sách lệnh có sẵn")
    
    def start(self):
        """Start the bot"""
        print("🤖 Starting simple test bot...")
        
        # Create application
        self.application = Application.builder().token(self.token).build()
        
        # Add handlers
        self.application.add_handler(CommandHandler("start", self.start_command))
        self.application.add_handler(CommandHandler("createbot", self.createbot_command))
        self.application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_message))
        
        print("🚀 Starting polling...")
        self.application.run_polling()

if __name__ == "__main__":
    token = os.getenv('TELEGRAM_BOT_TOKEN')
    chat_id = os.getenv('TELEGRAM_CHAT_ID')

    if not token or not chat_id:
        print("❌ Error: TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID environment variables are required")
        print("💡 Set them with:")
        print("   export TELEGRAM_BOT_TOKEN='your_bot_token'")
        print("   export TELEGRAM_CHAT_ID='your_chat_id'")
        print("💡 Or create a .env file (see .env.example)")
        sys.exit(1)

    bot = SimpleTestBot(token, int(chat_id))
    bot.start()
