#!/usr/bin/env python3
"""Auto test Telegram bot functionality"""

import sys
import os
import asyncio
import logging

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.infrastructure.telegram.telegram_api_client import TelegramAPIClient

async def test_telegram_integration():
    """Test Telegram integration automatically"""
    # Get credentials from environment variables
    token = os.getenv('TELEGRAM_BOT_TOKEN')
    chat_id = os.getenv('TELEGRAM_CHAT_ID')

    if not token or not chat_id:
        print("❌ Error: TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID environment variables are required")
        print("💡 Set them with:")
        print("   export TELEGRAM_BOT_TOKEN='your_bot_token'")
        print("   export TELEGRAM_CHAT_ID='your_chat_id'")
        print("💡 Or create a .env file (see .env.example)")
        return

    chat_id = int(chat_id)
    
    print("🧪 Testing Telegram API integration...")
    
    try:
        client = TelegramAPIClient(token)
        
        # Test 1: Send a simple message
        print("📤 Test 1: Sending simple message...")
        await client.send_message(chat_id, "🧪 **Auto Test Started**\n\nTesting bot functionality...")
        print("✅ Simple message sent")
        
        # Test 2: Send createbot wizard start
        print("📤 Test 2: Sending createbot wizard start...")
        await client.send_message(
            chat_id, 
            "🔑 **Sử dụng profile:** main\n\n"
            "Nhập symbol để trade (ví dụ: BTC, ETH, HYPER):"
        )
        print("✅ Createbot wizard message sent")
        
        # Test 3: Simulate user input
        print("📤 Test 3: Simulating user input 'eth'...")
        await asyncio.sleep(2)  # Wait a bit
        
        # Test 4: Check if we can get bot info
        print("📤 Test 4: Getting bot info...")
        bot_info = await client.get_me()
        print(f"✅ Bot info: {bot_info}")
        
        print("✅ All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_telegram_integration())
