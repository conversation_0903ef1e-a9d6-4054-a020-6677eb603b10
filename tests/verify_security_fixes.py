#!/usr/bin/env python3
"""Verify that all security fixes have been applied correctly"""

import os
import sys
import re
from pathlib import Path

def check_file_for_hardcoded_credentials(file_path):
    """Check if file contains hardcoded credentials"""
    hardcoded_patterns = [
        r'8087395947:AAEnYZT1T_d-BjBocISIF1KyhrSbvUuvfHE',  # Bot token
        r'1631630468',  # Chat ID (except in test files with placeholders)
    ]
    
    issues = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        for i, line in enumerate(content.split('\n'), 1):
            for pattern in hardcoded_patterns:
                if re.search(pattern, line):
                    # Allow placeholders in test files
                    if 'test' in file_path.name.lower() and ('123456789' in line or 'placeholder' in line.lower()):
                        continue
                    issues.append(f"Line {i}: {line.strip()}")
                    
    except Exception as e:
        issues.append(f"Error reading file: {e}")
    
    return issues

def check_file_uses_env_vars(file_path):
    """Check if file properly uses environment variables"""
    required_patterns = [
        r'os\.getenv\([\'"]TELEGRAM_BOT_TOKEN[\'"]',
        r'os\.getenv\([\'"]TELEGRAM_CHAT_ID[\'"]',
    ]
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # Check if file imports os
        if 'import os' not in content:
            return ["Missing 'import os' statement"]
        
        # Check if file uses environment variables
        uses_env_vars = any(re.search(pattern, content) for pattern in required_patterns)
        
        if not uses_env_vars:
            return ["Does not use environment variables for credentials"]
            
    except Exception as e:
        return [f"Error reading file: {e}"]
    
    return []

def main():
    """Main verification function"""
    print("🔐 Verifying Security Fixes")
    print("=" * 50)
    
    # Files that should be fixed
    files_to_check = [
        'run_telegram_central.py',
        'test_auto_telegram.py', 
        'auto_test_createbot.py',
        'test_bot_receive.py',
        'verify_bot.py',
        'test_simple_bot.py',
        'fix_wizard_test.py',
        'test_bot.py',
        'test_handlers.py',
    ]
    
    all_passed = True
    
    for file_name in files_to_check:
        file_path = Path(file_name)
        
        if not file_path.exists():
            print(f"⚠️  {file_name}: File not found")
            continue
            
        print(f"\n📁 Checking {file_name}...")
        
        # Check for hardcoded credentials
        hardcoded_issues = check_file_for_hardcoded_credentials(file_path)
        if hardcoded_issues:
            print(f"❌ Hardcoded credentials found:")
            for issue in hardcoded_issues:
                print(f"   {issue}")
            all_passed = False
        else:
            print("✅ No hardcoded credentials found")
        
        # Check for proper environment variable usage (skip test_handlers.py as it's a mock)
        if file_name != 'test_handlers.py':
            env_var_issues = check_file_uses_env_vars(file_path)
            if env_var_issues:
                print(f"❌ Environment variable issues:")
                for issue in env_var_issues:
                    print(f"   {issue}")
                all_passed = False
            else:
                print("✅ Properly uses environment variables")
    
    # Check if .env.example exists
    print(f"\n📁 Checking .env.example...")
    if Path('.env.example').exists():
        print("✅ .env.example template exists")
    else:
        print("❌ .env.example template missing")
        all_passed = False
    
    # Check if .env is in .gitignore
    print(f"\n📁 Checking .gitignore...")
    try:
        with open('.gitignore', 'r') as f:
            gitignore_content = f.read()
        
        if '.env' in gitignore_content:
            print("✅ .env is properly ignored in .gitignore")
        else:
            print("❌ .env not found in .gitignore")
            all_passed = False
    except FileNotFoundError:
        print("❌ .gitignore file not found")
        all_passed = False
    
    # Check if README_SECURITY.md exists
    print(f"\n📁 Checking README_SECURITY.md...")
    if Path('README_SECURITY.md').exists():
        print("✅ Security documentation exists")
    else:
        print("❌ README_SECURITY.md missing")
        all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 All security fixes verified successfully!")
        print("✅ No hardcoded credentials found")
        print("✅ All files use environment variables")
        print("✅ Security documentation complete")
        return 0
    else:
        print("❌ Security issues found - please fix the issues above")
        return 1

if __name__ == "__main__":
    sys.exit(main())
