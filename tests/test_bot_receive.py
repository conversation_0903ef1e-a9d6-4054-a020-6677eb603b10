#!/usr/bin/env python3
"""Test if bot receives messages"""

import asyncio
import aiohttp
import json
import time
import os

async def test_bot_receive():
    """Test if bot receives messages"""
    # Get credentials from environment variables
    token = os.getenv('TELEGRAM_BOT_TOKEN')
    chat_id = os.getenv('TELEGRAM_CHAT_ID')

    if not token or not chat_id:
        print("❌ Error: TELEGRAM_BOT_TOKEN and TELEGRAM_CHAT_ID environment variables are required")
        print("💡 Set them with:")
        print("   export TELEGRAM_BOT_TOKEN='your_bot_token'")
        print("   export TELEGRAM_CHAT_ID='your_chat_id'")
        print("💡 Or create a .env file (see .env.example)")
        return

    chat_id = int(chat_id)
    
    base_url = f"https://api.telegram.org/bot{token}"
    
    async with aiohttp.ClientSession() as session:
        # Step 1: Clear any pending updates
        print("🔄 Step 1: Clearing pending updates...")
        async with session.get(f"{base_url}/getUpdates?offset=-1") as response:
            data = await response.json()
            print(f"Clear result: {data.get('ok', False)}")
        
        # Step 2: Send a test message
        print("📤 Step 2: Sending test message...")
        payload = {
            'chat_id': chat_id,
            'text': '🧪 Test message for bot to receive'
        }
        async with session.post(f"{base_url}/sendMessage", json=payload) as response:
            data = await response.json()
            print(f"Send result: {data.get('ok', False)}")
            if data.get('ok'):
                message_id = data['result']['message_id']
                print(f"Message ID: {message_id}")
        
        # Step 3: Wait and get updates
        print("⏳ Step 3: Waiting 3 seconds...")
        await asyncio.sleep(3)
        
        print("📥 Step 4: Getting updates...")
        async with session.get(f"{base_url}/getUpdates") as response:
            data = await response.json()
            print(f"Updates result: {data.get('ok', False)}")
            if data.get('ok') and data.get('result'):
                print(f"Found {len(data['result'])} updates:")
                for update in data['result']:
                    print(f"  Update {update['update_id']}: {update}")
            else:
                print("No updates found")
        
        # Step 5: Send /createbot command
        print("📤 Step 5: Sending /createbot command...")
        payload = {
            'chat_id': chat_id,
            'text': '/createbot'
        }
        async with session.post(f"{base_url}/sendMessage", json=payload) as response:
            data = await response.json()
            print(f"Send /createbot result: {data.get('ok', False)}")
        
        # Step 6: Wait and get updates again
        print("⏳ Step 6: Waiting 3 seconds...")
        await asyncio.sleep(3)
        
        print("📥 Step 7: Getting updates after /createbot...")
        async with session.get(f"{base_url}/getUpdates") as response:
            data = await response.json()
            print(f"Updates result: {data.get('ok', False)}")
            if data.get('ok') and data.get('result'):
                print(f"Found {len(data['result'])} updates:")
                for update in data['result']:
                    print(f"  Update {update['update_id']}: {update}")
            else:
                print("No updates found")

if __name__ == "__main__":
    asyncio.run(test_bot_receive())
