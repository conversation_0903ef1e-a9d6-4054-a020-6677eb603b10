#!/bin/bash
echo "🧪 Testing Telegram bot..."

# Check if container is running
if docker ps | grep -q autotrader-telegram; then
    echo "✅ Container is running"
else
    echo "❌ Container is not running"
    echo "🚀 Starting container..."
    docker run -d --name autotrader-telegram --env-file .env -v $(pwd):/app autotrader-telegram
    sleep 3
fi

# Show recent logs
echo "📋 Recent logs:"
docker logs autotrader-telegram --tail 20

# Check if bot is responding
echo ""
echo "🔍 Checking bot status..."
if docker logs autotrader-telegram --tail 10 | grep -q "Bot started successfully"; then
    echo "✅ <PERSON><PERSON> is running successfully"
elif docker logs autotrader-telegram --tail 10 | grep -q "ERROR"; then
    echo "❌ <PERSON><PERSON> has errors"
    echo "📋 Error logs:"
    docker logs autotrader-telegram --tail 10 | grep ERROR
else
    echo "⚠️ Bot status unclear, check logs above"
fi

echo ""
echo "💬 Test the bot by sending /start to your Telegram bot"
echo "📱 Bot commands to test:"
echo "   /start - Welcome message"
echo "   /help - Help message"
echo "   /list - List active bots"
echo "   /createbot - Create new bot wizard"
