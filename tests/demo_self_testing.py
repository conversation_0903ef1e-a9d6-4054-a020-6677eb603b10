#!/usr/bin/env python3
"""
Demo script showcasing Telegram bot self-testing capabilities
This demonstrates how the bot can test itself automatically
"""

import asyncio
import os
import sys

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from src.infrastructure.telegram.telegram_api_client import TelegramAPIClient

async def demo_self_testing():
    """Demonstrate the self-testing system"""
    
    # Get credentials
    token = os.environ.get('TELEGRAM_BOT_TOKEN')
    chat_id = os.environ.get('TELEGRAM_CHAT_ID')
    
    if not token or not chat_id:
        print("❌ Missing TELEGRAM_BOT_TOKEN or TELEGRAM_CHAT_ID")
        print("💡 Set environment variables first:")
        print("   export TELEGRAM_BOT_TOKEN='your_token'")
        print("   export TELEGRAM_CHAT_ID='your_chat_id'")
        return 1
    
    client = TelegramAPIClient(token)
    
    print("🎬 Telegram Bot Self-Testing Demo")
    print("=" * 40)
    print(f"Bot Token: {token[:15]}...")
    print(f"Chat ID: {chat_id}")
    print()
    
    # Introduction
    await client.send_message(
        chat_id,
        "🎬 **Self-Testing Demo Started**\n\n"
        "Chào mừng đến với demo hệ thống tự test của bot!\n\n"
        "Bot sẽ demonstrate 3 chức năng chính:\n"
        "• `/selftest` - Tự test toàn bộ hệ thống\n"
        "• `/simulate` - Simulate messages\n"
        "• `/testcmd` - Test commands cụ thể"
    )
    
    print("📋 Demo started - check your Telegram chat!")
    await asyncio.sleep(3)
    
    # Demo 1: Comprehensive Self-Test
    print("\n🧪 Demo 1: Comprehensive Self-Test (/selftest)")
    await client.send_message(
        chat_id,
        "🧪 **Demo 1: Comprehensive Self-Test**\n\n"
        "Bot sẽ tự động test tất cả commands chính.\n"
        "Quan sát bot tự gửi và xử lý các commands..."
    )
    await asyncio.sleep(2)
    
    await client.send_message(chat_id, "/selftest")
    print("✅ /selftest command sent - bot is now testing itself!")
    await asyncio.sleep(20)  # Wait for selftest to complete
    
    # Demo 2: Message Simulation
    print("\n🎭 Demo 2: Message Simulation (/simulate)")
    await client.send_message(
        chat_id,
        "🎭 **Demo 2: Message Simulation**\n\n"
        "Bot có thể simulate bất kỳ message nào và xử lý như user thật gửi."
    )
    await asyncio.sleep(2)
    
    simulation_examples = [
        ("/help", "Test help command"),
        ("/createbot", "Test bot creation wizard"),
        ("Hello Bot!", "Test text message processing"),
        ("/list", "Test bot listing")
    ]
    
    for message, description in simulation_examples:
        await client.send_message(
            chat_id,
            f"🔄 **Simulating**: `{message}`\n{description}"
        )
        await asyncio.sleep(1)
        
        await client.send_message(chat_id, f"/simulate {message}")
        print(f"✅ Simulated: {message}")
        await asyncio.sleep(4)
    
    # Demo 3: Command Testing
    print("\n🧪 Demo 3: Command Testing (/testcmd)")
    await client.send_message(
        chat_id,
        "🧪 **Demo 3: Command Testing**\n\n"
        "Bot có thể test từng command cụ thể với interface thân thiện."
    )
    await asyncio.sleep(2)
    
    command_examples = [
        "/help",
        "/addcreds", 
        "/listcreds",
        "/status"
    ]
    
    for cmd in command_examples:
        await client.send_message(
            chat_id,
            f"🎯 **Testing Command**: `{cmd}`"
        )
        await asyncio.sleep(1)
        
        await client.send_message(chat_id, f"/testcmd {cmd}")
        print(f"✅ Tested command: {cmd}")
        await asyncio.sleep(4)
    
    # Demo 4: Error Handling
    print("\n❌ Demo 4: Error Handling")
    await client.send_message(
        chat_id,
        "❌ **Demo 4: Error Handling**\n\n"
        "Bot cũng xử lý errors gracefully trong self-testing."
    )
    await asyncio.sleep(2)
    
    error_examples = [
        ("/testcmd", "Missing command argument"),
        ("/simulate", "Missing message argument"),
        ("/testcmd /nonexistent", "Non-existent command")
    ]
    
    for error_cmd, description in error_examples:
        await client.send_message(
            chat_id,
            f"⚠️ **Error Test**: `{error_cmd}`\n{description}"
        )
        await asyncio.sleep(1)
        
        await client.send_message(chat_id, error_cmd)
        print(f"✅ Error test: {error_cmd}")
        await asyncio.sleep(3)
    
    # Summary
    await client.send_message(
        chat_id,
        "🎉 **Self-Testing Demo Complete!**\n\n"
        "📊 **What you've seen:**\n"
        "• Bot tự test toàn bộ hệ thống với `/selftest`\n"
        "• Bot simulate messages với `/simulate`\n"
        "• Bot test commands với `/testcmd`\n"
        "• Bot xử lý errors gracefully\n\n"
        "🔧 **Key Benefits:**\n"
        "• Hoàn toàn tự động\n"
        "• Không cần user interaction\n"
        "• Comprehensive testing\n"
        "• Detailed logging\n"
        "• Perfect for CI/CD\n\n"
        "📖 **Documentation**: `docs/TELEGRAM_SELF_TESTING.md`"
    )
    
    print("\n🎉 Demo completed successfully!")
    print("📱 Check your Telegram chat for full demo results")
    print("📖 See docs/TELEGRAM_SELF_TESTING.md for detailed documentation")
    
    return 0

async def quick_demo():
    """Quick demo of key features"""
    
    token = os.environ.get('TELEGRAM_BOT_TOKEN')
    chat_id = os.environ.get('TELEGRAM_CHAT_ID')
    
    if not token or not chat_id:
        print("❌ Missing credentials")
        return 1
    
    client = TelegramAPIClient(token)
    
    print("⚡ Quick Self-Testing Demo")
    print("=" * 25)
    
    await client.send_message(
        chat_id,
        "⚡ **Quick Self-Testing Demo**\n\n"
        "3 commands để tự test bot:\n\n"
        "🤖 `/selftest` - Test everything\n"
        "🎭 `/simulate /help` - Simulate message\n"
        "🧪 `/testcmd /list` - Test command\n\n"
        "Try them now! 👆"
    )
    
    print("✅ Quick demo sent to Telegram!")
    return 0

def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Telegram Bot Self-Testing Demo')
    parser.add_argument('--quick', action='store_true', help='Run quick demo only')
    args = parser.parse_args()
    
    try:
        if args.quick:
            return asyncio.run(quick_demo())
        else:
            return asyncio.run(demo_self_testing())
    except KeyboardInterrupt:
        print("\n🛑 Demo interrupted")
        return 1
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
