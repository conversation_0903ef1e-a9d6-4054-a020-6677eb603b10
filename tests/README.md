# Tests

This directory contains active test files and testing utilities for the project.

## Test Files

### Python Test Files
- `demo_integration.py` - Integration demo tests
- `simple_telegram_test.py` - Simple Telegram bot tests
- `test_auto_telegram.py` - Automated Telegram tests
- `test_bot.py` - Bot functionality tests
- `test_bot_receive.py` - Bot message receiving tests
- `test_docker_bot.py` - Docker bot tests
- `test_handlers.py` - <PERSON><PERSON> tests
- `test_session_manager.py` - Session manager tests
- `test_simple_bot.py` - Simple bot tests
- `test_telegram_basic.py` - Basic Telegram functionality tests
- `test_telegram_bot.sh` - Telegram bot shell tests
- `test_telegram_commands.py` - Telegram command tests
- `test_telegram_createbot.py` - Telegram bot creation tests
- `test_self_testing_system.py` - Test bot's self-testing capabilities (/selftest, /simulate, /testcmd)
- `verify_bot.py` - Bot verification script
- `verify_security_fixes.py` - Security verification script

### Shell Test Scripts
- `test-docker.sh` - Docker testing script
- `test-list.sh` - Test listing script
- `test_bot.sh` - Bot testing script
- `test_createbot.sh` - Bot creation testing script

## Running Tests

To run the tests, use the appropriate test runner for each file type:

```bash
# Python tests
python tests/test_filename.py

# Shell script tests
bash tests/test_script.sh
```

## Test Categories

- **Unit Tests**: Individual component testing
- **Integration Tests**: Component interaction testing
- **Bot Tests**: Telegram bot functionality testing
- **Self-Testing**: Bot's built-in self-testing system (`/selftest`, `/simulate`, `/testcmd`)
- **Docker Tests**: Container and deployment testing
- **Verification Tests**: System verification and validation

## Self-Testing System

The Telegram bot includes a comprehensive self-testing system:

### Commands:
- `/selftest` - Run complete automated test suite
- `/simulate <message>` - Simulate any message/command
- `/testcmd <command>` - Test specific commands

### Usage:
```bash
# Test the self-testing system
python tests/test_self_testing_system.py

# Or test manually in Telegram:
/selftest
/simulate /help
/testcmd /createbot
```

See `docs/TELEGRAM_SELF_TESTING.md` for detailed documentation.
