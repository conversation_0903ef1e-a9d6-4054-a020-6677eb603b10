#!/bin/bash
# Test script for Telegram bot

set -e

echo "🧪 Testing Telegram Bot Docker Image"
echo "======================================"

# Check if environment variables exist
if [[ -z "$TELEGRAM_BOT_TOKEN" ]]; then
    echo "❌ TELEGRAM_BOT_TOKEN not set"
    echo "💡 Please set environment variable: export TELEGRAM_BOT_TOKEN='your_token'"
    echo "💡 Or create a .env file (see .env.example)"
    exit 1
fi

if [[ -z "$TELEGRAM_CHAT_ID" ]]; then
    echo "❌ TELEGRAM_CHAT_ID not set"
    echo "💡 Please set environment variable: export TELEGRAM_CHAT_ID='your_chat_id'"
    echo "💡 Or create a .env file (see .env.example)"
    exit 1
fi

echo "✅ Environment variables found"
echo "   Bot Token: ${TELEGRAM_BOT_TOKEN:0:10}..."
echo "   Chat ID: $TELEGRAM_CHAT_ID"
echo ""

# Test 1: Validate command
echo "🧪 Test 1: Testing validate-createbot command"
echo "-------------------------------------------"
if docker run --rm \
    -e TELEGRAM_BOT_TOKEN="$TELEGRAM_BOT_TOKEN" \
    -e TELEGRAM_CHAT_ID="$TELEGRAM_CHAT_ID" \
    autotrader-telegram:test \
    python3 run_telegram_app.py validate-createbot; then
    echo "✅ Validate command successful"
else
    echo "⚠️ Validate command showed expected warnings"
fi
echo ""

# Test 2: List credentials
echo "🧪 Test 2: Testing list-credentials command"
echo "----------------------------------------"
docker run --rm \
    -e TELEGRAM_BOT_TOKEN="$TELEGRAM_BOT_TOKEN" \
    -e TELEGRAM_CHAT_ID="$TELEGRAM_CHAT_ID" \
    autotrader-telegram:test \
    python3 run_telegram_app.py list-credentials
echo ""

# Test 3: Start bot for 5 seconds
echo "🧪 Test 3: Starting Telegram bot for 5 seconds"
echo "--------------------------------------------"
echo "⚠️ This will start the actual bot - you can test commands!"
echo "   Try sending /start or /help to your bot"
echo ""
read -p "Press Enter to start bot for 5 seconds (Ctrl+C to skip): " -t 5

# Start bot in background and kill after 5 seconds
echo "🚀 Starting bot..."
container_id=$(docker run -d \
    -e TELEGRAM_BOT_TOKEN="$TELEGRAM_BOT_TOKEN" \
    -e TELEGRAM_CHAT_ID="$TELEGRAM_CHAT_ID" \
    autotrader-telegram:test)

echo "   Container ID: ${container_id:0:12}..."
echo "   Bot is running! Send commands to test..."
echo ""

# Wait 5 seconds
for i in {5..1}; do
    echo "   Stopping in $i seconds..."
    sleep 1
done

# Stop container
echo "🛑 Stopping bot..."
docker stop $container_id > /dev/null 2>&1 || true

# Show logs
echo ""
echo "📋 Bot logs (last 10 lines):"
echo "----------------------------"
docker logs --tail 10 $container_id 2>/dev/null || echo "No logs available"

# Cleanup
docker rm $container_id > /dev/null 2>&1 || true

echo ""
echo "🎉 Test completed!"
echo "💡 If bot started successfully, you can now deploy it permanently." 