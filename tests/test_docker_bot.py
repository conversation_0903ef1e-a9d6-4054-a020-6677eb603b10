#!/usr/bin/env python3
"""
Test script for Docker-based Telegram bot
"""

import asyncio
import os
import sys
import time

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.infrastructure.telegram.telegram_api_client import TelegramAPIClient

async def test_docker_bot():
    """Test Docker-based Telegram bot"""
    
    # Get credentials from environment
    token = os.environ.get('TELEGRAM_BOT_TOKEN')
    chat_id = os.environ.get('TELEGRAM_CHAT_ID')
    
    if not token or not chat_id:
        print("❌ Missing TELEGRAM_BOT_TOKEN or TELEGRAM_CHAT_ID")
        return
    
    client = TelegramAPIClient(token)
    
    print("🐳 Testing Docker-based Telegram Bot")
    print("=" * 50)
    
    # Test commands specifically for Docker environment
    commands_to_test = [
        "/start",
        "/help",
        "/listcreds",
        "/showcreds test_profile",
        "/listconfigs",
        "/showconfig config",
        "/subscribe",
        "/testnotify trade",
        "/testnotify position",
        "/testnotify error",
        "/testnotify profit"
    ]
    
    for i, command in enumerate(commands_to_test, 1):
        print(f"\n📤 [{i}/{len(commands_to_test)}] Sending: {command}")
        try:
            await client.send_message(chat_id, command)
            print("✅ Sent successfully")
            # Wait between commands to avoid rate limiting
            await asyncio.sleep(2)
        except Exception as e:
            print(f"❌ Error sending {command}: {e}")
    
    print("\n" + "=" * 50)
    print("✅ Docker bot test completed!")
    print("📱 Check your Telegram chat for responses")
    print("🐳 Container logs: docker logs autotrader-telegram")

if __name__ == "__main__":
    asyncio.run(test_docker_bot())
